# ProxyManager 配置文件模板
# 此文件包含应用的默认配置，敏感信息通过环境变量设置

# ================================
# 🚀 服务器配置
# ================================
server:
  port: 8080                    # 服务端口 (可通过 PROXY_MANAGER_SERVER_PORT 覆盖)
  mode: debug                   # 运行模式: debug, release (可通过 PROXY_MANAGER_SERVER_MODE 覆盖)
  read_timeout: 30s             # 读取超时
  write_timeout: 30s            # 写入超时

# ================================
# 🗄️ Redis 配置
# ================================
redis:
  addr: "localhost:6379"        # Redis地址 (可通过 PROXY_MANAGER_REDIS_ADDR 覆盖)
  password: ""                  # Redis密码 (通过 PROXY_MANAGER_REDIS_PASSWORD 设置)
  db: 0                         # Redis数据库 (可通过 PROXY_MANAGER_REDIS_DB 覆盖)
  pool_size: 10                 # 连接池大小
  min_idle_conns: 5             # 最小空闲连接数

# ================================
# 🐘 PostgreSQL 配置
# ================================
postgres:
  host: localhost               # 数据库主机 (可通过 PROXY_MANAGER_POSTGRES_HOST 覆盖)
  port: 5432                    # 数据库端口 (可通过 PROXY_MANAGER_POSTGRES_PORT 覆盖)
  user: postgres                # 数据库用户 (可通过 PROXY_MANAGER_POSTGRES_USER 覆盖)
  password: ""                  # 数据库密码 (必须通过 PROXY_MANAGER_POSTGRES_PASSWORD 设置)
  database: proxy_manager       # 数据库名称 (可通过 PROXY_MANAGER_POSTGRES_DATABASE 覆盖)
  ssl_mode: disable             # SSL模式 (可通过 PROXY_MANAGER_POSTGRES_SSL_MODE 覆盖)
  max_open_conns: 25            # 最大打开连接数
  max_idle_conns: 5             # 最大空闲连接数
  conn_max_lifetime: 5m         # 连接最大生存时间
  conn_max_idle_time: 5m        # 连接最大空闲时间

# ================================
# 🔄 数据同步配置
# ================================
data_sync:
  sync_interval: 30s            # 同步间隔
  batch_size: 100               # 批处理大小
  enable_realtime: true         # 启用实时同步
  conflict_policy: latest_wins  # 冲突解决策略

# ================================
# 🌐 代理配置
# ================================
proxy:
  health_check_interval: 86400s    # 健康检查间隔
  max_failures: 3               # 最大失败次数
  timeout: 10s                  # 超时时间
  max_retries: 3                # 最大重试次数
  strategies:                   # 负载均衡策略
    - round_robin
    - least_used
    - random
    - weighted

# ================================
# ⚙️ 任务配置
# ================================
task:
  max_concurrent: 100           # 最大并发任务数
  queue_size: 1000              # 队列大小
  retry_attempts: 3             # 重试次数
  retry_delay: 5s               # 重试延迟

# ================================
# 🔐 认证配置 (敏感信息通过环境变量设置)
# ================================
auth:
  # JWT密钥 - 必须通过 PROXY_MANAGER_JWT_SECRET 环境变量设置
  # 使用 scripts/generate_jwt_secret.go 生成安全密钥
  jwt_secret: ""                # 从环境变量加载，不要在此设置
  token_expiry: 24h             # Token过期时间
  refresh_token_expiry: 168h    # 刷新Token过期时间 (7天)

  # Super API密钥 - 必须通过 PROXY_MANAGER_SUPER_API_KEY 环境变量设置
  super_api_key: ""             # 从环境变量加载，不要在此设置

# ================================
# 📊 监控配置
# ================================
monitoring:
  enabled: true                 # 启用监控
  metrics_path: "/metrics"      # 指标路径
  prometheus_enabled: true      # 启用Prometheus

# ================================
# 📝 日志配置
# ================================
logging:
  level: "info"                 # 日志级别: debug, info, warn, error (可通过 PROXY_MANAGER_LOG_LEVEL 覆盖)
  format: "json"                # 日志格式: json, text (可通过 PROXY_MANAGER_LOG_FORMAT 覆盖)
  output: "stdout"              # 日志输出: stdout, file (可通过 PROXY_MANAGER_LOG_OUTPUT 覆盖)
  file_path: "logs/app.log"     # 日志文件路径

# ================================
# 🔄 代理采集器配置
# ================================
collector:
  enabled: true                 # 启用代理采集器
  collect_interval: 60m         # 采集间隔 (可通过 PROXY_MANAGER_COLLECTOR_INTERVAL 覆盖)
  sources_file: "freeProxy/sources.json"  # 代理源配置文件
  concurrent_workers: 1000       # 并发工作者数量（高并发优化）
  request_timeout: 30s          # HTTP请求超时时间
  verification_timeout: 10s     # 代理验证超时时间
  test_url: "http://httpbin.org/ip"  # 测试URL
  max_retries: 2                # 最大重试次数
  retry_delay: 5s               # 重试延迟
  save_raw_data: true           # 保存原始数据
  raw_data_path: "data/raw_proxies"  # 原始数据保存路径
  min_quality_score: 0.3        # 最低质量分数 (0.0-1.0)
  enable_geo_location: true     # 启用地理位置检测

  # 大规模验证优化配置
  batch_size: 1000              # 批处理大小
  cache_enabled: true           # 启用缓存机制
  cache_file_path: "data/proxy_cache"  # 缓存文件路径
  cache_valid_duration: 168h     # 缓存有效期