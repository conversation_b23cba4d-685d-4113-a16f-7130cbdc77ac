package config

import (
	"fmt"
	"time"

	"github.com/spf13/viper"
)

// Config 配置结构
type Config struct {
	Server     ServerConfig     `mapstructure:"server"`
	Redis      RedisConfig      `mapstructure:"redis"`
	Postgres   PostgresConfig   `mapstructure:"postgres"`
	DataSync   DataSyncConfig   `mapstructure:"data_sync"`
	Proxy      ProxyConfig      `mapstructure:"proxy"`
	Task       TaskConfig       `mapstructure:"task"`
	Auth       AuthConfig       `mapstructure:"auth"`
	Monitoring MonitoringConfig `mapstructure:"monitoring"`
	Logging    LoggingConfig    `mapstructure:"logging"`
	Collector  CollectorConfig  `mapstructure:"collector"`
}

// ServerConfig 服务器配置
type ServerConfig struct {
	Port         int           `mapstructure:"port"`
	Mode         string        `mapstructure:"mode"`
	ReadTimeout  time.Duration `mapstructure:"read_timeout"`
	WriteTimeout time.Duration `mapstructure:"write_timeout"`
}

// RedisConfig Redis配置
type RedisConfig struct {
	Addr         string `mapstructure:"addr"`
	Password     string `mapstructure:"password"`
	DB           int    `mapstructure:"db"`
	PoolSize     int    `mapstructure:"pool_size"`
	MinIdleConns int    `mapstructure:"min_idle_conns"`
}

// PostgresConfig PostgreSQL配置
type PostgresConfig struct {
	Host            string        `mapstructure:"host"`
	Port            int           `mapstructure:"port"`
	User            string        `mapstructure:"user"`
	Password        string        `mapstructure:"password"`
	Database        string        `mapstructure:"database"`
	SSLMode         string        `mapstructure:"ssl_mode"`
	MaxOpenConns    int           `mapstructure:"max_open_conns"`
	MaxIdleConns    int           `mapstructure:"max_idle_conns"`
	ConnMaxLifetime time.Duration `mapstructure:"conn_max_lifetime"`
	ConnMaxIdleTime time.Duration `mapstructure:"conn_max_idle_time"`
}

// DataSyncConfig 数据同步配置
type DataSyncConfig struct {
	SyncInterval   time.Duration `mapstructure:"sync_interval"`
	BatchSize      int           `mapstructure:"batch_size"`
	EnableRealtime bool          `mapstructure:"enable_realtime"`
	ConflictPolicy string        `mapstructure:"conflict_policy"`
}

// ProxyConfig 代理配置
type ProxyConfig struct {
	HealthCheckInterval time.Duration `mapstructure:"health_check_interval"`
	MaxFailures         int           `mapstructure:"max_failures"`
	Timeout             time.Duration `mapstructure:"timeout"`
	MaxRetries          int           `mapstructure:"max_retries"`
	Strategies          []string      `mapstructure:"strategies"`
}

// TaskConfig 任务配置
type TaskConfig struct {
	MaxConcurrent int           `mapstructure:"max_concurrent"`
	QueueSize     int           `mapstructure:"queue_size"`
	RetryAttempts int           `mapstructure:"retry_attempts"`
	RetryDelay    time.Duration `mapstructure:"retry_delay"`
}

// AuthConfig 认证配置
type AuthConfig struct {
	JWTSecret          string        `mapstructure:"jwt_secret"`
	TokenExpiry        time.Duration `mapstructure:"token_expiry"`
	RefreshTokenExpiry time.Duration `mapstructure:"refresh_token_expiry"`
	SuperApiKey        string        `mapstructure:"super_api_key"`
}

// MonitoringConfig 监控配置
type MonitoringConfig struct {
	Enabled           bool   `mapstructure:"enabled"`
	MetricsPath       string `mapstructure:"metrics_path"`
	PrometheusEnabled bool   `mapstructure:"prometheus_enabled"`
}

// LoggingConfig 日志配置
type LoggingConfig struct {
	Level    string `mapstructure:"level"`
	Format   string `mapstructure:"format"`
	Output   string `mapstructure:"output"`
	FilePath string `mapstructure:"file_path"`
}

// CollectorConfig 代理采集器配置
type CollectorConfig struct {
	Enabled             bool          `mapstructure:"enabled"`
	CollectInterval     time.Duration `mapstructure:"collect_interval"`
	SourcesFile         string        `mapstructure:"sources_file"`
	ConcurrentWorkers   int           `mapstructure:"concurrent_workers"`
	RequestTimeout      time.Duration `mapstructure:"request_timeout"`
	VerificationTimeout time.Duration `mapstructure:"verification_timeout"`
	TestURL             string        `mapstructure:"test_url"`
	MaxRetries          int           `mapstructure:"max_retries"`
	RetryDelay          time.Duration `mapstructure:"retry_delay"`
	SaveRawData         bool          `mapstructure:"save_raw_data"`
	RawDataPath         string        `mapstructure:"raw_data_path"`
	MinQualityScore     float64       `mapstructure:"min_quality_score"`
	EnableGeoLocation   bool          `mapstructure:"enable_geo_location"`

	// 大规模验证优化配置
	BatchSize          int           `mapstructure:"batch_size"`           // 批处理大小
	CacheEnabled       bool          `mapstructure:"cache_enabled"`        // 启用缓存
	CacheFilePath      string        `mapstructure:"cache_file_path"`      // 缓存文件路径
	CacheValidDuration time.Duration `mapstructure:"cache_valid_duration"` // 缓存有效期
}

// Load 加载配置
func Load(configPath string) (*Config, error) {
	// 设置配置文件
	viper.SetConfigFile(configPath)
	viper.SetConfigType("yaml")
	viper.AutomaticEnv()

	// 设置环境变量前缀
	viper.SetEnvPrefix("PROXY_MANAGER")

	// 绑定所有环境变量映射
	bindEnvironmentVariables()

	// 尝试读取配置文件，如果失败则使用默认配置
	if err := viper.ReadInConfig(); err != nil {
		// 配置文件不存在或无法读取时，使用默认配置
		config := GetDefaultConfig()
		applyEnvironmentOverrides(config)
		if err := validateConfig(config); err != nil {
			return nil, fmt.Errorf("configuration validation failed: %w", err)
		}
		return config, nil
	}

	var config Config
	if err := viper.Unmarshal(&config); err != nil {
		return nil, fmt.Errorf("failed to unmarshal config: %w", err)
	}

	// 应用环境变量覆盖
	applyEnvironmentOverrides(&config)

	// 验证配置
	if err := validateConfig(&config); err != nil {
		return nil, fmt.Errorf("configuration validation failed: %w", err)
	}

	return &config, nil
}

// bindEnvironmentVariables 绑定所有环境变量映射
func bindEnvironmentVariables() {
	// 服务器配置
	viper.BindEnv("server.port", "PROXY_MANAGER_SERVER_PORT")
	viper.BindEnv("server.mode", "PROXY_MANAGER_SERVER_MODE")

	// PostgreSQL配置
	viper.BindEnv("postgres.host", "PROXY_MANAGER_POSTGRES_HOST")
	viper.BindEnv("postgres.port", "PROXY_MANAGER_POSTGRES_PORT")
	viper.BindEnv("postgres.user", "PROXY_MANAGER_POSTGRES_USER")
	viper.BindEnv("postgres.password", "PROXY_MANAGER_POSTGRES_PASSWORD")
	viper.BindEnv("postgres.database", "PROXY_MANAGER_POSTGRES_DATABASE")
	viper.BindEnv("postgres.ssl_mode", "PROXY_MANAGER_POSTGRES_SSL_MODE")

	// Redis配置
	viper.BindEnv("redis.addr", "PROXY_MANAGER_REDIS_ADDR")
	viper.BindEnv("redis.password", "PROXY_MANAGER_REDIS_PASSWORD")
	viper.BindEnv("redis.db", "PROXY_MANAGER_REDIS_DB")

	// 认证配置
	viper.BindEnv("auth.jwt_secret", "PROXY_MANAGER_JWT_SECRET")
	viper.BindEnv("auth.super_api_key", "PROXY_MANAGER_SUPER_API_KEY")

	// 日志配置
	viper.BindEnv("logging.level", "PROXY_MANAGER_LOG_LEVEL")
	viper.BindEnv("logging.format", "PROXY_MANAGER_LOG_FORMAT")
	viper.BindEnv("logging.output", "PROXY_MANAGER_LOG_OUTPUT")

	// 采集器配置
	viper.BindEnv("collector.enabled", "PROXY_MANAGER_COLLECTOR_ENABLED")
	viper.BindEnv("collector.collect_interval", "PROXY_MANAGER_COLLECTOR_INTERVAL")
	viper.BindEnv("collector.concurrent_workers", "PROXY_MANAGER_COLLECTOR_WORKERS")
	viper.BindEnv("collector.min_quality_score", "PROXY_MANAGER_COLLECTOR_MIN_QUALITY")
}

// applyEnvironmentOverrides 应用环境变量覆盖
func applyEnvironmentOverrides(config *Config) {
	// 服务器配置
	if port := viper.GetInt("server.port"); port != 0 {
		config.Server.Port = port
	}
	if mode := viper.GetString("server.mode"); mode != "" {
		config.Server.Mode = mode
	}

	// PostgreSQL配置
	if host := viper.GetString("postgres.host"); host != "" {
		config.Postgres.Host = host
	}
	if port := viper.GetInt("postgres.port"); port != 0 {
		config.Postgres.Port = port
	}
	if user := viper.GetString("postgres.user"); user != "" {
		config.Postgres.User = user
	}
	if password := viper.GetString("postgres.password"); password != "" {
		config.Postgres.Password = password
	}
	if database := viper.GetString("postgres.database"); database != "" {
		config.Postgres.Database = database
	}
	if sslMode := viper.GetString("postgres.ssl_mode"); sslMode != "" {
		config.Postgres.SSLMode = sslMode
	}

	// Redis配置
	if addr := viper.GetString("redis.addr"); addr != "" {
		config.Redis.Addr = addr
	}
	if password := viper.GetString("redis.password"); password != "" {
		config.Redis.Password = password
	}
	if db := viper.GetInt("redis.db"); db != 0 {
		config.Redis.DB = db
	}

	// 认证配置
	if jwtSecret := viper.GetString("auth.jwt_secret"); jwtSecret != "" {
		config.Auth.JWTSecret = jwtSecret
	}
	if superApiKey := viper.GetString("auth.super_api_key"); superApiKey != "" {
		config.Auth.SuperApiKey = superApiKey
	}

	// 日志配置
	if level := viper.GetString("logging.level"); level != "" {
		config.Logging.Level = level
	}
	if format := viper.GetString("logging.format"); format != "" {
		config.Logging.Format = format
	}
	if output := viper.GetString("logging.output"); output != "" {
		config.Logging.Output = output
	}

	// 采集器配置
	if enabled := viper.GetBool("collector.enabled"); viper.IsSet("collector.enabled") {
		config.Collector.Enabled = enabled
	}
	if interval := viper.GetDuration("collector.collect_interval"); interval != 0 {
		config.Collector.CollectInterval = interval
	}
	if workers := viper.GetInt("collector.concurrent_workers"); workers != 0 {
		config.Collector.ConcurrentWorkers = workers
	}
	if minQuality := viper.GetFloat64("collector.min_quality_score"); minQuality != 0 {
		config.Collector.MinQualityScore = minQuality
	}
}

// GetDefaultConfig 获取默认配置
func GetDefaultConfig() *Config {
	return &Config{
		Server: ServerConfig{
			Port:         8080,
			Mode:         "debug",
			ReadTimeout:  30 * time.Second,
			WriteTimeout: 30 * time.Second,
		},
		Redis: RedisConfig{
			Addr:         "localhost:6379",
			Password:     "",
			DB:           0,
			PoolSize:     10,
			MinIdleConns: 5,
		},
		Postgres: PostgresConfig{
			Host:            "localhost",
			Port:            5432,
			User:            "postgres",
			Password:        "",
			Database:        "proxy_manager",
			SSLMode:         "disable",
			MaxOpenConns:    25,
			MaxIdleConns:    5,
			ConnMaxLifetime: 5 * time.Minute,
			ConnMaxIdleTime: 5 * time.Minute,
		},
		DataSync: DataSyncConfig{
			SyncInterval:   30 * time.Second,
			BatchSize:      100,
			EnableRealtime: true,
			ConflictPolicy: "latest_wins",
		},
		Proxy: ProxyConfig{
			HealthCheckInterval: 30 * time.Second,
			MaxFailures:         3,
			Timeout:             10 * time.Second,
			MaxRetries:          3,
			Strategies:          []string{"round_robin", "least_used", "random", "weighted"},
		},
		Task: TaskConfig{
			MaxConcurrent: 100,
			QueueSize:     1000,
			RetryAttempts: 3,
			RetryDelay:    5 * time.Second,
		},
		Auth: AuthConfig{
			JWTSecret:          "", // 必须从环境变量设置
			TokenExpiry:        24 * time.Hour,
			RefreshTokenExpiry: 168 * time.Hour, // 7 days
			SuperApiKey:        "",              // 必须从环境变量设置
		},
		Monitoring: MonitoringConfig{
			Enabled:           true,
			MetricsPath:       "/metrics",
			PrometheusEnabled: true,
		},
		Logging: LoggingConfig{
			Level:    "info",
			Format:   "json",
			Output:   "stdout",
			FilePath: "logs/app.log",
		},
		Collector: CollectorConfig{
			Enabled:             true,
			CollectInterval:     60 * time.Minute, // 每小时采集一次
			SourcesFile:         "freeProxy/sources.json",
			ConcurrentWorkers:   500, // 增加并发数以充分利用多核CPU
			RequestTimeout:      10 * time.Second,
			VerificationTimeout: 10 * time.Second,
			TestURL:             "http://httpbin.org/ip",
			MaxRetries:          3,
			RetryDelay:          5 * time.Second,
			SaveRawData:         true,
			RawDataPath:         "data/raw_proxies",
			MinQualityScore:     0.3, // 最低质量分数
			EnableGeoLocation:   true,

			// 大规模验证优化默认配置
			BatchSize:          1000,               // 每批处理1000个代理
			CacheEnabled:       true,               // 启用缓存
			CacheFilePath:      "data/proxy_cache", // 缓存文件路径
			CacheValidDuration: 24 * time.Hour,     // 缓存24小时有效
		},
	}
}

// validateConfig 验证配置
func validateConfig(config *Config) error {
	// 验证服务器配置
	if config.Server.Port <= 0 || config.Server.Port > 65535 {
		return fmt.Errorf("invalid server port: %d", config.Server.Port)
	}

	// 验证PostgreSQL配置
	if config.Postgres.Host == "" {
		return fmt.Errorf("postgres host is required")
	}
	if config.Postgres.Port <= 0 || config.Postgres.Port > 65535 {
		return fmt.Errorf("invalid postgres port: %d", config.Postgres.Port)
	}
	if config.Postgres.User == "" {
		return fmt.Errorf("postgres user is required")
	}
	if config.Postgres.Database == "" {
		return fmt.Errorf("postgres database is required")
	}

	// 验证Redis配置
	if config.Redis.Addr == "" {
		return fmt.Errorf("redis address is required")
	}

	// 验证认证配置 - 增强安全性
	if err := validateAuthConfig(&config.Auth); err != nil {
		return fmt.Errorf("auth config validation failed: %w", err)
	}

	return nil
}

// validateAuthConfig 验证认证配置
func validateAuthConfig(auth *AuthConfig) error {
	// 检查JWT密钥
	if auth.JWTSecret == "" {
		return fmt.Errorf("JWT secret is required")
	}

	// 禁止使用默认密钥
	if auth.JWTSecret == "your-super-secret-jwt-key-change-in-production" {
		return fmt.Errorf("default JWT secret detected - this is a serious security vulnerability. Please set PROXY_MANAGER_JWT_SECRET environment variable with a secure random key")
	}

	// 验证密钥强度
	if len(auth.JWTSecret) < 32 {
		return fmt.Errorf("JWT secret must be at least 32 characters long for security")
	}

	// 检查密钥复杂性
	if !isSecureJWTSecret(auth.JWTSecret) {
		return fmt.Errorf("JWT secret is too weak - must contain uppercase, lowercase, numbers and special characters")
	}

	// 验证Super API Key（如果设置）
	if auth.SuperApiKey != "" && len(auth.SuperApiKey) < 32 {
		return fmt.Errorf("super API key must be at least 32 characters long")
	}

	// 验证token过期时间
	if auth.TokenExpiry <= 0 {
		return fmt.Errorf("token expiry must be positive")
	}

	if auth.TokenExpiry > 24*7*time.Hour { // 最长7天
		return fmt.Errorf("token expiry too long - maximum 7 days for security")
	}

	return nil
}

// isSecureJWTSecret 检查JWT密钥是否足够安全
func isSecureJWTSecret(secret string) bool {
	if len(secret) < 32 {
		return false
	}

	hasUpper := false
	hasLower := false
	hasDigit := false
	hasSpecial := false

	for _, char := range secret {
		switch {
		case char >= 'A' && char <= 'Z':
			hasUpper = true
		case char >= 'a' && char <= 'z':
			hasLower = true
		case char >= '0' && char <= '9':
			hasDigit = true
		case char >= 32 && char <= 126: // 可打印ASCII字符
			if !((char >= 'A' && char <= 'Z') || (char >= 'a' && char <= 'z') || (char >= '0' && char <= '9')) {
				hasSpecial = true
			}
		}
	}

	return hasUpper && hasLower && hasDigit && hasSpecial
}
