# 代理采集器使用指南

## 概述

代理采集器是 ProxyManager 系统的核心组件之一，负责自动从各种免费代理源采集、验证和管理代理服务器。

## 功能特性

- 🔄 **自动采集**: 定期从配置的代理源获取最新代理列表
- ✅ **智能验证**: 并发验证代理可用性、响应时间和匿名级别
- 📊 **质量评估**: 基于多维度指标计算代理质量分数
- 🗄️ **数据库集成**: 自动存储验证通过的代理到 PostgreSQL
- 🔍 **去重处理**: 避免重复添加相同的代理服务器
- 📈 **监控指标**: 集成 Prometheus 监控和统计
- 🌐 **地理位置**: 支持代理地理位置信息检测
- 🎛️ **API管理**: 提供完整的 REST API 进行管理

## 配置说明

### 基础配置

在 `config/config.yaml` 中配置采集器：

```yaml
collector:
  enabled: true                 # 启用代理采集器
  collect_interval: 60m         # 采集间隔 (30m-24h)
  sources_file: "freeProxy/sources.json"  # 代理源配置文件
  concurrent_workers: 500        # 并发工作者数量 (1-1000)
  request_timeout: 30s          # HTTP请求超时时间
  verification_timeout: 10s     # 代理验证超时时间
  test_url: "http://httpbin.org/ip"  # 测试URL
  max_retries: 3                # 最大重试次数
  retry_delay: 5s               # 重试延迟
  save_raw_data: true           # 保存原始数据
  raw_data_path: "data/raw_proxies"  # 原始数据保存路径
  min_quality_score: 0.3        # 最低质量分数 (0.0-1.0)
  enable_geo_location: true     # 启用地理位置检测
```

### 环境变量覆盖

可以通过环境变量覆盖配置：

```bash
# 启用/禁用采集器
export PROXY_MANAGER_COLLECTOR_ENABLED=true

# 设置采集间隔
export PROXY_MANAGER_COLLECTOR_INTERVAL=30m

# 设置并发工作者数量
export PROXY_MANAGER_COLLECTOR_WORKERS=30

# 设置最低质量分数
export PROXY_MANAGER_COLLECTOR_MIN_QUALITY=0.5
```

### 代理源配置

编辑 `freeProxy/sources.json` 文件添加或修改代理源：

```json
{
  "http": [
    "https://raw.githubusercontent.com/example/proxy-list/main/http.txt",
    "https://api.proxyscrape.com/v2/?request=get&protocol=http"
  ],
  "https": [
    "https://raw.githubusercontent.com/example/proxy-list/main/https.txt"
  ],
  "socks5": [
    "https://raw.githubusercontent.com/example/proxy-list/main/socks5.txt"
  ]
}
```

## API 接口

### 获取采集器状态

```bash
GET /api/v1/collector/status
```

响应示例：
```json
{
  "success": true,
  "data": {
    "running": true,
    "last_collection": "2024-01-15T10:30:00Z",
    "total_sources": 45,
    "success_sources": 42,
    "total_proxies": 1250,
    "valid_proxies": 387,
    "duplicate_count": 125,
    "success_rate": 30.96,
    "collection_duration": "2m15s"
  }
}
```

### 手动触发采集

```bash
POST /api/v1/collector/collect
Authorization: Bearer <token>
```

### 获取采集统计

```bash
GET /api/v1/collector/stats
```

### 获取采集历史

```bash
GET /api/v1/collector/history?limit=10
```

### 启动/停止采集器

```bash
# 启动采集器
POST /api/v1/collector/start
Authorization: Bearer <admin-token>

# 停止采集器
POST /api/v1/collector/stop
Authorization: Bearer <admin-token>
```

## 质量评估机制

采集器使用多维度指标评估代理质量：

### 评分维度

1. **响应时间 (40%)**
   - ≤1秒: 0.4分
   - ≤3秒: 0.3分
   - ≤5秒: 0.2分
   - >5秒: 0.1分

2. **匿名级别 (30%)**
   - Elite: 0.3分
   - Anonymous: 0.25分
   - Transparent: 0.15分
   - Unknown: 0.1分

3. **代理类型 (20%)**
   - HTTPS: 0.2分
   - SOCKS5: 0.18分
   - HTTP: 0.15分

4. **基础可用性 (10%)**
   - 验证通过: 0.1分

### 匿名级别检测

- **Transparent**: 真实IP包含代理IP
- **Anonymous**: 真实IP是单个IP且不是代理IP
- **Elite**: 多个IP或复杂格式
- **Unknown**: 无法确定

## 监控和日志

### Prometheus 指标

- `proxy_collector_proxies_collected_total`: 采集的代理总数
- `proxy_collector_proxies_valid_total`: 验证通过的代理数
- `proxy_collector_proxies_duplicate_total`: 重复代理数
- `proxy_collector_errors_total`: 采集错误总数
- `proxy_collector_source_errors_total`: 源错误总数
- `proxy_collector_collection_duration_seconds`: 采集耗时

### 日志级别

- **INFO**: 采集开始/完成、统计信息
- **DEBUG**: 详细的采集过程、代理验证结果
- **ERROR**: 采集失败、验证错误、数据库错误
- **WARN**: 源访问失败、配置问题

## 故障排除

### 常见问题

1. **采集器无法启动**
   - 检查配置文件格式
   - 验证数据库连接
   - 确认 Redis 连接

2. **代理验证失败率高**
   - 调整验证超时时间
   - 检查网络连接
   - 更新测试URL

3. **采集速度慢**
   - 增加并发工作者数量
   - 优化网络配置
   - 检查代理源响应速度

4. **内存使用过高**
   - 减少并发工作者数量
   - 调整采集间隔
   - 清理旧的原始数据文件

### 调试命令

```bash
# 检查采集器状态
curl -H "Authorization: Bearer $TOKEN" \
  http://localhost:8080/api/v1/collector/status

# 查看日志
tail -f logs/app.log | grep collector

# 手动触发采集
curl -X POST -H "Authorization: Bearer $ADMIN_TOKEN" \
  http://localhost:8080/api/v1/collector/collect
```

## 最佳实践

### 性能优化

1. **合理设置并发数**: 根据服务器性能调整 `concurrent_workers`
2. **优化采集间隔**: 平衡数据新鲜度和系统负载
3. **定期清理数据**: 清理过期的原始数据文件
4. **监控资源使用**: 关注CPU、内存和网络使用情况

### 安全考虑

1. **API访问控制**: 管理操作需要管理员权限
2. **网络安全**: 采集过程中注意网络安全
3. **数据隐私**: 妥善处理代理认证信息
4. **日志安全**: 避免在日志中记录敏感信息

### 维护建议

1. **定期更新源**: 保持代理源列表的时效性
2. **监控质量**: 关注代理质量趋势
3. **性能调优**: 根据使用情况调整配置
4. **备份配置**: 定期备份配置文件和数据

## 扩展开发

### 添加新的代理源

1. 在 `freeProxy/sources.json` 中添加新源
2. 确保源返回标准格式的代理列表
3. 测试源的稳定性和质量

### 自定义验证逻辑

1. 修改 `internal/collector/verifier.go`
2. 实现自定义的验证方法
3. 更新质量评分算法

### 集成地理位置服务

1. 实现 `UpdateProxyGeoLocation` 方法
2. 集成第三方地理位置API
3. 更新数据库模型以存储位置信息

## 支持

如有问题或建议，请：

1. 查看日志文件获取详细错误信息
2. 检查配置文件和环境变量
3. 参考API文档进行调试
4. 提交Issue到项目仓库
