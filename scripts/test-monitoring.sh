#!/bin/bash

# ProxyManager 监控系统测试脚本
# 此脚本用于测试 Prometheus 和 Grafana 监控系统是否正常工作

set -e

echo "🧪 测试 ProxyManager 监控系统..."

# 颜色定义
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
NC='\033[0m' # No Color

# 测试函数
test_service() {
    local service_name=$1
    local url=$2
    local expected_status=${3:-200}
    
    echo -n "测试 $service_name... "
    
    if curl -s -o /dev/null -w "%{http_code}" "$url" | grep -q "$expected_status"; then
        echo -e "${GREEN}✅ 通过${NC}"
        return 0
    else
        echo -e "${RED}❌ 失败${NC}"
        return 1
    fi
}

# 测试指标端点
test_metrics() {
    local service_name=$1
    local url=$2
    local expected_metrics=$3
    
    echo -n "测试 $service_name 指标... "
    
    response=$(curl -s "$url")
    if echo "$response" | grep -q "$expected_metrics"; then
        echo -e "${GREEN}✅ 通过${NC}"
        return 0
    else
        echo -e "${RED}❌ 失败 (未找到预期指标: $expected_metrics)${NC}"
        return 1
    fi
}

# 等待服务启动
wait_for_service() {
    local service_name=$1
    local url=$2
    local max_attempts=30
    local attempt=1
    
    echo "等待 $service_name 启动..."
    
    while [ $attempt -le $max_attempts ]; do
        if curl -s "$url" > /dev/null 2>&1; then
            echo -e "${GREEN}✅ $service_name 已启动${NC}"
            return 0
        fi
        
        echo -n "."
        sleep 2
        attempt=$((attempt + 1))
    done
    
    echo -e "${RED}❌ $service_name 启动超时${NC}"
    return 1
}

# 检查 Docker 服务状态
check_docker_services() {
    echo "📋 检查 Docker 服务状态..."
    
    services=("redis" "redis-exporter" "prometheus" "grafana")
    
    for service in "${services[@]}"; do
        if docker-compose ps "$service" | grep -q "Up"; then
            echo -e "  ${GREEN}✅${NC} $service"
        else
            echo -e "  ${RED}❌${NC} $service (未运行)"
            return 1
        fi
    done
}

# 主测试流程
main() {
    echo "🚀 开始监控系统测试..."
    echo ""
    
    # 检查 Docker 服务
    if ! check_docker_services; then
        echo -e "${RED}❌ 部分 Docker 服务未运行，请先启动监控服务${NC}"
        echo "运行: docker-compose up -d redis-exporter prometheus grafana"
        exit 1
    fi
    
    echo ""
    echo "🔍 测试服务可访问性..."
    
    # 等待服务启动
    wait_for_service "Redis Exporter" "http://localhost:9121/metrics"
    wait_for_service "Prometheus" "http://localhost:9090/-/healthy"
    wait_for_service "Grafana" "http://localhost:3000/api/health"
    
    echo ""
    echo "🧪 执行功能测试..."
    
    # 测试基本服务
    test_service "Redis Exporter" "http://localhost:9121/metrics"
    test_service "Prometheus Health" "http://localhost:9090/-/healthy"
    test_service "Prometheus Ready" "http://localhost:9090/-/ready"
    test_service "Grafana Health" "http://localhost:3000/api/health"
    
    echo ""
    echo "📊 测试指标收集..."
    
    # 测试指标端点
    test_metrics "Redis Exporter" "http://localhost:9121/metrics" "redis_up"
    
    # 测试 Prometheus 目标
    echo -n "测试 Prometheus 目标状态... "
    targets_response=$(curl -s "http://localhost:9090/api/v1/targets")
    if echo "$targets_response" | grep -q '"health":"up"'; then
        echo -e "${GREEN}✅ 通过${NC}"
    else
        echo -e "${YELLOW}⚠️  部分目标可能未就绪${NC}"
    fi
    
    # 测试 Grafana 数据源
    echo -n "测试 Grafana 数据源... "
    if curl -s -u admin:admin "http://localhost:3000/api/datasources" | grep -q "Prometheus"; then
        echo -e "${GREEN}✅ 通过${NC}"
    else
        echo -e "${RED}❌ 失败${NC}"
    fi
    
    echo ""
    echo "🎯 测试 ProxyManager 应用指标 (如果运行)..."
    
    # 测试 ProxyManager 指标 (可选)
    if curl -s "http://localhost:8080/health" > /dev/null 2>&1; then
        test_service "ProxyManager Health" "http://localhost:8080/health"
        test_metrics "ProxyManager Metrics" "http://localhost:8080/metrics" "proxy_collector"
    else
        echo -e "${YELLOW}⚠️  ProxyManager 应用未运行，跳过应用指标测试${NC}"
    fi
    
    echo ""
    echo "📈 生成测试报告..."
    
    # 获取一些基本指标
    echo "=== 监控系统状态 ==="
    echo "Prometheus 版本: $(curl -s http://localhost:9090/api/v1/status/buildinfo | grep -o '"version":"[^"]*"' | cut -d'"' -f4)"
    echo "Grafana 状态: $(curl -s http://localhost:3000/api/health | grep -o '"database":"[^"]*"' | cut -d'"' -f4)"
    
    # Redis 连接数
    redis_connections=$(curl -s http://localhost:9121/metrics | grep "redis_connected_clients" | head -1 | awk '{print $2}')
    echo "Redis 连接数: ${redis_connections:-N/A}"
    
    echo ""
    echo "🎉 监控系统测试完成！"
    echo ""
    echo "📊 访问地址:"
    echo "  • Prometheus: http://localhost:9090"
    echo "  • Grafana:    http://localhost:3000 (admin/admin)"
    echo "  • Redis Exporter: http://localhost:9121/metrics"
    echo ""
    echo "💡 建议:"
    echo "  1. 访问 Grafana 检查仪表板是否正常显示"
    echo "  2. 在 Prometheus 中查询指标: redis_up, redis_connected_clients"
    echo "  3. 如果 ProxyManager 应用在运行，检查 proxy_collector_* 指标"
}

# 运行主函数
main "$@"
