#!/bin/bash

# ProxyManager Docker Compose 部署脚本

set -e

# 颜色定义
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
PURPLE='\033[0;35m'
CYAN='\033[0;36m'
NC='\033[0m'

# 日志函数
log_info() { echo -e "${BLUE}ℹ️  $1${NC}"; }
log_success() { echo -e "${GREEN}✅ $1${NC}"; }
log_warning() { echo -e "${YELLOW}⚠️  $1${NC}"; }
log_error() { echo -e "${RED}❌ $1${NC}"; }
log_step() { echo -e "${PURPLE}🔧 $1${NC}"; }

# 进入项目根目录
cd "$(dirname "$0")/.."

# 默认参数
ACTION="up"
DETACHED=true
BUILD=false
MONITORING=false

# 解析命令行参数
while [[ $# -gt 0 ]]; do
    case $1 in
        up|start)
            ACTION="up"
            shift
            ;;
        down|stop)
            ACTION="down"
            shift
            ;;
        restart)
            ACTION="restart"
            shift
            ;;
        logs)
            ACTION="logs"
            shift
            ;;
        --build)
            BUILD=true
            shift
            ;;
        --monitoring)
            MONITORING=true
            shift
            ;;
        --foreground|-f)
            DETACHED=false
            shift
            ;;
        --help|-h)
            echo "ProxyManager Docker Compose 部署脚本"
            echo ""
            echo "用法: $0 [动作] [选项]"
            echo ""
            echo "动作:"
            echo "  up, start     启动服务 [默认]"
            echo "  down, stop    停止服务"
            echo "  restart       重启服务"
            echo "  logs          查看日志"
            echo ""
            echo "选项:"
            echo "  --build       重新构建镜像"
            echo "  --monitoring  启用监控服务 (Prometheus + Grafana)"
            echo "  --foreground, -f  前台运行"
            echo "  --help, -h    显示帮助信息"
            echo ""
            echo "示例:"
            echo "  $0                    # 启动基础服务"
            echo "  $0 --build           # 重新构建并启动"
            echo "  $0 --monitoring      # 启动包含监控的完整服务"
            echo "  $0 down              # 停止所有服务"
            echo "  $0 logs              # 查看服务日志"
            exit 0
            ;;
        *)
            log_error "未知参数: $1"
            echo "使用 --help 查看帮助信息"
            exit 1
            ;;
    esac
done

echo -e "${CYAN}"
echo "╔══════════════════════════════════════════════════════════════╗"
echo "║                ProxyManager Docker 部署                       ║"
echo "╚══════════════════════════════════════════════════════════════╝"
echo -e "${NC}"

# 检查 Docker 和 Docker Compose
check_docker() {
    if ! command -v docker >/dev/null 2>&1; then
        log_error "Docker 未安装，请先安装 Docker"
        exit 1
    fi
    
    if ! command -v docker-compose >/dev/null 2>&1 && ! docker compose version >/dev/null 2>&1; then
        log_error "Docker Compose 未安装，请先安装 Docker Compose"
        exit 1
    fi
    
    if ! docker info >/dev/null 2>&1; then
        log_error "Docker 服务未运行，请启动 Docker"
        exit 1
    fi
    
    log_success "Docker 环境检查通过"
}

# 准备环境文件
prepare_env() {
    log_step "准备环境配置..."
    
    if [ ! -f ".env" ]; then
        log_warning ".env 文件不存在，运行初始化脚本..."
        if [ -f "scripts/init.sh" ]; then
            ./scripts/init.sh
        else
            log_error "初始化脚本不存在，请手动创建 .env 文件"
            exit 1
        fi
    fi
    
    # 验证必需的环境变量
    if ! grep -q "PROXY_MANAGER_JWT_SECRET" .env || ! grep -q "PROXY_MANAGER_SUPER_API_KEY" .env; then
        log_error ".env 文件缺少必需的环境变量"
        log_info "请运行 ./scripts/init.sh 重新初始化"
        exit 1
    fi
    
    log_success "环境配置准备完成"
}

# 创建必需的目录
create_directories() {
    log_step "创建必需的目录..."
    mkdir -p logs
    log_success "目录创建完成"
}

# 构建服务组合
build_compose_command() {
    local cmd="docker-compose"
    
    # 检查是否使用新版 docker compose
    if docker compose version >/dev/null 2>&1; then
        cmd="docker compose"
    fi
    
    # 基础服务
    local compose_files="-f docker-compose.yml"
    
    # 添加监控服务
    if [ "$MONITORING" = true ]; then
        if [ -f "docker-compose.monitoring.yml" ]; then
            compose_files="$compose_files -f docker-compose.monitoring.yml"
        else
            log_warning "监控配置文件不存在，仅启动基础服务"
        fi
    fi
    
    echo "$cmd $compose_files"
}

# 执行动作
case $ACTION in
    up)
        check_docker
        prepare_env
        create_directories
        
        log_step "启动 ProxyManager 服务..."
        
        compose_cmd=$(build_compose_command)
        
        if [ "$BUILD" = true ]; then
            log_info "重新构建镜像..."
            $compose_cmd build --no-cache
        fi
        
        if [ "$DETACHED" = true ]; then
            $compose_cmd up -d
            log_success "服务已在后台启动"
            
            # 等待服务启动
            log_info "等待服务启动..."
            sleep 10
            
            # 检查服务状态
            log_info "检查服务状态..."
            $compose_cmd ps
            
            echo ""
            log_success "部署完成！"
            echo ""
            echo -e "${CYAN}🌐 访问地址:${NC}"
            echo "• 应用地址: http://localhost:8080"
            echo "• API 文档: http://localhost:8080/api/docs"
            echo "• 健康检查: http://localhost:8080/health"
            
            if [ "$MONITORING" = true ]; then
                echo "• Prometheus: http://localhost:9090"
                echo "• Grafana: http://localhost:3000"
            fi
            
            echo ""
            echo -e "${CYAN}📋 管理员信息:${NC}"
            if [ -f ".env" ]; then
                ADMIN_USER=$(grep "PROXY_MANAGER_ADMIN_USERNAME" .env | cut -d'"' -f2 2>/dev/null || echo "admin")
                ADMIN_PASS=$(grep "PROXY_MANAGER_ADMIN_PASSWORD" .env | cut -d'"' -f2 2>/dev/null || echo "请查看 .env 文件")
                echo "• 用户名: $ADMIN_USER"
                echo "• 密码: $ADMIN_PASS"
            fi
            
            echo ""
            echo -e "${YELLOW}💡 常用命令:${NC}"
            echo "• 查看日志: $0 logs"
            echo "• 停止服务: $0 down"
            echo "• 重启服务: $0 restart"
            echo "• 查看状态: docker-compose ps"
        else
            $compose_cmd up
        fi
        ;;
        
    down)
        log_step "停止 ProxyManager 服务..."
        compose_cmd=$(build_compose_command)
        $compose_cmd down
        log_success "服务已停止"
        ;;
        
    restart)
        log_step "重启 ProxyManager 服务..."
        compose_cmd=$(build_compose_command)
        $compose_cmd restart
        log_success "服务已重启"
        ;;
        
    logs)
        compose_cmd=$(build_compose_command)
        $compose_cmd logs -f
        ;;
        
    *)
        log_error "未知动作: $ACTION"
        exit 1
        ;;
esac
