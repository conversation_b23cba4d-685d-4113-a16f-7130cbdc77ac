#!/bin/bash

# ProxyManager 一键初始化脚本
# 功能：生成密钥、创建配置文件、初始化数据库

set -e

# 颜色定义
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
PURPLE='\033[0;35m'
CYAN='\033[0;36m'
NC='\033[0m' # No Color

# 日志函数
log_info() {
    echo -e "${BLUE}ℹ️  $1${NC}"
}

log_success() {
    echo -e "${GREEN}✅ $1${NC}"
}

log_warning() {
    echo -e "${YELLOW}⚠️  $1${NC}"
}

log_error() {
    echo -e "${RED}❌ $1${NC}"
}

log_step() {
    echo -e "${PURPLE}🔧 $1${NC}"
}

# 进入项目根目录
cd "$(dirname "$0")/.."

echo -e "${CYAN}"
echo "╔══════════════════════════════════════════════════════════════╗"
echo "║                ProxyManager 一键初始化                        ║"
echo "║                     v1.0.0 - 简化版                          ║"
echo "╚══════════════════════════════════════════════════════════════╝"
echo -e "${NC}"

# 0. 环境检查
log_step "步骤 0/5: 环境依赖检查"
if [ -f "scripts/check_env.sh" ]; then
    chmod +x scripts/check_env.sh
    if ! ./scripts/check_env.sh; then
        log_error "环境检查失败，请先解决依赖问题"
        exit 1
    fi
else
    log_warning "环境检查脚本不存在，跳过环境检查"
fi

# 1. 生成安全密钥
log_step "步骤 1/5: 生成安全密钥"

if [ ! -f ".env" ]; then
    log_info "生成 JWT Secret 和 Super API Key..."
    
    # 生成 JWT Secret
    JWT_SECRET=$(go run scripts/generate_jwt_secret.go jwt 2>/dev/null | awk 'length>30' | head -1)
    if [ -z "$JWT_SECRET" ]; then
        log_error "生成 JWT Secret 失败"
        exit 1
    fi
    
    # 生成 Super API Key
    SUPER_API_KEY=$(go run scripts/generate_jwt_secret.go api 2>/dev/null | sed -n '2p')
    if [ -z "$SUPER_API_KEY" ]; then
        log_error "生成 Super API Key 失败"
        exit 1
    fi
    
    # 创建 .env 文件
    cat > .env << EOF
# ProxyManager 环境变量配置
# 自动生成时间: $(date)

# ================================
# 🔐 敏感信息配置 (必须设置)
# ================================

# JWT 密钥 - 用于用户认证 (使用 scripts/generate_jwt_secret.go 生成)
PROXY_MANAGER_JWT_SECRET="$JWT_SECRET"

# 超级 API 密钥 - 用于管理员操作 (使用 scripts/generate_jwt_secret.go 生成)
PROXY_MANAGER_SUPER_API_KEY="$SUPER_API_KEY"

# API 密钥加密密钥 - 用于加密存储的API密钥 (32字节)
# PROXY_MANAGER_API_KEY_ENCRYPTION_KEY=""

# ================================
# 🗄️ 数据库配置
# ================================

# PostgreSQL 数据库密码
PROXY_MANAGER_POSTGRES_PASSWORD="proxy_manager_2025"

# PostgreSQL 连接配置 (可选，使用默认值)
# PROXY_MANAGER_POSTGRES_HOST="localhost"
# PROXY_MANAGER_POSTGRES_PORT="5432"
# PROXY_MANAGER_POSTGRES_USER="postgres"
# PROXY_MANAGER_POSTGRES_DATABASE="proxy_manager"
# PROXY_MANAGER_POSTGRES_SSL_MODE="disable"

# Redis 连接配置 (可选，使用默认值)
# PROXY_MANAGER_REDIS_ADDR="localhost:6379"
# PROXY_MANAGER_REDIS_PASSWORD=""
# PROXY_MANAGER_REDIS_DB="0"

# ================================
# 🚀 部署配置 (可选)
# ================================

# 应用环境 (development, testing, production)
# PROXY_MANAGER_ENV="development"

# 服务器端口
# PROXY_MANAGER_SERVER_PORT="8080"

# 服务器模式 (debug, release)
# PROXY_MANAGER_SERVER_MODE="debug"

# ================================
# 📝 日志配置 (可选)
# ================================

# 日志级别 (debug, info, warn, error)
# PROXY_MANAGER_LOG_LEVEL="info"

# 日志格式 (json, text)
# PROXY_MANAGER_LOG_FORMAT="json"

# 日志输出 (stdout, file)
# PROXY_MANAGER_LOG_OUTPUT="stdout"

# ================================
# 🔧 高级配置 (可选)
# ================================

# 启用开发模式特性
# PROXY_MANAGER_DEV_MODE="false"

# 启用调试日志
# PROXY_MANAGER_DEBUG="false"

# 配置文件路径覆盖
# PROXY_MANAGER_CONFIG_PATH="config/config.yaml"

# ================================
# 🌐 代理提供商配置
# ================================

# Webshare API Token - 用于从 Webshare 获取代理列表
# 获取方式: 登录 https://proxy.webshare.io/ -> Account -> API Token
WEBSHARE_API_TOKEN=""
EOF
    
    log_success ".env 文件已创建"
else
    log_warning ".env 文件已存在，跳过密钥生成"
fi

# 2. 创建配置文件
log_step "步骤 2/5: 创建配置文件"

if [ ! -f "config/config.yaml" ]; then
    log_info "复制配置文件模板..."
    cp config/config.example.yaml config/config.yaml
    log_success "config.yaml 已创建"
else
    log_warning "config.yaml 已存在，跳过创建"
fi

# 3. 检查依赖
log_step "步骤 3/5: 检查项目依赖"

# 检查 Go 依赖
log_info "检查 Go 依赖..."
if go mod tidy; then
    log_success "Go 依赖检查完成"
else
    log_error "Go 依赖检查失败"
    exit 1
fi

# 检查前端依赖
log_info "检查前端依赖..."
cd web
if [ -f "pnpm-lock.yaml" ] && command -v pnpm >/dev/null 2>&1; then
    if pnpm install --frozen-lockfile; then
        log_success "前端依赖安装完成 (pnpm)"
    else
        log_error "前端依赖安装失败"
        exit 1
    fi
elif [ -f "package-lock.json" ] || command -v npm >/dev/null 2>&1; then
    if npm install; then
        log_success "前端依赖安装完成 (npm)"
    else
        log_error "前端依赖安装失败"
        exit 1
    fi
else
    log_warning "未找到包管理器，请手动安装前端依赖"
fi
cd ..

# 4. 创建管理员用户
log_step "步骤 4/5: 创建管理员用户"

# 生成管理员密码
ADMIN_PASSWORD=$(openssl rand -base64 12 2>/dev/null || head -c 12 /dev/urandom | base64 | tr -d '\n')
ADMIN_USERNAME="admin"

log_info "生成管理员账户..."
log_success "管理员用户名: $ADMIN_USERNAME"
log_success "管理员密码: $ADMIN_PASSWORD"

# 将管理员信息添加到 .env 文件
echo "" >> .env
echo "# ================================" >> .env
echo "# 👤 管理员账户信息 (首次初始化生成)" >> .env
echo "# ================================" >> .env
echo "" >> .env
echo "# 管理员用户名" >> .env
echo "PROXY_MANAGER_ADMIN_USERNAME=\"$ADMIN_USERNAME\"" >> .env
echo "" >> .env
echo "# 管理员密码" >> .env
echo "PROXY_MANAGER_ADMIN_PASSWORD=\"$ADMIN_PASSWORD\"" >> .env

log_success "管理员账户信息已保存到 .env 文件"

# 5. 显示初始化完成信息
log_step "步骤 5/5: 初始化完成"

echo ""
echo -e "${GREEN}"
echo "╔══════════════════════════════════════════════════════════════╗"
echo "║                    🎉 初始化完成！                            ║"
echo "╚══════════════════════════════════════════════════════════════╝"
echo -e "${NC}"

echo -e "${CYAN}📋 下一步操作:${NC}"
echo ""
echo "1. 启动数据库服务 (PostgreSQL + Redis):"
echo "   ./scripts/dev.sh start"
echo ""
echo "2. 分别启动前后端:"
echo "   # 后端"
echo "   go run cmd/main.go"
echo "   # 前端 (新终端)"
echo "   cd web && npm run dev"
echo ""

echo -e "${CYAN}🔑 重要信息:${NC}"
echo "• Super API Key: 已保存在 .env 文件中"
echo "• 管理员账户: $ADMIN_USERNAME / $ADMIN_PASSWORD"
echo "• 配置文件: config/config.yaml"
echo "• 数据库会在首次启动时自动初始化"
echo ""

echo -e "${YELLOW}💡 提示:${NC}"
echo "• 请妥善保管 .env 文件中的密钥和管理员密码"
echo "• 生产环境请修改默认密码"
echo "• 首次登录后建议立即修改管理员密码"
echo "• 如需导入代理，请使用: go run scripts/import_webshare_proxies.go"
echo ""

echo -e "${GREEN}🎯 管理员登录信息:${NC}"
echo "┌─────────────────────────────────────────────┐"
echo "│ 用户名: $ADMIN_USERNAME                           │"
echo "│ 密码:   $ADMIN_PASSWORD                    │"
echo "│ 登录地址: http://localhost:8080             │"
echo "└─────────────────────────────────────────────┘"
echo ""

log_success "ProxyManager 初始化完成！"
