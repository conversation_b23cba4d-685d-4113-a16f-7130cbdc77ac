#!/bin/bash

# ProxyManager 监控服务启动脚本
# 此脚本用于启动 Prometheus 和 Grafana 监控服务

set -e

echo "🚀 启动 ProxyManager 监控服务..."

# 检查 Docker 是否运行
if ! docker info > /dev/null 2>&1; then
    echo "❌ Docker 未运行，请先启动 Docker"
    exit 1
fi

# 检查 docker-compose 是否可用
if ! command -v docker-compose &> /dev/null; then
    echo "❌ docker-compose 未安装，请先安装 docker-compose"
    exit 1
fi

# 检查必要的配置文件
echo "📋 检查配置文件..."

if [ ! -f "monitoring/prometheus.yml" ]; then
    echo "❌ Prometheus 配置文件不存在: monitoring/prometheus.yml"
    exit 1
fi

if [ ! -f "monitoring/grafana/datasources/datasource.yml" ]; then
    echo "❌ Grafana 数据源配置文件不存在: monitoring/grafana/datasources/datasource.yml"
    exit 1
fi

if [ ! -f "monitoring/grafana/dashboards/dashboard.yml" ]; then
    echo "❌ Grafana 仪表板配置文件不存在: monitoring/grafana/dashboards/dashboard.yml"
    exit 1
fi

echo "✅ 配置文件检查完成"

# 创建必要的目录
echo "📁 创建必要的目录..."
mkdir -p logs
mkdir -p data

# 启动监控服务
echo "🔧 启动监控服务..."

# 只启动监控相关的服务
docker-compose up -d redis-exporter prometheus grafana

echo "⏳ 等待服务启动..."
sleep 10

# 检查服务状态
echo "🔍 检查服务状态..."

# 检查 Redis Exporter
if curl -s http://localhost:9121/metrics > /dev/null; then
    echo "✅ Redis Exporter 运行正常 (http://localhost:9121)"
else
    echo "⚠️  Redis Exporter 可能未正常启动"
fi

# 检查 Prometheus
if curl -s http://localhost:9090/-/healthy > /dev/null; then
    echo "✅ Prometheus 运行正常 (http://localhost:9090)"
else
    echo "⚠️  Prometheus 可能未正常启动"
fi

# 检查 Grafana
if curl -s http://localhost:3000/api/health > /dev/null; then
    echo "✅ Grafana 运行正常 (http://localhost:3000)"
    echo "   默认登录: admin/admin"
else
    echo "⚠️  Grafana 可能未正常启动"
fi

echo ""
echo "🎉 监控服务启动完成！"
echo ""
echo "📊 访问地址:"
echo "   • Prometheus: http://localhost:9090"
echo "   • Grafana:    http://localhost:3000 (admin/admin)"
echo "   • Redis Exporter: http://localhost:9121/metrics"
echo ""
echo "📝 注意事项:"
echo "   1. 首次访问 Grafana 需要使用 admin/admin 登录"
echo "   2. Grafana 仪表板会自动加载 ProxyManager 监控面板"
echo "   3. 确保 ProxyManager 主应用也在运行以获取完整指标"
echo ""
echo "🛑 停止监控服务: docker-compose down prometheus grafana redis-exporter"
