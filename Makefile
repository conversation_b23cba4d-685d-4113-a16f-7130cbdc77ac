.PHONY: build run test clean docker-build docker-run docker-stop help

# 变量定义
BINARY_NAME=proxyManager
BUILD_DIR=build
DOCKER_IMAGE=proxy-manager

# 默认目标
.DEFAULT_GOAL := help

# 帮助信息
help: ## 显示帮助信息
	@echo "Proxy Manager 构建和运行命令:"
	@echo ""
	@grep -E '^[a-zA-Z_-]+:.*?## .*$$' $(MAKEFILE_LIST) | sort | awk 'BEGIN {FS = ":.*?## "}; {printf "\033[36m%-20s\033[0m %s\n", $$1, $$2}'

# 构建应用
build: ## 构建应用
	@echo "构建 Proxy Manager..."
	@mkdir -p $(BUILD_DIR)
	@go build -o $(BUILD_DIR)/$(BINARY_NAME) cmd/main.go
	@echo "构建完成: $(BUILD_DIR)/$(BINARY_NAME)"

# 运行应用
run: build ## 构建并运行应用
	@echo "运行 Proxy Manager..."
	@./$(BUILD_DIR)/$(BINARY_NAME)

# 开发模式运行
dev: ## 开发模式运行（使用air热重载）
	@if command -v air > /dev/null; then \
		air; \
	else \
		echo "Air 未安装，使用 go run 运行..."; \
		go run cmd/main.go; \
	fi

# 前端开发模式
dev-frontend: ## 启动前端开发服务器
	@echo "启动前端开发服务器..."
	@cd web && pnpm run dev

# 后端开发模式（热重载）
dev-backend: ## 启动后端开发服务器（热重载）
	@echo "启动后端开发服务器（热重载）..."
	@if command -v air > /dev/null; then \
		air; \
	else \
		echo "Air 未安装，使用 go run 运行..."; \
		go run cmd/main.go; \
	fi

# 全栈开发模式（前后端同时运行）
dev-full: ## 启动全栈开发环境（前后端同时运行）
	@echo "启动全栈开发环境..."
	@echo "后端: http://localhost:8080"
	@echo "前端: http://localhost:3000"
	@echo "按 Ctrl+C 停止所有服务"
	@trap 'kill %1 %2' SIGINT; \
	$(MAKE) dev-backend & \
	$(MAKE) dev-frontend & \
	wait

# 使用concurrently运行（如果安装了）
dev-concurrent: ## 使用concurrently同时运行前后端
	@if command -v concurrently > /dev/null; then \
		concurrently \
			--names "backend,frontend" \
			--prefix-colors "blue,green" \
			"$(MAKE) dev-backend" \
			"$(MAKE) dev-frontend"; \
	else \
		echo "concurrently 未安装，使用基础模式..."; \
		$(MAKE) dev-full; \
	fi

# 安装前端依赖
install-frontend: ## 安装前端依赖
	@echo "安装前端依赖..."
	@cd web && pnpm install

# 安装后端依赖
install-backend: ## 安装后端依赖
	@echo "安装后端依赖..."
	@go mod tidy
	@go mod download

# 安装所有依赖
install: install-backend install-frontend ## 安装所有依赖

# 环境检查
check-env: ## 检查环境依赖
	@chmod +x scripts/check_env.sh
	@./scripts/check_env.sh

# 项目初始化
init: ## 初始化项目（生成配置、安装依赖）
	@chmod +x scripts/init.sh
	@./scripts/init.sh

# 验证配置
validate-config: ## 验证项目配置
	@go run scripts/validate_config.go

# 一键部署（开发环境）
deploy: ## 一键部署（开发环境）
	@chmod +x scripts/deploy.sh
	@./scripts/deploy.sh

# 一键部署（生产环境）
deploy-prod: ## 一键部署（生产环境）
	@chmod +x scripts/deploy.sh
	@./scripts/deploy.sh --env production

# Docker Compose 部署
docker-deploy: ## Docker Compose 部署
	@chmod +x scripts/docker-deploy.sh
	@./scripts/docker-deploy.sh

# Docker Compose 部署（包含监控）
docker-deploy-full: ## Docker Compose 部署（包含监控）
	@chmod +x scripts/docker-deploy.sh
	@./scripts/docker-deploy.sh --monitoring

# 测试
test: ## 运行测试
	@echo "运行测试..."
	@go test -v ./...

# 测试覆盖率
test-coverage: ## 运行测试并生成覆盖率报告
	@echo "运行测试并生成覆盖率报告..."
	@go test -v -coverprofile=coverage.out ./...
	@go tool cover -html=coverage.out -o coverage.html
	@echo "覆盖率报告已生成: coverage.html"

# 清理
clean: ## 清理构建文件
	@echo "清理构建文件..."
	@rm -rf $(BUILD_DIR)
	@rm -f coverage.out coverage.html
	@go clean
	@cd web && pnpm run clean 2>/dev/null || true

# Docker构建
docker-build: ## 构建Docker镜像
	@echo "构建 Docker 镜像..."
	@docker build -t $(DOCKER_IMAGE) .
	@echo "Docker 镜像构建完成: $(DOCKER_IMAGE)"

# Docker运行
docker-run: ## 使用Docker Compose运行
	@echo "使用 Docker Compose 启动服务..."
	@docker-compose up -d
	@echo "服务已启动，访问 http://localhost:8080"

# Docker停止
docker-stop: ## 停止Docker服务
	@echo "停止 Docker 服务..."
	@docker-compose down
	@echo "服务已停止"

# Docker重启
docker-restart: docker-stop docker-run ## 重启Docker服务

# 查看日志
logs: ## 查看Docker日志
	@docker-compose logs -f

# 安装依赖
deps: ## 安装Go依赖
	@echo "安装 Go 依赖..."
	@go mod tidy
	@go mod download

# 格式化代码
fmt: ## 格式化代码
	@echo "格式化代码..."
	@go fmt ./...

# 代码检查
lint: ## 运行代码检查
	@echo "运行代码检查..."
	@if command -v golangci-lint > /dev/null; then \
		golangci-lint run; \
	else \
		echo "golangci-lint 未安装，跳过代码检查"; \
	fi

# 生成API文档
docs: ## 生成API文档（需要swag）
	@if command -v swag > /dev/null; then \
		swag init -g cmd/main.go; \
		echo "API文档已生成"; \
	else \
		echo "swag 未安装，无法生成API文档"; \
	fi

# 性能测试
bench: ## 运行性能测试
	@echo "运行性能测试..."
	@go test -bench=. -benchmem ./...

# 安全扫描
security: ## 运行安全扫描
	@echo "运行安全扫描..."
	@if command -v gosec > /dev/null; then \
		gosec ./...; \
	else \
		echo "gosec 未安装，跳过安全扫描"; \
	fi

# 完整构建流程
all: clean deps fmt lint test build ## 完整构建流程（清理、安装依赖、格式化、检查、测试、构建）

# 开发环境设置
setup-dev: ## 设置开发环境
	@echo "设置开发环境..."
	@go install github.com/cosmtrek/air@latest
	@go install github.com/golangci/golangci-lint/cmd/golangci-lint@latest
	@go install github.com/swaggo/swag/cmd/swag@latest
	@go install github.com/securecodewarrior/gosec/v2/cmd/gosec@latest
	@npm install -g concurrently
	@echo "开发环境设置完成"

# 停止所有服务
stop-all: ## 停止所有服务
	@echo "停止数据库服务..."
	@./scripts/dev.sh stop 2>/dev/null || true
	@echo "停止 Docker 服务..."
	@./scripts/docker-deploy.sh down 2>/dev/null || true
	@echo "停止后台进程..."
	@pkill -f "proxyManager" 2>/dev/null || true
	@pkill -f "go run cmd/main.go" 2>/dev/null || true

# 导入代理
import-proxies: ## 导入代理数据
	@go run scripts/import_webshare_proxies.go

# 生成密钥
generate-keys: ## 生成 JWT 密钥和 API 密钥
	@go run scripts/generate_jwt_secret.go both

# 数据库迁移切换
switch-migration: ## 切换数据库迁移方案
	@chmod +x scripts/switch-migration.sh
	@./scripts/switch-migration.sh

# 完整重置
reset: ## 完整重置项目（删除配置和数据）
	@echo "⚠️  这将删除所有配置和数据，确认请输入 'yes':"
	@read confirm && [ "$$confirm" = "yes" ] || (echo "操作已取消" && exit 1)
	@$(MAKE) stop-all
	@rm -f .env
	@rm -f config/config.yaml
	@docker-compose down -v 2>/dev/null || true
	@echo "重置完成，请运行 'make init' 重新初始化"

# 快速开发环境启动
quick-start: init deploy ## 快速启动（初始化+部署）

# ========================================
# 📊 监控相关命令
# ========================================

# 启动监控服务
monitoring: ## 启动监控服务 (Prometheus + Grafana)
	@echo "📊 启动监控服务..."
	@docker-compose up -d redis redis-exporter prometheus grafana
	@echo "⏳ 等待服务启动..."
	@sleep 10
	@echo "✅ 监控服务已启动"
	@echo ""
	@echo "📊 访问地址:"
	@echo "  • Prometheus: http://localhost:9090"
	@echo "  • Grafana:    http://localhost:3000 (admin/admin)"
	@echo "  • Redis Exporter: http://localhost:9121/metrics"

# 启动监控服务（别名）
monitoring-start: monitoring ## 启动监控服务

# 停止监控服务
monitoring-stop: ## 停止监控服务
	@echo "🛑 停止监控服务..."
	@docker-compose stop prometheus grafana redis-exporter

# 重启监控服务
monitoring-restart: ## 重启监控服务
	@echo "🔄 重启监控服务..."
	@docker-compose restart prometheus grafana redis-exporter

# 测试监控系统
monitoring-test: ## 测试监控系统
	@echo "🧪 测试监控系统..."
	@if [ -f "scripts/test-monitoring.sh" ]; then \
		chmod +x scripts/test-monitoring.sh && \
		./scripts/test-monitoring.sh; \
	else \
		echo "❌ 测试脚本不存在"; \
	fi

# 查看监控服务日志
monitoring-logs: ## 查看监控服务日志
	@echo "📝 查看监控服务日志..."
	@docker-compose logs -f prometheus grafana redis-exporter

# 监控服务状态
monitoring-status: ## 查看监控服务状态
	@echo "📊 监控服务状态:"
	@docker-compose ps prometheus grafana redis-exporter

# 健康检查
health-check: ## 检查所有服务健康状态
	@echo "🏥 检查服务健康状态..."
	@echo "ProxyManager App:"
	@curl -s http://localhost:8080/health 2>/dev/null && echo "  ✅ 正常" || echo "  ❌ 异常"
	@echo "Prometheus:"
	@curl -s http://localhost:9090/-/healthy 2>/dev/null && echo "  ✅ 正常" || echo "  ❌ 异常"
	@echo "Grafana:"
	@curl -s http://localhost:3000/api/health 2>/dev/null && echo "  ✅ 正常" || echo "  ❌ 异常"
	@echo "Redis Exporter:"
	@curl -s http://localhost:9121/metrics 2>/dev/null && echo "  ✅ 正常" || echo "  ❌ 异常"