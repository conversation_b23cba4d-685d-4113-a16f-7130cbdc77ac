# 数据库迁移说明

本目录包含 ProxyManager 项目的数据库迁移文件。

## 🎯 简化方案

为了简化项目初始化，我们提供了两种数据库初始化方案：

### 方案一：简化版（推荐新项目）
使用 `000_simplified_schema.sql` - 包含项目运行所需的最基本表结构。

### 方案二：完整版（现有项目）
使用 `001-006` 系列迁移文件 - 包含完整的功能特性。

## 📁 文件说明

### 简化版迁移
- `000_simplified_schema.sql` - **推荐使用**，包含核心功能的简化数据库结构

### 完整版迁移（可选）
- `001_initial_schema.sql` - 初始数据库结构（包含高级功能）
- `002_api_keys_and_settings.sql` - API密钥和用户设置
- `003_add_proxy_tags.sql` - 代理标签功能
- `004_rbac_system.sql` - 角色权限管理系统
- `005_add_encrypted_api_key.sql` - 加密API密钥字段
- `006_rename_encrypted_key.sql` - 重命名加密密钥字段

## 🚀 使用建议

### 新项目（推荐）
如果您是第一次部署 ProxyManager，建议使用简化版：

1. 重命名或备份现有迁移文件：
```bash
mkdir migrations/backup
mv migrations/00[1-6]_*.sql migrations/backup/
```

2. 确保只有 `000_simplified_schema.sql` 在 migrations 目录中

3. 启动项目，数据库会自动初始化

### 现有项目
如果您已经在使用完整版迁移，可以继续使用现有的迁移文件。

## 📊 功能对比

| 功能 | 简化版 | 完整版 |
|------|--------|--------|
| 用户管理 | ✅ 基础 | ✅ 完整 |
| 代理管理 | ✅ 核心功能 | ✅ 高级功能 |
| 任务管理 | ✅ 基础 | ✅ 完整 |
| API密钥 | ✅ 基础 | ✅ 高级管理 |
| 代理标签 | ❌ | ✅ |
| 权限管理 | ❌ | ✅ RBAC |
| 健康检查记录 | ❌ | ✅ |
| 告警系统 | ❌ | ✅ |
| 团队管理 | ❌ | ✅ |

## 🔄 迁移管理

项目使用自动迁移管理器，会按文件名顺序执行迁移：

1. 系统会自动创建 `schema_migrations` 表记录已执行的迁移
2. 只有未执行的迁移会被运行
3. 迁移在事务中执行，失败会自动回滚

## 🛠️ 手动管理

如果需要手动管理迁移：

### 查看迁移状态
```sql
SELECT * FROM schema_migrations ORDER BY version;
```

### 手动执行迁移
```bash
# 连接到数据库
psql -h localhost -U postgres -d proxy_manager

# 执行特定迁移文件
\i migrations/000_simplified_schema.sql
```

### 重置数据库（谨慎操作）
```sql
-- 删除所有表（会丢失所有数据！）
DROP SCHEMA public CASCADE;
CREATE SCHEMA public;
```

## 📝 自定义迁移

如果需要添加自定义功能，可以创建新的迁移文件：

1. 文件命名格式：`XXX_description.sql`
2. XXX 为三位数字版本号，按顺序递增
3. description 为功能描述，使用下划线分隔

示例：
```sql
-- 007_add_custom_feature.sql
-- 添加自定义功能

ALTER TABLE proxies ADD COLUMN custom_field VARCHAR(100);
```

## ⚠️ 注意事项

1. **备份数据**：执行迁移前请备份重要数据
2. **测试环境**：先在测试环境验证迁移
3. **版本控制**：迁移文件应纳入版本控制
4. **不可修改**：已执行的迁移文件不应修改
5. **向前兼容**：新迁移应保持向前兼容性

## 🔧 故障排除

### 迁移失败
1. 检查数据库连接
2. 查看错误日志
3. 检查 SQL 语法
4. 确认权限设置

### 重复执行
迁移管理器会自动跳过已执行的迁移，可以安全地重复运行。

### 回滚迁移
```bash
# 注意：这只是删除迁移记录，不会回滚数据变更
# 实际的数据回滚需要手动编写回滚脚本
```

## 📞 获取帮助

如果遇到迁移相关问题：

1. 查看项目日志
2. 检查数据库连接状态
3. 参考项目文档
4. 提交 Issue 到项目仓库
