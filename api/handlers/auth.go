package handlers

import (
	"context"
	"net/http"
	"time"

	"proxyManager/internal/auth"
	"proxyManager/internal/models"
	"proxyManager/internal/repository"
	"proxyManager/pkg/database"

	"github.com/gin-gonic/gin"
	"github.com/sirupsen/logrus"
)

// AuthHandler 认证处理器
type AuthHandler struct {
	repoManager repository.RepositoryManager
	redisClient *database.RedisClient // 保留Redis用于缓存
	jwtManager  *auth.JWTManager
	logger      *logrus.Logger
}

// NewAuthHandler 创建认证处理器
func NewAuthHandler(repoManager repository.RepositoryManager, redisClient *database.RedisClient, jwtManager *auth.JWTManager, logger *logrus.Logger) *AuthHandler {
	return &AuthHandler{
		repoManager: repoManager,
		redisClient: redisClient,
		jwtManager:  jwtManager,
		logger:      logger,
	}
}

// Register 用户注册
func (h *AuthHandler) Register(c *gin.Context) {
	var req models.UserRequest
	if err := c.ShouldBindJSON(&req); err != nil {
		c.JSON(http.StatusBadRequest, gin.H{"error": err.Error()})
		return
	}

	ctx := c.Request.Context()

	// 检查用户名是否已存在
	existingUser, _ := h.repoManager.GetUser().GetByUsername(ctx, req.Username)
	if existingUser != nil {
		c.JSON(http.StatusConflict, gin.H{"error": "Username already exists"})
		return
	}

	// 检查邮箱是否已存在
	existingUser, _ = h.repoManager.GetUser().GetByEmail(ctx, req.Email)
	if existingUser != nil {
		c.JSON(http.StatusConflict, gin.H{"error": "Email already exists"})
		return
	}

	// 哈希密码
	hashedPassword, err := auth.HashPassword(req.Password)
	if err != nil {
		h.logger.WithError(err).Error("Failed to hash password")
		c.JSON(http.StatusInternalServerError, gin.H{"error": "Failed to create user"})
		return
	}

	// 生成API密钥
	apiKey, err := auth.GenerateAPIKey()
	if err != nil {
		h.logger.WithError(err).Error("Failed to generate API key")
		c.JSON(http.StatusInternalServerError, gin.H{"error": "Failed to create user"})
		return
	}

	// 创建用户
	now := time.Now()
	user := &models.User{
		ID:               "", // 让数据库自动生成 UUID
		Username:         req.Username,
		Email:            req.Email,
		Password:         hashedPassword,
		Role:             req.Role,
		Status:           models.UserStatusActive,
		CreatedAt:        now,
		UpdatedAt:        now,
		APIKey:           apiKey,
		APIKeyCreated:    now,
		APIKeyLastUsed:   time.Time{}, // 初始为零值
		APIKeyUsageCount: 0,
	}

	// 保存用户到PostgreSQL
	if err := h.repoManager.GetUser().Create(ctx, user); err != nil {
		h.logger.WithError(err).Error("Failed to save user")
		c.JSON(http.StatusInternalServerError, gin.H{"error": "Failed to create user"})
		return
	}

	// 同时缓存到Redis（可选，用于提高查询性能）
	go func() {
		cacheCtx := context.Background()
		key := "user:" + user.ID
		h.redisClient.Set(cacheCtx, key, user, time.Hour*24) // 缓存24小时

		// 缓存用户名索引
		usernameKey := "user:username:" + user.Username
		h.redisClient.Set(cacheCtx, usernameKey, user.ID, time.Hour*24)

		// 缓存邮箱索引
		emailKey := "user:email:" + user.Email
		h.redisClient.Set(cacheCtx, emailKey, user.ID, time.Hour*24)
	}()

	c.JSON(http.StatusCreated, gin.H{
		"message": "User registered successfully",
		"data": models.UserResponse{
			ID:        user.ID,
			Username:  user.Username,
			Email:     user.Email,
			Role:      user.Role,
			Status:    user.Status,
			CreatedAt: user.CreatedAt,
		},
	})
}

// Login 用户登录
func (h *AuthHandler) Login(c *gin.Context) {
	var req models.LoginRequest
	if err := c.ShouldBindJSON(&req); err != nil {
		c.JSON(http.StatusBadRequest, gin.H{"error": err.Error()})
		return
	}

	ctx := c.Request.Context()

	// 获取用户
	user, err := h.repoManager.GetUser().GetByUsername(ctx, req.Username)
	if err != nil || user == nil {
		h.logger.WithFields(logrus.Fields{
			"username": req.Username,
		}).Warn("User not found during login")
		c.JSON(http.StatusUnauthorized, gin.H{"error": "Invalid credentials"})
		return
	}

	// 检查密码
	if !auth.CheckPassword(req.Password, user.Password) {
		h.logger.WithFields(logrus.Fields{
			"username": req.Username,
		}).Warn("Password check failed during login")
		c.JSON(http.StatusUnauthorized, gin.H{"error": "Invalid credentials"})
		return
	}

	// 检查用户状态
	if user.Status != models.UserStatusActive {
		c.JSON(http.StatusForbidden, gin.H{"error": "Account is not active"})
		return
	}

	// 生成token
	token, err := h.jwtManager.GenerateToken(user)
	if err != nil {
		h.logger.WithError(err).Error("Failed to generate token")
		c.JSON(http.StatusInternalServerError, gin.H{"error": "Failed to generate token"})
		return
	}

	// 更新最后登录时间
	now := time.Now()
	if err := h.repoManager.GetUser().UpdateLastLogin(ctx, user.ID, now); err != nil {
		h.logger.WithError(err).Error("Failed to update last login time")
		// 不阻断登录流程，只记录错误
	}
	user.LastLogin = &now

	c.JSON(http.StatusOK, gin.H{
		"message": "Login successful",
		"data": models.LoginResponse{
			AccessToken: token,
			ExpiresIn:   int64(h.jwtManager.TokenDuration().Seconds()),
			User: models.UserResponse{
				ID:        user.ID,
				Username:  user.Username,
				Email:     user.Email,
				Role:      user.Role,
				Status:    user.Status,
				CreatedAt: user.CreatedAt,
				LastLogin: user.LastLogin,
			},
		},
	})
}

// GetProfile 获取用户资料
func (h *AuthHandler) GetProfile(c *gin.Context) {
	userID, exists := c.Get("user_id")
	if !exists {
		c.JSON(http.StatusUnauthorized, gin.H{"error": "User not authenticated"})
		return
	}

	ctx := c.Request.Context()
	user, err := h.repoManager.GetUser().GetByID(ctx, userID.(string))
	if err != nil || user == nil {
		c.JSON(http.StatusNotFound, gin.H{"error": "User not found"})
		return
	}

	c.JSON(http.StatusOK, gin.H{
		"data": models.UserResponse{
			ID:        user.ID,
			Username:  user.Username,
			Email:     user.Email,
			Role:      user.Role,
			Status:    user.Status,
			CreatedAt: user.CreatedAt,
			LastLogin: user.LastLogin,
		},
	})
}

// 私有方法

func (h *AuthHandler) getUserByID(ctx context.Context, userID string) (*models.User, error) {
	// 首先尝试从缓存获取
	key := "user:" + userID
	var user models.User
	if err := h.redisClient.Get(ctx, key, &user); err == nil {
		return &user, nil
	}

	// 缓存未命中，从数据库获取
	dbUser, err := h.repoManager.GetUser().GetByID(ctx, userID)
	if err != nil || dbUser == nil {
		return nil, err
	}

	// 异步更新缓存
	go func() {
		cacheCtx := context.Background()
		h.redisClient.Set(cacheCtx, key, dbUser, time.Hour*24)
	}()

	return dbUser, nil
}

func (h *AuthHandler) getUserByUsername(ctx context.Context, username string) (*models.User, error) {
	// 首先尝试从缓存获取用户ID
	usernameKey := "user:username:" + username
	var userID string
	if err := h.redisClient.Get(ctx, usernameKey, &userID); err == nil {
		return h.getUserByID(ctx, userID)
	}

	// 缓存未命中，从数据库获取
	return h.repoManager.GetUser().GetByUsername(ctx, username)
}

func (h *AuthHandler) getUserByEmail(ctx context.Context, email string) (*models.User, error) {
	// 首先尝试从缓存获取用户ID
	emailKey := "user:email:" + email
	var userID string
	if err := h.redisClient.Get(ctx, emailKey, &userID); err == nil {
		return h.getUserByID(ctx, userID)
	}

	// 缓存未命中，从数据库获取
	return h.repoManager.GetUser().GetByEmail(ctx, email)
}

// maskAPIKey 隐藏 API Key 的中间部分
func (h *AuthHandler) maskAPIKey(apiKey string) string {
	if len(apiKey) <= 8 {
		return apiKey
	}
	return apiKey[:4] + "****" + apiKey[len(apiKey)-4:]
}

// GetAPIKey 获取当前用户的 API Key 信息
func (h *AuthHandler) GetAPIKey(c *gin.Context) {
	userID, exists := c.Get("user_id")
	if !exists {
		c.JSON(http.StatusUnauthorized, gin.H{"error": "User not authenticated"})
		return
	}

	ctx := c.Request.Context()
	user, err := h.repoManager.GetUser().GetByID(ctx, userID.(string))
	if err != nil || user == nil {
		c.JSON(http.StatusNotFound, gin.H{"error": "User not found"})
		return
	}

	response := models.APIKeyStatsResponse{
		APIKeyCreated:    user.APIKeyCreated,
		APIKeyLastUsed:   user.APIKeyLastUsed,
		APIKeyUsageCount: user.APIKeyUsageCount,
		MaskedAPIKey:     h.maskAPIKey(user.APIKey),
	}

	c.JSON(http.StatusOK, gin.H{
		"data": response,
	})
}

// GenerateAPIKey 生成新的 API Key
func (h *AuthHandler) GenerateAPIKey(c *gin.Context) {
	userID, exists := c.Get("user_id")
	if !exists {
		c.JSON(http.StatusUnauthorized, gin.H{"error": "User not authenticated"})
		return
	}

	ctx := c.Request.Context()
	user, err := h.repoManager.GetUser().GetByID(ctx, userID.(string))
	if err != nil || user == nil {
		c.JSON(http.StatusNotFound, gin.H{"error": "User not found"})
		return
	}

	// 生成新的 API Key
	newAPIKey, err := auth.GenerateAPIKey()
	if err != nil {
		h.logger.WithError(err).Error("Failed to generate API key")
		c.JSON(http.StatusInternalServerError, gin.H{"error": "Failed to generate API key"})
		return
	}

	// 更新用户信息
	now := time.Now()
	user.APIKey = newAPIKey
	user.APIKeyCreated = now
	user.APIKeyLastUsed = time.Time{} // 重置使用时间
	user.APIKeyUsageCount = 0         // 重置使用次数
	user.UpdatedAt = now

	// 保存到PostgreSQL数据库
	if err := h.repoManager.GetUser().Update(ctx, user); err != nil {
		h.logger.WithError(err).Error("Failed to update user API key")
		c.JSON(http.StatusInternalServerError, gin.H{"error": "Failed to update API key"})
		return
	}

	// 异步更新缓存和API Key索引
	go func() {
		cacheCtx := context.Background()

		// 更新用户缓存
		key := "user:" + user.ID
		h.redisClient.Set(cacheCtx, key, user, time.Hour*24)

		// 更新API Key索引
		if newAPIKey != "" {
			h.redisClient.HSet(cacheCtx, "apikey_to_user", newAPIKey, user.ID)
		}
	}()

	h.logger.WithFields(logrus.Fields{
		"user_id":  user.ID,
		"username": user.Username,
	}).Info("User generated new API key")

	response := models.APIKeyResponse{
		APIKeyInfo: models.APIKeyInfo{
			ID:               "", // 旧版本不需要ID
			Name:             "Legacy API Key",
			APIKeyCreated:    user.APIKeyCreated,
			APIKeyLastUsed:   user.APIKeyLastUsed,
			APIKeyUsageCount: user.APIKeyUsageCount,
			MaskedAPIKey:     h.maskAPIKey(newAPIKey),
			IsActive:         true,
		},
		APIKey: newAPIKey,
	}

	c.JSON(http.StatusOK, gin.H{
		"message": "API key generated successfully",
		"data":    response,
	})
}

// RegenerateAPIKey 重新生成 API Key（与 GenerateAPIKey 相同，但语义不同）
func (h *AuthHandler) RegenerateAPIKey(c *gin.Context) {
	userID, exists := c.Get("user_id")
	if !exists {
		c.JSON(http.StatusUnauthorized, gin.H{"error": "User not authenticated"})
		return
	}

	ctx := c.Request.Context()
	user, err := h.repoManager.GetUser().GetByID(ctx, userID.(string))
	if err != nil || user == nil {
		c.JSON(http.StatusNotFound, gin.H{"error": "User not found"})
		return
	}

	// 生成新的 API Key
	newAPIKey, err := auth.GenerateAPIKey()
	if err != nil {
		h.logger.WithError(err).Error("Failed to regenerate API key")
		c.JSON(http.StatusInternalServerError, gin.H{"error": "Failed to regenerate API key"})
		return
	}

	// 保存旧的API Key用于清理索引
	oldAPIKey := user.APIKey

	// 更新用户信息
	now := time.Now()
	user.APIKey = newAPIKey
	user.APIKeyCreated = now
	user.APIKeyLastUsed = time.Time{} // 重置使用时间
	user.APIKeyUsageCount = 0         // 重置使用次数
	user.UpdatedAt = now

	// 保存到PostgreSQL数据库
	if err := h.repoManager.GetUser().Update(ctx, user); err != nil {
		h.logger.WithError(err).Error("Failed to update user API key")
		c.JSON(http.StatusInternalServerError, gin.H{"error": "Failed to regenerate API key"})
		return
	}

	// 异步更新缓存和API Key索引
	go func() {
		cacheCtx := context.Background()

		// 清理旧的API Key索引
		if oldAPIKey != "" {
			h.redisClient.HDel(cacheCtx, "apikey_to_user", oldAPIKey)
		}

		// 更新用户缓存
		key := "user:" + user.ID
		h.redisClient.Set(cacheCtx, key, user, time.Hour*24)

		// 创建新的API Key索引
		if newAPIKey != "" {
			h.redisClient.HSet(cacheCtx, "apikey_to_user", newAPIKey, user.ID)
		}
	}()

	h.logger.WithFields(logrus.Fields{
		"user_id":  user.ID,
		"username": user.Username,
	}).Info("User regenerated API key")

	response := models.APIKeyResponse{
		APIKeyInfo: models.APIKeyInfo{
			ID:               "", // 旧版本不需要ID
			Name:             "Legacy API Key",
			APIKeyCreated:    user.APIKeyCreated,
			APIKeyLastUsed:   user.APIKeyLastUsed,
			APIKeyUsageCount: user.APIKeyUsageCount,
			MaskedAPIKey:     h.maskAPIKey(newAPIKey),
			IsActive:         true,
		},
		APIKey: newAPIKey,
	}

	c.JSON(http.StatusOK, gin.H{
		"message": "API key regenerated successfully",
		"data":    response,
	})
}

// DeleteAPIKey 删除/禁用 API Key
func (h *AuthHandler) DeleteAPIKey(c *gin.Context) {
	userID, exists := c.Get("user_id")
	if !exists {
		c.JSON(http.StatusUnauthorized, gin.H{"error": "User not authenticated"})
		return
	}

	ctx := c.Request.Context()
	user, err := h.repoManager.GetUser().GetByID(ctx, userID.(string))
	if err != nil || user == nil {
		c.JSON(http.StatusNotFound, gin.H{"error": "User not found"})
		return
	}

	// 清空 API Key
	user.APIKey = ""
	user.APIKeyCreated = time.Time{}
	user.APIKeyLastUsed = time.Time{}
	user.APIKeyUsageCount = 0
	user.UpdatedAt = time.Now()

	// 保存到PostgreSQL数据库
	if err := h.repoManager.GetUser().Update(ctx, user); err != nil {
		h.logger.WithError(err).Error("Failed to delete user API key")
		c.JSON(http.StatusInternalServerError, gin.H{"error": "Failed to delete API key"})
		return
	}

	// 异步更新缓存
	go func() {
		cacheCtx := context.Background()
		key := "user:" + user.ID
		h.redisClient.Set(cacheCtx, key, user, time.Hour*24)
	}()

	h.logger.WithFields(logrus.Fields{
		"user_id":  user.ID,
		"username": user.Username,
	}).Info("User deleted API key")

	c.JSON(http.StatusOK, gin.H{
		"message": "API key deleted successfully",
	})
}
