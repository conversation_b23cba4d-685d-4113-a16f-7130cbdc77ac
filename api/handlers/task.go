package handlers

import (
	"net/http"

	"proxyManager/internal/models"
	"proxyManager/internal/task"

	"github.com/gin-gonic/gin"
	"github.com/sirupsen/logrus"
)

// TaskHandler 任务处理器
type TaskHandler struct {
	taskManager *task.Manager
	logger      *logrus.Logger
}

// NewTaskHandler 创建任务处理器
func NewTaskHandler(taskManager *task.Manager, logger *logrus.Logger) *TaskHandler {
	return &TaskHandler{
		taskManager: taskManager,
		logger:      logger,
	}
}

// CreateTask 创建任务
func (h *TaskHandler) CreateTask(c *gin.Context) {
	var req models.TaskRequest
	if err := c.ShouldBindJSON(&req); err != nil {
		c.JSON(http.StatusBadRequest, gin.H{"error": err.Error()})
		return
	}

	// 从上下文中获取用户ID
	userID, exists := c.Get("user_id")
	if !exists {
		c.<PERSON>(http.StatusUnauthorized, gin.H{"error": "User not authenticated"})
		return
	}

	ctx := c.Request.Context()
	task, err := h.taskManager.CreateTask(ctx, &req, userID.(string))
	if err != nil {
		h.logger.WithError(err).Error("Failed to create task")
		c.JSON(http.StatusInternalServerError, gin.H{"error": "Failed to create task"})
		return
	}

	c.JSON(http.StatusCreated, gin.H{
		"message": "Task created successfully",
		"data":    task,
	})
}

// GetTask 获取任务
func (h *TaskHandler) GetTask(c *gin.Context) {
	taskID := c.Param("id")
	if taskID == "" {
		c.JSON(http.StatusBadRequest, gin.H{"error": "Task ID is required"})
		return
	}

	ctx := c.Request.Context()
	task, err := h.taskManager.GetTask(ctx, taskID)
	if err != nil {
		h.logger.WithError(err).WithField("task_id", taskID).Error("Failed to get task")
		c.JSON(http.StatusNotFound, gin.H{"error": "Task not found"})
		return
	}

	c.JSON(http.StatusOK, gin.H{
		"data": task,
	})
}

// GetUserTasks 获取用户任务
func (h *TaskHandler) GetUserTasks(c *gin.Context) {
	// 从上下文中获取用户ID
	userID, exists := c.Get("user_id")
	if !exists {
		c.JSON(http.StatusUnauthorized, gin.H{"error": "User not authenticated"})
		return
	}

	ctx := c.Request.Context()
	tasks, err := h.taskManager.GetUserTasks(ctx, userID.(string))
	if err != nil {
		h.logger.WithError(err).Error("Failed to get user tasks")
		c.JSON(http.StatusInternalServerError, gin.H{"error": "Failed to get user tasks"})
		return
	}

	c.JSON(http.StatusOK, gin.H{
		"data":  tasks,
		"total": len(tasks),
	})
}

// UpdateTask 更新任务
func (h *TaskHandler) UpdateTask(c *gin.Context) {
	taskID := c.Param("id")
	if taskID == "" {
		c.JSON(http.StatusBadRequest, gin.H{"error": "Task ID is required"})
		return
	}

	var req models.TaskRequest
	if err := c.ShouldBindJSON(&req); err != nil {
		c.JSON(http.StatusBadRequest, gin.H{"error": err.Error()})
		return
	}

	// 从上下文中获取用户ID
	userID, exists := c.Get("user_id")
	if !exists {
		c.JSON(http.StatusUnauthorized, gin.H{"error": "User not authenticated"})
		return
	}

	ctx := c.Request.Context()
	task, err := h.taskManager.UpdateTask(ctx, taskID, &req, userID.(string))
	if err != nil {
		h.logger.WithError(err).WithField("task_id", taskID).Error("Failed to update task")
		if err.Error() == "unauthorized to update this task" {
			c.JSON(http.StatusForbidden, gin.H{"error": err.Error()})
		} else if err.Error() == "cannot update running task" {
			c.JSON(http.StatusBadRequest, gin.H{"error": err.Error()})
		} else {
			c.JSON(http.StatusInternalServerError, gin.H{"error": "Failed to update task"})
		}
		return
	}

	c.JSON(http.StatusOK, gin.H{
		"message": "Task updated successfully",
		"data":    task,
	})
}

// DeleteTask 删除任务
func (h *TaskHandler) DeleteTask(c *gin.Context) {
	taskID := c.Param("id")
	if taskID == "" {
		c.JSON(http.StatusBadRequest, gin.H{"error": "Task ID is required"})
		return
	}

	// 从上下文中获取用户ID
	userID, exists := c.Get("user_id")
	if !exists {
		c.JSON(http.StatusUnauthorized, gin.H{"error": "User not authenticated"})
		return
	}

	ctx := c.Request.Context()
	err := h.taskManager.DeleteTask(ctx, taskID, userID.(string))
	if err != nil {
		h.logger.WithError(err).WithField("task_id", taskID).Error("Failed to delete task")
		if err.Error() == "unauthorized to delete this task" {
			c.JSON(http.StatusForbidden, gin.H{"error": err.Error()})
		} else if err.Error() == "cannot delete running task" {
			c.JSON(http.StatusBadRequest, gin.H{"error": err.Error()})
		} else {
			c.JSON(http.StatusInternalServerError, gin.H{"error": "Failed to delete task"})
		}
		return
	}

	c.JSON(http.StatusOK, gin.H{
		"message": "Task deleted successfully",
	})
}

// CancelTask 取消任务
func (h *TaskHandler) CancelTask(c *gin.Context) {
	taskID := c.Param("id")
	if taskID == "" {
		c.JSON(http.StatusBadRequest, gin.H{"error": "Task ID is required"})
		return
	}

	// 从上下文中获取用户ID
	userID, exists := c.Get("user_id")
	if !exists {
		c.JSON(http.StatusUnauthorized, gin.H{"error": "User not authenticated"})
		return
	}

	ctx := c.Request.Context()
	err := h.taskManager.CancelTask(ctx, taskID, userID.(string))
	if err != nil {
		h.logger.WithError(err).WithField("task_id", taskID).Error("Failed to cancel task")
		c.JSON(http.StatusInternalServerError, gin.H{"error": err.Error()})
		return
	}

	c.JSON(http.StatusOK, gin.H{
		"message": "Task cancelled successfully",
	})
}

// BatchDeleteTasks 批量删除任务
func (h *TaskHandler) BatchDeleteTasks(c *gin.Context) {
	var req struct {
		TaskIDs []string `json:"task_ids" binding:"required"`
	}

	if err := c.ShouldBindJSON(&req); err != nil {
		c.JSON(http.StatusBadRequest, gin.H{"error": err.Error()})
		return
	}

	// 从上下文中获取用户ID
	userID, exists := c.Get("user_id")
	if !exists {
		c.JSON(http.StatusUnauthorized, gin.H{"error": "User not authenticated"})
		return
	}

	ctx := c.Request.Context()
	err := h.taskManager.BatchDeleteTasks(ctx, req.TaskIDs, userID.(string))
	if err != nil {
		h.logger.WithError(err).Error("Failed to batch delete tasks")
		c.JSON(http.StatusInternalServerError, gin.H{"error": "Failed to batch delete tasks"})
		return
	}

	c.JSON(http.StatusOK, gin.H{
		"message": "Tasks deleted successfully",
	})
}

// BatchCancelTasks 批量取消任务
func (h *TaskHandler) BatchCancelTasks(c *gin.Context) {
	var req struct {
		TaskIDs []string `json:"task_ids" binding:"required"`
	}

	if err := c.ShouldBindJSON(&req); err != nil {
		c.JSON(http.StatusBadRequest, gin.H{"error": err.Error()})
		return
	}

	// 从上下文中获取用户ID
	userID, exists := c.Get("user_id")
	if !exists {
		c.JSON(http.StatusUnauthorized, gin.H{"error": "User not authenticated"})
		return
	}

	ctx := c.Request.Context()
	err := h.taskManager.BatchCancelTasks(ctx, req.TaskIDs, userID.(string))
	if err != nil {
		h.logger.WithError(err).Error("Failed to batch cancel tasks")
		c.JSON(http.StatusInternalServerError, gin.H{"error": "Failed to batch cancel tasks"})
		return
	}

	c.JSON(http.StatusOK, gin.H{
		"message": "Tasks cancelled successfully",
	})
}

// GetTaskStats 获取任务统计
func (h *TaskHandler) GetTaskStats(c *gin.Context) {
	// 从上下文中获取用户ID
	userID, exists := c.Get("user_id")
	if !exists {
		c.JSON(http.StatusUnauthorized, gin.H{"error": "User not authenticated"})
		return
	}

	ctx := c.Request.Context()
	tasks, err := h.taskManager.GetUserTasks(ctx, userID.(string))
	if err != nil {
		h.logger.WithError(err).Error("Failed to get user tasks")
		c.JSON(http.StatusInternalServerError, gin.H{"error": "Failed to get user tasks"})
		return
	}

	stats := &models.TaskStats{}
	for _, t := range tasks {
		stats.Total++
		switch t.Status {
		case models.TaskStatusPending:
			stats.Pending++
		case models.TaskStatusRunning:
			stats.Running++
		case models.TaskStatusCompleted:
			stats.Completed++
		case models.TaskStatusFailed:
			stats.Failed++
		case models.TaskStatusCancelled:
			stats.Cancelled++
		}
	}

	c.JSON(http.StatusOK, gin.H{
		"data": stats,
	})
}
