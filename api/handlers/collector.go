package handlers

import (
	"net/http"
	"strconv"

	"proxyManager/internal/collector"

	"github.com/gin-gonic/gin"
	"github.com/sirupsen/logrus"
)

// CollectorHandler 采集器处理器
type CollectorHandler struct {
	collector collector.Collector
	logger    *logrus.Logger
}

// NewCollectorHandler 创建采集器处理器
func NewCollectorHandler(collector collector.Collector, logger *logrus.Logger) *CollectorHandler {
	return &CollectorHandler{
		collector: collector,
		logger:    logger,
	}
}

// GetStatus 获取采集器状态
// @Summary 获取采集器状态
// @Description 获取代理采集器的运行状态和统计信息
// @Tags collector
// @Accept json
// @Produce json
// @Success 200 {object} map[string]interface{} "采集器状态"
// @Failure 500 {object} map[string]interface{} "内部错误"
// @Router /api/v1/collector/status [get]
func (h *CollectorHandler) GetStatus(c *gin.Context) {
	stats := h.collector.GetStats()
	
	status := map[string]interface{}{
		"running":           h.collector.IsRunning(),
		"last_collection":   stats.LastCollection,
		"total_sources":     stats.TotalSources,
		"success_sources":   stats.SuccessSources,
		"total_proxies":     stats.TotalProxies,
		"valid_proxies":     stats.ValidProxies,
		"duplicate_count":   stats.DuplicateCount,
		"success_rate":      0.0,
		"collection_duration": stats.Duration,
		"start_time":        stats.StartTime,
		"end_time":          stats.EndTime,
	}
	
	if stats.TotalProxies > 0 {
		status["success_rate"] = float64(stats.ValidProxies) / float64(stats.TotalProxies) * 100
	}
	
	c.JSON(http.StatusOK, gin.H{
		"success": true,
		"data":    status,
	})
}

// TriggerCollection 手动触发采集
// @Summary 手动触发代理采集
// @Description 立即执行一次代理采集任务
// @Tags collector
// @Accept json
// @Produce json
// @Success 200 {object} map[string]interface{} "采集结果"
// @Failure 400 {object} map[string]interface{} "请求错误"
// @Failure 500 {object} map[string]interface{} "内部错误"
// @Router /api/v1/collector/collect [post]
func (h *CollectorHandler) TriggerCollection(c *gin.Context) {
	h.logger.Info("Manual collection triggered")
	
	// 执行采集
	stats, err := h.collector.CollectOnce(c.Request.Context())
	if err != nil {
		h.logger.WithError(err).Error("Manual collection failed")
		c.JSON(http.StatusInternalServerError, gin.H{
			"success": false,
			"error":   "Collection failed: " + err.Error(),
		})
		return
	}
	
	successRate := 0.0
	if stats.TotalProxies > 0 {
		successRate = float64(stats.ValidProxies) / float64(stats.TotalProxies) * 100
	}
	
	result := map[string]interface{}{
		"total_sources":     stats.TotalSources,
		"success_sources":   stats.SuccessSources,
		"total_proxies":     stats.TotalProxies,
		"valid_proxies":     stats.ValidProxies,
		"duplicate_count":   stats.DuplicateCount,
		"success_rate":      successRate,
		"duration":          stats.Duration,
		"start_time":        stats.StartTime,
		"end_time":          stats.EndTime,
	}
	
	h.logger.WithFields(logrus.Fields{
		"total_proxies":   stats.TotalProxies,
		"valid_proxies":   stats.ValidProxies,
		"success_rate":    successRate,
		"duration":        stats.Duration,
	}).Info("Manual collection completed")
	
	c.JSON(http.StatusOK, gin.H{
		"success": true,
		"message": "Collection completed successfully",
		"data":    result,
	})
}

// GetStats 获取采集统计
// @Summary 获取采集统计信息
// @Description 获取详细的采集统计信息
// @Tags collector
// @Accept json
// @Produce json
// @Success 200 {object} map[string]interface{} "统计信息"
// @Failure 500 {object} map[string]interface{} "内部错误"
// @Router /api/v1/collector/stats [get]
func (h *CollectorHandler) GetStats(c *gin.Context) {
	stats := h.collector.GetStats()
	
	c.JSON(http.StatusOK, gin.H{
		"success": true,
		"data":    stats,
	})
}

// GetHistory 获取采集历史
// @Summary 获取采集历史记录
// @Description 获取最近的采集历史记录
// @Tags collector
// @Accept json
// @Produce json
// @Param limit query int false "限制数量" default(10)
// @Success 200 {object} map[string]interface{} "历史记录"
// @Failure 400 {object} map[string]interface{} "请求错误"
// @Failure 500 {object} map[string]interface{} "内部错误"
// @Router /api/v1/collector/history [get]
func (h *CollectorHandler) GetHistory(c *gin.Context) {
	limitStr := c.DefaultQuery("limit", "10")
	limit, err := strconv.Atoi(limitStr)
	if err != nil || limit <= 0 {
		c.JSON(http.StatusBadRequest, gin.H{
			"success": false,
			"error":   "Invalid limit parameter",
		})
		return
	}
	
	if limit > 100 {
		limit = 100 // 最大限制100条记录
	}
	
	// 这里需要实现历史记录获取逻辑
	// 暂时返回当前统计作为示例
	stats := h.collector.GetStats()
	history := []*collector.CollectionStats{stats}
	
	c.JSON(http.StatusOK, gin.H{
		"success": true,
		"data": map[string]interface{}{
			"history": history,
			"total":   len(history),
		},
	})
}

// StartCollector 启动采集器
// @Summary 启动采集器
// @Description 启动代理采集器服务
// @Tags collector
// @Accept json
// @Produce json
// @Success 200 {object} map[string]interface{} "启动结果"
// @Failure 400 {object} map[string]interface{} "请求错误"
// @Failure 500 {object} map[string]interface{} "内部错误"
// @Router /api/v1/collector/start [post]
func (h *CollectorHandler) StartCollector(c *gin.Context) {
	if h.collector.IsRunning() {
		c.JSON(http.StatusBadRequest, gin.H{
			"success": false,
			"error":   "Collector is already running",
		})
		return
	}
	
	if err := h.collector.Start(c.Request.Context()); err != nil {
		h.logger.WithError(err).Error("Failed to start collector")
		c.JSON(http.StatusInternalServerError, gin.H{
			"success": false,
			"error":   "Failed to start collector: " + err.Error(),
		})
		return
	}
	
	h.logger.Info("Collector started via API")
	c.JSON(http.StatusOK, gin.H{
		"success": true,
		"message": "Collector started successfully",
	})
}

// StopCollector 停止采集器
// @Summary 停止采集器
// @Description 停止代理采集器服务
// @Tags collector
// @Accept json
// @Produce json
// @Success 200 {object} map[string]interface{} "停止结果"
// @Failure 400 {object} map[string]interface{} "请求错误"
// @Failure 500 {object} map[string]interface{} "内部错误"
// @Router /api/v1/collector/stop [post]
func (h *CollectorHandler) StopCollector(c *gin.Context) {
	if !h.collector.IsRunning() {
		c.JSON(http.StatusBadRequest, gin.H{
			"success": false,
			"error":   "Collector is not running",
		})
		return
	}
	
	if err := h.collector.Stop(); err != nil {
		h.logger.WithError(err).Error("Failed to stop collector")
		c.JSON(http.StatusInternalServerError, gin.H{
			"success": false,
			"error":   "Failed to stop collector: " + err.Error(),
		})
		return
	}
	
	h.logger.Info("Collector stopped via API")
	c.JSON(http.StatusOK, gin.H{
		"success": true,
		"message": "Collector stopped successfully",
	})
}

// GetMetrics 获取采集器指标
// @Summary 获取采集器指标
// @Description 获取采集器的详细指标信息
// @Tags collector
// @Accept json
// @Produce json
// @Success 200 {object} map[string]interface{} "指标信息"
// @Failure 500 {object} map[string]interface{} "内部错误"
// @Router /api/v1/collector/metrics [get]
func (h *CollectorHandler) GetMetrics(c *gin.Context) {
	// 这里需要实现指标获取逻辑
	// 暂时返回基本信息
	stats := h.collector.GetStats()
	
	metrics := map[string]interface{}{
		"is_running":        h.collector.IsRunning(),
		"last_collection":   stats.LastCollection,
		"total_collections": 1, // 这里需要从持久化存储获取
		"avg_success_rate":  0.0,
		"avg_duration":      stats.Duration,
		"total_proxies_collected": stats.TotalProxies,
		"total_valid_proxies":     stats.ValidProxies,
	}
	
	if stats.TotalProxies > 0 {
		metrics["avg_success_rate"] = float64(stats.ValidProxies) / float64(stats.TotalProxies) * 100
	}
	
	c.JSON(http.StatusOK, gin.H{
		"success": true,
		"data":    metrics,
	})
}
