package handlers

import (
	"encoding/json"
	"net/http"

	"proxyManager/internal/repository"

	"github.com/gin-gonic/gin"
	"github.com/sirupsen/logrus"
)

// SettingsHandler 设置处理器
type SettingsHandler struct {
	repoManager repository.RepositoryManager
	logger      *logrus.Logger
}

// NewSettingsHandler 创建设置处理器
func NewSettingsHandler(repoManager repository.RepositoryManager, logger *logrus.Logger) *SettingsHandler {
	return &SettingsHandler{
		repoManager: repoManager,
		logger:      logger,
	}
}

// GetUserSettings 获取用户设置
func (h *SettingsHandler) GetUserSettings(c *gin.Context) {
	userID, exists := c.Get("user_id")
	if !exists {
		c.JSON(http.StatusUnauthorized, gin.H{"error": "User not authenticated"})
		return
	}

	ctx := c.Request.Context()
	userIDStr := userID.(string)

	// 处理超级管理员的特殊情况
	var settings map[string]interface{}
	var err error

	if userIDStr == "super_admin" {
		// 超级管理员返回默认设置
		settings = make(map[string]interface{})
	} else {
		settings, err = h.repoManager.GetUserSettings().GetAll(ctx, userIDStr)
		if err != nil {
			h.logger.WithError(err).WithField("user_id", userID).Error("Failed to get user settings")
			c.JSON(http.StatusInternalServerError, gin.H{
				"error":   "Failed to get user settings",
				"details": err.Error(),
			})
			return
		}
	}

	// 构建响应数据结构
	response := map[string]interface{}{
		"profile": map[string]interface{}{
			"username": "",
			"email":    "",
		},
		"preferences": map[string]interface{}{
			"theme":           "light",
			"language":        "zh",
			"autoRefresh":     true,
			"refreshInterval": 30,
		},
		"security": map[string]interface{}{
			"twoFactorEnabled":    false,
			"sessionTimeout":      3600,
			"passwordLastChanged": nil,
		},
	}

	// 合并用户设置
	for key, value := range settings {
		switch key {
		case "profile":
			if profileData, ok := value.(map[string]interface{}); ok {
				response["profile"] = profileData
			}
		case "preferences":
			if preferencesData, ok := value.(map[string]interface{}); ok {
				response["preferences"] = preferencesData
			}
		case "security":
			if securityData, ok := value.(map[string]interface{}); ok {
				response["security"] = securityData
			}
		default:
			response[key] = value
		}
	}

	c.JSON(http.StatusOK, gin.H{
		"success": true,
		"data":    response,
	})
}

// UpdateUserSettings 更新用户设置
func (h *SettingsHandler) UpdateUserSettings(c *gin.Context) {
	userID, exists := c.Get("user_id")
	if !exists {
		c.JSON(http.StatusUnauthorized, gin.H{"error": "User not authenticated"})
		return
	}

	var req map[string]interface{}
	if err := c.ShouldBindJSON(&req); err != nil {
		c.JSON(http.StatusBadRequest, gin.H{"error": "Invalid request format"})
		return
	}

	ctx := c.Request.Context()
	userIDStr := userID.(string)

	// 处理超级管理员的特殊情况
	if userIDStr == "super_admin" {
		// 超级管理员的设置更新只是返回成功，不实际保存
		h.logger.WithField("user_id", userID).Info("Super admin settings update (no-op)")
	} else {
		// 批量更新设置
		if err := h.repoManager.GetUserSettings().SetMultiple(ctx, userIDStr, req); err != nil {
			h.logger.WithError(err).WithField("user_id", userID).Error("Failed to update user settings")
			c.JSON(http.StatusInternalServerError, gin.H{"error": "Failed to update user settings"})
			return
		}
		h.logger.WithField("user_id", userID).Info("User settings updated successfully")
	}

	h.logger.WithField("user_id", userID).Info("User settings updated successfully")

	c.JSON(http.StatusOK, gin.H{
		"success": true,
		"message": "Settings updated successfully",
	})
}

// GetProxySettings 获取代理设置
func (h *SettingsHandler) GetProxySettings(c *gin.Context) {
	userID, exists := c.Get("user_id")
	if !exists {
		c.JSON(http.StatusUnauthorized, gin.H{"error": "User not authenticated"})
		return
	}

	ctx := c.Request.Context()
	userIDStr := userID.(string)

	var proxySettings interface{}
	var err error

	if userIDStr == "super_admin" {
		// 超级管理员返回默认代理设置
		proxySettings = nil
	} else {
		proxySettings, err = h.repoManager.GetUserSettings().Get(ctx, userIDStr, "proxy")
		if err != nil {
			h.logger.WithError(err).WithField("user_id", userID).Error("Failed to get proxy settings")
			c.JSON(http.StatusInternalServerError, gin.H{"error": "Failed to get proxy settings"})
			return
		}
	}

	// 默认代理设置
	defaultSettings := map[string]interface{}{
		"defaultSettings": map[string]interface{}{
			"timeout":    30,
			"maxRetries": 3,
			"strategy":   "round_robin",
		},
		"healthCheck": map[string]interface{}{
			"enabled":  true,
			"interval": 300,
			"timeout":  10,
		},
	}

	if proxySettings != nil {
		if settingsMap, ok := proxySettings.(map[string]interface{}); ok {
			// 合并用户设置和默认设置
			for key, value := range settingsMap {
				defaultSettings[key] = value
			}
		}
	}

	c.JSON(http.StatusOK, gin.H{
		"success": true,
		"data":    defaultSettings,
	})
}

// UpdateProxySettings 更新代理设置
func (h *SettingsHandler) UpdateProxySettings(c *gin.Context) {
	userID, exists := c.Get("user_id")
	if !exists {
		c.JSON(http.StatusUnauthorized, gin.H{"error": "User not authenticated"})
		return
	}

	var req map[string]interface{}
	if err := c.ShouldBindJSON(&req); err != nil {
		c.JSON(http.StatusBadRequest, gin.H{"error": "Invalid request format"})
		return
	}

	ctx := c.Request.Context()

	// 更新代理设置
	if err := h.repoManager.GetUserSettings().Set(ctx, userID.(string), "proxy", req); err != nil {
		h.logger.WithError(err).WithField("user_id", userID).Error("Failed to update proxy settings")
		c.JSON(http.StatusInternalServerError, gin.H{"error": "Failed to update proxy settings"})
		return
	}

	h.logger.WithField("user_id", userID).Info("Proxy settings updated successfully")

	c.JSON(http.StatusOK, gin.H{
		"success": true,
		"message": "Proxy settings updated successfully",
	})
}

// ResetSettings 重置设置到默认值
func (h *SettingsHandler) ResetSettings(c *gin.Context) {
	userID, exists := c.Get("user_id")
	if !exists {
		c.JSON(http.StatusUnauthorized, gin.H{"error": "User not authenticated"})
		return
	}

	ctx := c.Request.Context()

	// 删除所有用户设置
	if err := h.repoManager.GetUserSettings().DeleteAll(ctx, userID.(string)); err != nil {
		h.logger.WithError(err).WithField("user_id", userID).Error("Failed to reset user settings")
		c.JSON(http.StatusInternalServerError, gin.H{"error": "Failed to reset settings"})
		return
	}

	h.logger.WithField("user_id", userID).Info("User settings reset successfully")

	c.JSON(http.StatusOK, gin.H{
		"success": true,
		"message": "Settings reset successfully",
	})
}

// ImportSettings 导入设置
func (h *SettingsHandler) ImportSettings(c *gin.Context) {
	userID, exists := c.Get("user_id")
	if !exists {
		c.JSON(http.StatusUnauthorized, gin.H{"error": "User not authenticated"})
		return
	}

	var req map[string]interface{}
	if err := c.ShouldBindJSON(&req); err != nil {
		c.JSON(http.StatusBadRequest, gin.H{"error": "Invalid request format"})
		return
	}

	ctx := c.Request.Context()

	// 批量导入设置
	if err := h.repoManager.GetUserSettings().SetMultiple(ctx, userID.(string), req); err != nil {
		h.logger.WithError(err).WithField("user_id", userID).Error("Failed to import user settings")
		c.JSON(http.StatusInternalServerError, gin.H{"error": "Failed to import settings"})
		return
	}

	h.logger.WithField("user_id", userID).Info("User settings imported successfully")

	c.JSON(http.StatusOK, gin.H{
		"success": true,
		"message": "Settings imported successfully",
	})
}

// ExportSettings 导出设置
func (h *SettingsHandler) ExportSettings(c *gin.Context) {
	userID, exists := c.Get("user_id")
	if !exists {
		c.JSON(http.StatusUnauthorized, gin.H{"error": "User not authenticated"})
		return
	}

	ctx := c.Request.Context()
	settings, err := h.repoManager.GetUserSettings().GetAll(ctx, userID.(string))
	if err != nil {
		h.logger.WithError(err).WithField("user_id", userID).Error("Failed to export user settings")
		c.JSON(http.StatusInternalServerError, gin.H{"error": "Failed to export settings"})
		return
	}

	// 设置响应头为JSON文件下载
	c.Header("Content-Type", "application/json")
	c.Header("Content-Disposition", "attachment; filename=settings.json")

	// 直接输出JSON
	settingsJSON, _ := json.MarshalIndent(settings, "", "  ")
	c.Data(http.StatusOK, "application/json", settingsJSON)
}
