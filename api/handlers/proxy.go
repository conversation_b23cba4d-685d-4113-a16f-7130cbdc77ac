package handlers

import (
	"fmt"
	"net/http"
	"strconv"

	"proxyManager/internal/models"
	"proxyManager/internal/proxy"

	"github.com/gin-gonic/gin"
	"github.com/sirupsen/logrus"
)

// ProxyHandler 代理处理器
type ProxyHandler struct {
	proxyManager *proxy.Manager
	logger       *logrus.Logger
}

// NewProxyHandler 创建代理处理器
func NewProxyHandler(proxyManager *proxy.Manager, logger *logrus.Logger) *ProxyHandler {
	return &ProxyHandler{
		proxyManager: proxyManager,
		logger:       logger,
	}
}

// convertToProxyResponse 将 Proxy 模型转换为 ProxyResponse
func convertToProxyResponse(p *models.Proxy) models.ProxyResponse {
	response := models.ProxyResponse{
		ID:                    p.ID,
		Host:                  p.Host,
		Port:                  p.Port,
		Type:                  p.Type,
		Status:                p.Status,
		UseCount:              p.UseCount,
		Weight:                p.Weight,
		CountryCode:           p.CountryCode,
		CityName:              p.CityName,
		ASNName:               p.ASNName,
		HighCountryConfidence: p.HighCountryConfidence,
	}

	// 处理可能为 nil 的指针字段
	if p.LastCheck != nil {
		response.LastCheck = *p.LastCheck
	}
	if p.LastSuccess != nil {
		response.LastSuccess = *p.LastSuccess
	}
	if p.ResponseTime != nil {
		response.ResponseTime = *p.ResponseTime
	}
	if p.ASNNumber != nil {
		response.ASNNumber = *p.ASNNumber
	}

	return response
}

// GetProxies 获取代理列表
func (h *ProxyHandler) GetProxies(c *gin.Context) {
	ctx := c.Request.Context()

	// 解析分页和筛选参数
	var req models.ProxyListRequest
	if err := c.ShouldBindQuery(&req); err != nil {
		h.logger.WithError(err).Error("Failed to bind query parameters")
		c.JSON(http.StatusBadRequest, gin.H{"error": "Invalid query parameters"})
		return
	}

	// 调用Manager层的分页方法
	result, err := h.proxyManager.GetProxiesWithPagination(ctx, &req)
	if err != nil {
		h.logger.WithError(err).Error("Failed to get proxies with pagination")
		c.JSON(http.StatusInternalServerError, gin.H{"error": "Failed to get proxies"})
		return
	}

	c.JSON(http.StatusOK, result)
}

// GetAvailableProxies 获取可用代理
func (h *ProxyHandler) GetAvailableProxies(c *gin.Context) {
	ctx := c.Request.Context()

	proxies, err := h.proxyManager.GetAvailableProxies(ctx)
	if err != nil {
		h.logger.WithError(err).Error("Failed to get available proxies")
		c.JSON(http.StatusInternalServerError, gin.H{"error": "Failed to get available proxies"})
		return
	}

	var responses []models.ProxyResponse
	for _, p := range proxies {
		responses = append(responses, convertToProxyResponse(p))
	}

	c.JSON(http.StatusOK, gin.H{
		"data":  responses,
		"total": len(responses),
	})
}

// AddProxy 添加代理
func (h *ProxyHandler) AddProxy(c *gin.Context) {
	fmt.Println("DEBUG: AddProxy called")

	var req models.ProxyRequest
	if err := c.ShouldBindJSON(&req); err != nil {
		fmt.Printf("DEBUG: Failed to bind JSON: %v\n", err)
		h.logger.WithError(err).Error("Failed to bind JSON request")
		c.JSON(http.StatusBadRequest, gin.H{"error": err.Error()})
		return
	}

	fmt.Printf("DEBUG: Request parsed successfully: %+v\n", req)

	h.logger.WithFields(map[string]interface{}{
		"host": req.Host,
		"port": req.Port,
		"type": req.Type,
	}).Info("Adding proxy")

	ctx := c.Request.Context()
	proxy, err := h.proxyManager.AddProxy(ctx, &req)
	if err != nil {
		fmt.Printf("DEBUG: AddProxy failed: %v\n", err)
		h.logger.WithError(err).WithFields(map[string]interface{}{
			"host": req.Host,
			"port": req.Port,
			"type": req.Type,
		}).Error("Failed to add proxy")
		c.JSON(http.StatusInternalServerError, gin.H{"error": "Failed to add proxy"})
		return
	}

	fmt.Printf("DEBUG: Proxy added successfully: %+v\n", proxy)
	h.logger.WithField("proxy_id", proxy.ID).Info("Proxy added successfully")
	c.JSON(http.StatusCreated, gin.H{
		"message": "Proxy added successfully",
		"data":    proxy,
	})
}

// DeleteProxy 删除代理
func (h *ProxyHandler) DeleteProxy(c *gin.Context) {
	proxyID := c.Param("id")
	if proxyID == "" {
		c.JSON(http.StatusBadRequest, gin.H{"error": "Proxy ID is required"})
		return
	}

	ctx := c.Request.Context()
	err := h.proxyManager.DeleteProxy(ctx, proxyID)
	if err != nil {
		h.logger.WithError(err).WithField("proxy_id", proxyID).Error("Failed to delete proxy")
		c.JSON(http.StatusInternalServerError, gin.H{"error": "Failed to delete proxy"})
		return
	}

	c.JSON(http.StatusOK, gin.H{
		"message": "Proxy deleted successfully",
	})
}

// GetProxy 获取单个代理
func (h *ProxyHandler) GetProxy(c *gin.Context) {
	proxyID := c.Param("id")
	if proxyID == "" {
		c.JSON(http.StatusBadRequest, gin.H{"error": "Proxy ID is required"})
		return
	}

	ctx := c.Request.Context()
	// 直接从数据库获取代理
	targetProxy, err := h.proxyManager.GetProxyByID(ctx, proxyID)
	if err != nil {
		h.logger.WithError(err).WithField("proxy_id", proxyID).Error("Failed to get proxy")
		c.JSON(http.StatusInternalServerError, gin.H{"error": "Failed to get proxy"})
		return
	}

	if targetProxy == nil {
		c.JSON(http.StatusNotFound, gin.H{"error": "Proxy not found"})
		return
	}

	c.JSON(http.StatusOK, gin.H{
		"data": targetProxy,
	})
}

// HealthCheck 健康检查
func (h *ProxyHandler) HealthCheck(c *gin.Context) {
	proxyID := c.Param("id")
	if proxyID == "" {
		c.JSON(http.StatusBadRequest, gin.H{"error": "Proxy ID is required"})
		return
	}

	ctx := c.Request.Context()
	// 直接从数据库获取代理
	targetProxy, err := h.proxyManager.GetProxyByID(ctx, proxyID)
	if err != nil {
		h.logger.WithError(err).WithField("proxy_id", proxyID).Error("Failed to get proxy for health check")
		c.JSON(http.StatusInternalServerError, gin.H{"error": "Failed to get proxy"})
		return
	}

	if targetProxy == nil {
		c.JSON(http.StatusNotFound, gin.H{"error": "Proxy not found"})
		return
	}

	check := h.proxyManager.HealthCheck(ctx, targetProxy)
	c.JSON(http.StatusOK, gin.H{
		"data": check,
	})
}

// GetProxyStats 获取代理统计
func (h *ProxyHandler) GetProxyStats(c *gin.Context) {
	ctx := c.Request.Context()

	// 使用分页方法获取所有代理（从PostgreSQL）以确保获取准确的统计数据
	req := &models.ProxyListRequest{
		PaginationParams: models.PaginationParams{
			Page:  1,
			Limit: 100000, // 设置一个很大的限制以获取所有代理
		},
	}
	
	result, err := h.proxyManager.GetProxiesWithPagination(ctx, req)
	if err != nil {
		h.logger.WithError(err).Error("Failed to get proxy stats from database")
		c.JSON(http.StatusInternalServerError, gin.H{"error": "Failed to get proxy stats"})
		return
	}

	stats := &models.ProxyStats{}
	
	// 使用总数直接从分页结果获取，避免计数不一致
	stats.Total = int(result.Total)
	
	// 遍历返回的代理数据进行分类统计
	if proxyResponses, ok := result.Data.([]models.ProxyResponse); ok {
		for _, p := range proxyResponses {
			switch p.Status {
			case models.ProxyStatusActive:
				stats.Active++
			case models.ProxyStatusInactive:
				stats.Inactive++
			case models.ProxyStatusFailed:
				stats.Failed++
			}

			switch p.Type {
			case models.ProxyTypeHTTP:
				stats.HTTP++
			case models.ProxyTypeHTTPS:
				stats.HTTPS++
			case models.ProxyTypeSOCKS5:
				stats.SOCKS5++
			}
		}
	}

	h.logger.WithFields(map[string]interface{}{
		"total":    stats.Total,
		"active":   stats.Active,
		"inactive": stats.Inactive,
		"failed":   stats.Failed,
		"http":     stats.HTTP,
		"https":    stats.HTTPS,
		"socks5":   stats.SOCKS5,
	}).Info("Proxy stats calculated from database")

	c.JSON(http.StatusOK, gin.H{
		"data": stats,
	})
}

// GetProxyByStrategy 根据策略获取代理
func (h *ProxyHandler) GetProxyByStrategy(c *gin.Context) {
	strategy := c.Query("strategy")
	if strategy == "" {
		strategy = "round_robin"
	}

	ctx := c.Request.Context()
	proxy, err := h.proxyManager.GetProxy(ctx, proxy.Strategy(strategy))
	if err != nil {
		h.logger.WithError(err).WithField("strategy", strategy).Error("Failed to get proxy by strategy")
		c.JSON(http.StatusInternalServerError, gin.H{"error": "Failed to get proxy"})
		return
	}

	c.JSON(http.StatusOK, gin.H{
		"data": proxy,
	})
}

// GetProxiesByLocation 按地理位置获取代理
func (h *ProxyHandler) GetProxiesByLocation(c *gin.Context) {
	countryCode := c.Query("country_code")
	cityName := c.Query("city_name")

	ctx := c.Request.Context()
	proxies, err := h.proxyManager.GetProxiesByLocation(ctx, countryCode, cityName)
	if err != nil {
		h.logger.WithError(err).Error("Failed to get proxies by location")
		c.JSON(http.StatusInternalServerError, gin.H{"error": "Failed to get proxies by location"})
		return
	}

	// 转换为响应格式
	var responses []models.ProxyResponse
	for _, p := range proxies {
		responses = append(responses, convertToProxyResponse(p))
	}

	c.JSON(http.StatusOK, gin.H{
		"data":  responses,
		"total": len(responses),
		"filters": gin.H{
			"country_code": countryCode,
			"city_name":    cityName,
		},
	})
}

// GetProxyLocationStats 获取代理地理位置统计
func (h *ProxyHandler) GetProxyLocationStats(c *gin.Context) {
	ctx := c.Request.Context()

	stats, err := h.proxyManager.GetProxyLocationStats(ctx)
	if err != nil {
		h.logger.WithError(err).Error("Failed to get proxy location stats")
		c.JSON(http.StatusInternalServerError, gin.H{"error": "Failed to get proxy location stats"})
		return
	}

	c.JSON(http.StatusOK, gin.H{
		"data": stats,
	})
}

// GroupProxiesByLocation 按地理位置分组代理
func (h *ProxyHandler) GroupProxiesByLocation(c *gin.Context) {
	groupBy := c.Query("group_by")
	if groupBy == "" {
		groupBy = "country"
	}

	// 验证groupBy参数
	if groupBy != "country" && groupBy != "city" {
		c.JSON(http.StatusBadRequest, gin.H{"error": "Invalid group_by parameter. Must be 'country' or 'city'"})
		return
	}

	ctx := c.Request.Context()
	groups, err := h.proxyManager.GroupProxiesByLocation(ctx, groupBy)
	if err != nil {
		h.logger.WithError(err).Error("Failed to group proxies by location")
		c.JSON(http.StatusInternalServerError, gin.H{"error": "Failed to group proxies by location"})
		return
	}

	c.JSON(http.StatusOK, gin.H{
		"data":     groups,
		"group_by": groupBy,
		"total":    len(groups),
	})
}

// BatchHealthCheck 批量健康检查
func (h *ProxyHandler) BatchHealthCheck(c *gin.Context) {
	var req struct {
		ProxyIDs []string `json:"proxy_ids" binding:"required"`
	}

	if err := c.ShouldBindJSON(&req); err != nil {
		c.JSON(http.StatusBadRequest, gin.H{"error": err.Error()})
		return
	}

	ctx := c.Request.Context()

	// 从数据库获取指定的代理
	var targetProxies []*models.Proxy
	for _, id := range req.ProxyIDs {
		proxy, err := h.proxyManager.GetProxyByID(ctx, id)
		if err != nil {
			h.logger.WithError(err).WithField("proxy_id", id).Error("Failed to get proxy for batch health check")
			continue
		}
		if proxy != nil {
			targetProxies = append(targetProxies, proxy)
		}
	}

	if len(targetProxies) == 0 {
		c.JSON(http.StatusBadRequest, gin.H{"error": "No valid proxy IDs provided"})
		return
	}

	// 启动并发批量健康检查
	results, err := h.proxyManager.BatchHealthCheck(ctx, targetProxies)
	if err != nil {
		h.logger.WithError(err).Error("Batch health check failed")
		c.JSON(http.StatusInternalServerError, gin.H{"error": "Batch health check failed"})
		return
	}

	h.logger.WithField("count", len(targetProxies)).Info("Batch health check completed")

	c.JSON(http.StatusOK, gin.H{
		"message": "Batch health check completed",
		"data":    results,
		"total":   len(results),
	})
}

// HealthCheckAll 检查所有代理
func (h *ProxyHandler) HealthCheckAll(c *gin.Context) {
	ctx := c.Request.Context()

	// 使用分页方法获取所有代理（从PostgreSQL）
	req := &models.ProxyListRequest{
		PaginationParams: models.PaginationParams{
			Page:  1,
			Limit: 100000, // 设置一个很大的限制以获取所有代理
		},
	}
	
	result, err := h.proxyManager.GetProxiesWithPagination(ctx, req)
	if err != nil {
		h.logger.WithError(err).Error("Failed to get all proxies for health check all")
		c.JSON(http.StatusInternalServerError, gin.H{"error": "Failed to get proxies"})
		return
	}

	if result.Total == 0 {
		c.JSON(http.StatusOK, gin.H{
			"message": "No proxies to check",
			"data":    []models.ProxyHealthCheck{},
			"total":   0,
		})
		return
	}

	// 将ProxyResponse转换为Proxy对象
	var allProxies []*models.Proxy
	if proxyResponses, ok := result.Data.([]models.ProxyResponse); ok {
		for _, pr := range proxyResponses {
			// 通过ID从数据库获取完整的Proxy对象
			proxy, err := h.proxyManager.GetProxyByID(ctx, pr.ID)
			if err != nil {
				h.logger.WithError(err).WithField("proxy_id", pr.ID).Error("Failed to get proxy for health check all")
				continue
			}
			if proxy != nil {
				allProxies = append(allProxies, proxy)
			}
		}
	}

	if len(allProxies) == 0 {
		c.JSON(http.StatusOK, gin.H{
			"message": "No valid proxies to check",
			"data":    []models.ProxyHealthCheck{},
			"total":   0,
		})
		return
	}

	// 启动所有代理的并发健康检查
	results, err := h.proxyManager.BatchHealthCheck(ctx, allProxies)
	if err != nil {
		h.logger.WithError(err).Error("Health check all failed")
		c.JSON(http.StatusInternalServerError, gin.H{"error": "Health check all failed"})
		return
	}

	h.logger.WithField("count", len(allProxies)).Info("Health check all completed")

	c.JSON(http.StatusOK, gin.H{
		"message": "Health check all completed",
		"data":    results,
		"total":   len(results),
	})
}

// BatchImportProxies 批量导入代理（仅管理员）
func (h *ProxyHandler) BatchImportProxies(c *gin.Context) {
	// 检查用户角色（支持 super admin 和 admin）
	role, exists := c.Get("role")
	if !exists {
		c.JSON(http.StatusUnauthorized, gin.H{"error": "User role not found"})
		return
	}

	userRole, ok := role.(models.UserRole)
	if !ok {
		c.JSON(http.StatusInternalServerError, gin.H{"error": "Invalid role type"})
		return
	}

	// 只允许 super admin 和 admin 访问
	if userRole != models.UserRoleSuperAdmin && userRole != models.UserRoleAdmin {
		c.JSON(http.StatusForbidden, gin.H{"error": "Forbidden: admin or super admin only"})
		return
	}

	var reqs []*models.ProxyRequest
	if err := c.ShouldBindJSON(&reqs); err != nil {
		c.JSON(http.StatusBadRequest, gin.H{"error": err.Error()})
		return
	}

	ctx := c.Request.Context()
	success, failed, errors := h.proxyManager.BatchAddProxies(ctx, reqs)

	h.logger.WithFields(map[string]interface{}{
		"success": success,
		"failed":  failed,
	}).Info("Batch import proxies")

	c.JSON(http.StatusOK, gin.H{
		"message": "Batch import completed",
		"success": success,
		"failed":  failed,
		"errors":  errors,
	})
}

// AssessProxyQuality 评估单个代理质量
func (h *ProxyHandler) AssessProxyQuality(c *gin.Context) {
	proxyID := c.Param("id")
	if proxyID == "" {
		c.JSON(http.StatusBadRequest, gin.H{"error": "Proxy ID is required"})
		return
	}

	ctx := c.Request.Context()
	score, err := h.proxyManager.AssessProxyQuality(ctx, proxyID)
	if err != nil {
		h.logger.WithError(err).WithField("proxy_id", proxyID).Error("Failed to assess proxy quality")
		c.JSON(http.StatusInternalServerError, gin.H{"error": "Failed to assess proxy quality"})
		return
	}

	c.JSON(http.StatusOK, gin.H{
		"message": "Quality assessment completed",
		"data":    score,
	})
}

// BatchAssessQuality 批量评估代理质量
func (h *ProxyHandler) BatchAssessQuality(c *gin.Context) {
	var req struct {
		ProxyIDs []string `json:"proxy_ids" binding:"required"`
	}

	if err := c.ShouldBindJSON(&req); err != nil {
		c.JSON(http.StatusBadRequest, gin.H{"error": err.Error()})
		return
	}

	if len(req.ProxyIDs) == 0 {
		c.JSON(http.StatusBadRequest, gin.H{"error": "At least one proxy ID is required"})
		return
	}

	ctx := c.Request.Context()
	err := h.proxyManager.BatchAssessQuality(ctx, req.ProxyIDs)
	if err != nil {
		h.logger.WithError(err).Error("Failed to batch assess proxy quality")
		c.JSON(http.StatusInternalServerError, gin.H{"error": "Failed to batch assess proxy quality"})
		return
	}

	c.JSON(http.StatusOK, gin.H{
		"message": "Batch quality assessment completed",
		"count":   len(req.ProxyIDs),
	})
}

// GetProxiesByQuality 根据质量评分获取代理
func (h *ProxyHandler) GetProxiesByQuality(c *gin.Context) {
	minScore := 0.0
	maxScore := 1.0

	if minScoreStr := c.Query("min_score"); minScoreStr != "" {
		if parsed, err := strconv.ParseFloat(minScoreStr, 64); err == nil && parsed >= 0 && parsed <= 1 {
			minScore = parsed
		}
	}

	if maxScoreStr := c.Query("max_score"); maxScoreStr != "" {
		if parsed, err := strconv.ParseFloat(maxScoreStr, 64); err == nil && parsed >= 0 && parsed <= 1 {
			maxScore = parsed
		}
	}

	ctx := c.Request.Context()
	proxies, err := h.proxyManager.GetProxiesByQualityRange(ctx, minScore, maxScore)
	if err != nil {
		h.logger.WithError(err).Error("Failed to get proxies by quality range")
		c.JSON(http.StatusInternalServerError, gin.H{"error": "Failed to get proxies by quality range"})
		return
	}

	// 转换为响应格式
	var responses []models.ProxyResponse
	for _, p := range proxies {
		responses = append(responses, convertToProxyResponse(p))
	}

	c.JSON(http.StatusOK, gin.H{
		"data":  responses,
		"total": len(responses),
		"filters": gin.H{
			"min_score": minScore,
			"max_score": maxScore,
		},
	})
}

// GetTopQualityProxies 获取质量最高的代理
func (h *ProxyHandler) GetTopQualityProxies(c *gin.Context) {
	limit := 10 // 默认返回10个

	if limitStr := c.Query("limit"); limitStr != "" {
		if parsed, err := strconv.Atoi(limitStr); err == nil && parsed > 0 && parsed <= 100 {
			limit = parsed
		}
	}

	ctx := c.Request.Context()
	proxies, err := h.proxyManager.GetTopQualityProxies(ctx, limit)
	if err != nil {
		h.logger.WithError(err).Error("Failed to get top quality proxies")
		c.JSON(http.StatusInternalServerError, gin.H{"error": "Failed to get top quality proxies"})
		return
	}

	// 转换为响应格式
	var responses []models.ProxyResponse
	for _, p := range proxies {
		responses = append(responses, convertToProxyResponse(p))
	}

	c.JSON(http.StatusOK, gin.H{
		"data":  responses,
		"total": len(responses),
		"limit": limit,
	})
}

// GetProxiesByTags 根据标签获取代理
func (h *ProxyHandler) GetProxiesByTags(c *gin.Context) {
	tags := c.QueryArray("tags")
	if len(tags) == 0 {
		c.JSON(http.StatusBadRequest, gin.H{"error": "At least one tag is required"})
		return
	}

	ctx := c.Request.Context()
	proxies, err := h.proxyManager.GetProxyByTags(ctx, tags)
	if err != nil {
		h.logger.WithError(err).Error("Failed to get proxies by tags")
		c.JSON(http.StatusInternalServerError, gin.H{"error": "Failed to get proxies by tags"})
		return
	}

	// 转换为响应格式
	var responses []models.ProxyResponse
	for _, p := range proxies {
		responses = append(responses, convertToProxyResponse(p))
	}

	c.JSON(http.StatusOK, gin.H{
		"data":  responses,
		"total": len(responses),
		"tags":  tags,
	})
}

// GetProxiesByScenario 根据场景获取代理
func (h *ProxyHandler) GetProxiesByScenario(c *gin.Context) {
	scenario := c.Query("scenario")
	if scenario == "" {
		c.JSON(http.StatusBadRequest, gin.H{"error": "Scenario is required"})
		return
	}

	ctx := c.Request.Context()
	proxies, err := h.proxyManager.GetProxyByScenario(ctx, scenario)
	if err != nil {
		h.logger.WithError(err).Error("Failed to get proxies by scenario")
		c.JSON(http.StatusInternalServerError, gin.H{"error": "Failed to get proxies by scenario"})
		return
	}

	// 转换为响应格式
	var responses []models.ProxyResponse
	for _, p := range proxies {
		responses = append(responses, convertToProxyResponse(p))
	}

	c.JSON(http.StatusOK, gin.H{
		"data":     responses,
		"total":    len(responses),
		"scenario": scenario,
	})
}

// GetProxyWithSmartRouting 智能路由获取代理
func (h *ProxyHandler) GetProxyWithSmartRouting(c *gin.Context) {
	var filter models.ProxyFilterByTags

	// 从查询参数获取筛选条件
	if tags := c.QueryArray("tags"); len(tags) > 0 {
		filter.Tags = tags
	}

	if scenario := c.Query("scenario"); scenario != "" {
		filter.Scenario = scenario
	}

	if strategy := c.Query("strategy"); strategy != "" {
		filter.Strategy = strategy
	}

	ctx := c.Request.Context()
	proxy, err := h.proxyManager.GetProxyWithSmartRouting(ctx, &filter)
	if err != nil {
		h.logger.WithError(err).Error("Failed to get proxy with smart routing")
		c.JSON(http.StatusInternalServerError, gin.H{"error": "Failed to get proxy with smart routing"})
		return
	}

	response := convertToProxyResponse(proxy)
	c.JSON(http.StatusOK, gin.H{
		"data":   response,
		"filter": filter,
	})
}
