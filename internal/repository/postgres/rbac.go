package postgres

import (
	"context"
	"database/sql"
	"fmt"
	"strings"
	"time"

	"github.com/jmoiron/sqlx"
	"github.com/sirupsen/logrus"

	"proxyManager/internal/models"
	"proxyManager/internal/repository"
)

// RBACRepository PostgreSQL RBAC仓储实现
type RBACRepository struct {
	db     *sqlx.DB
	logger *logrus.Logger
}

// NewRBACRepository 创建RBAC仓储
func NewRBACRepository(db *sqlx.DB, logger *logrus.Logger) repository.RBACRepository {
	return &RBACRepository{
		db:     db,
		logger: logger,
	}
}

// CreatePermission 创建权限
func (r *RBACRepository) CreatePermission(ctx context.Context, permission *models.Permission) error {
	var query string
	var err error

	if permission.ID == "" {
		query = `
			INSERT INTO permissions (name, resource, action, description)
			VALUES ($1, $2, $3, $4)
			RETURNING id`
		err = r.db.QueryRowContext(ctx, query,
			permission.Name, permission.Resource, permission.Action, permission.Description).Scan(&permission.ID)
	} else {
		query = `
			INSERT INTO permissions (id, name, resource, action, description)
			VALUES ($1, $2, $3, $4, $5)`
		_, err = r.db.ExecContext(ctx, query,
			permission.ID, permission.Name, permission.Resource, permission.Action, permission.Description)
	}

	if err != nil {
		r.logger.WithError(err).Error("Failed to create permission")
		return fmt.Errorf("failed to create permission: %w", err)
	}

	return nil
}

// GetPermissionByID 根据ID获取权限
func (r *RBACRepository) GetPermissionByID(ctx context.Context, id string) (*models.Permission, error) {
	var permission models.Permission
	query := `SELECT * FROM permissions WHERE id = $1`

	err := r.db.GetContext(ctx, &permission, query, id)
	if err != nil {
		if err == sql.ErrNoRows {
			return nil, nil
		}
		r.logger.WithError(err).WithField("permission_id", id).Error("Failed to get permission by ID")
		return nil, fmt.Errorf("failed to get permission by ID: %w", err)
	}

	return &permission, nil
}

// GetPermissionByName 根据名称获取权限
func (r *RBACRepository) GetPermissionByName(ctx context.Context, name string) (*models.Permission, error) {
	var permission models.Permission
	query := `SELECT * FROM permissions WHERE name = $1`

	err := r.db.GetContext(ctx, &permission, query, name)
	if err != nil {
		if err == sql.ErrNoRows {
			return nil, nil
		}
		r.logger.WithError(err).WithField("permission_name", name).Error("Failed to get permission by name")
		return nil, fmt.Errorf("failed to get permission by name: %w", err)
	}

	return &permission, nil
}

// ListPermissions 获取所有权限
func (r *RBACRepository) ListPermissions(ctx context.Context) ([]*models.Permission, error) {
	query := `SELECT * FROM permissions ORDER BY resource, action`

	var permissions []*models.Permission
	err := r.db.SelectContext(ctx, &permissions, query)
	if err != nil {
		r.logger.WithError(err).Error("Failed to list permissions")
		return nil, fmt.Errorf("failed to list permissions: %w", err)
	}

	return permissions, nil
}

// UpdatePermission 更新权限
func (r *RBACRepository) UpdatePermission(ctx context.Context, permission *models.Permission) error {
	permission.UpdatedAt = time.Now()

	query := `
		UPDATE permissions SET
			name = $2, resource = $3, action = $4, description = $5, updated_at = $6
		WHERE id = $1`

	result, err := r.db.ExecContext(ctx, query,
		permission.ID, permission.Name, permission.Resource, permission.Action,
		permission.Description, permission.UpdatedAt)
	if err != nil {
		r.logger.WithError(err).WithField("permission_id", permission.ID).Error("Failed to update permission")
		return fmt.Errorf("failed to update permission: %w", err)
	}

	rowsAffected, err := result.RowsAffected()
	if err != nil {
		return fmt.Errorf("failed to get rows affected: %w", err)
	}

	if rowsAffected == 0 {
		return fmt.Errorf("permission not found")
	}

	return nil
}

// DeletePermission 删除权限
func (r *RBACRepository) DeletePermission(ctx context.Context, id string) error {
	// 先删除相关的角色权限关联
	_, err := r.db.ExecContext(ctx, "DELETE FROM role_permissions WHERE permission_id = $1", id)
	if err != nil {
		r.logger.WithError(err).WithField("permission_id", id).Error("Failed to delete role permissions")
		return fmt.Errorf("failed to delete role permissions: %w", err)
	}

	// 删除用户权限关联
	_, err = r.db.ExecContext(ctx, "DELETE FROM user_permissions WHERE permission_id = $1", id)
	if err != nil {
		r.logger.WithError(err).WithField("permission_id", id).Error("Failed to delete user permissions")
		return fmt.Errorf("failed to delete user permissions: %w", err)
	}

	// 删除权限
	result, err := r.db.ExecContext(ctx, "DELETE FROM permissions WHERE id = $1", id)
	if err != nil {
		r.logger.WithError(err).WithField("permission_id", id).Error("Failed to delete permission")
		return fmt.Errorf("failed to delete permission: %w", err)
	}

	rowsAffected, err := result.RowsAffected()
	if err != nil {
		return fmt.Errorf("failed to get rows affected: %w", err)
	}

	if rowsAffected == 0 {
		return fmt.Errorf("permission not found")
	}

	return nil
}

// CreateRole 创建角色
func (r *RBACRepository) CreateRole(ctx context.Context, role *models.Role) error {
	var query string
	var err error

	if role.ID == "" {
		query = `
			INSERT INTO roles (name, display_name, description, is_system, created_by)
			VALUES ($1, $2, $3, $4, $5)
			RETURNING id`
		err = r.db.QueryRowContext(ctx, query,
			role.Name, role.DisplayName, role.Description, role.IsSystem, role.CreatedBy).Scan(&role.ID)
	} else {
		query = `
			INSERT INTO roles (id, name, display_name, description, is_system, created_by)
			VALUES ($1, $2, $3, $4, $5, $6)`
		_, err = r.db.ExecContext(ctx, query,
			role.ID, role.Name, role.DisplayName, role.Description, role.IsSystem, role.CreatedBy)
	}

	if err != nil {
		r.logger.WithError(err).Error("Failed to create role")
		return fmt.Errorf("failed to create role: %w", err)
	}

	return nil
}

// GetRoleByID 根据ID获取角色
func (r *RBACRepository) GetRoleByID(ctx context.Context, id string) (*models.Role, error) {
	var role models.Role
	query := `SELECT * FROM roles WHERE id = $1`

	err := r.db.GetContext(ctx, &role, query, id)
	if err != nil {
		if err == sql.ErrNoRows {
			return nil, nil
		}
		r.logger.WithError(err).WithField("role_id", id).Error("Failed to get role by ID")
		return nil, fmt.Errorf("failed to get role by ID: %w", err)
	}

	return &role, nil
}

// GetRoleByName 根据名称获取角色
func (r *RBACRepository) GetRoleByName(ctx context.Context, name string) (*models.Role, error) {
	var role models.Role
	query := `SELECT * FROM roles WHERE name = $1`

	err := r.db.GetContext(ctx, &role, query, name)
	if err != nil {
		if err == sql.ErrNoRows {
			return nil, nil
		}
		r.logger.WithError(err).WithField("role_name", name).Error("Failed to get role by name")
		return nil, fmt.Errorf("failed to get role by name: %w", err)
	}

	return &role, nil
}

// ListRoles 获取所有角色
func (r *RBACRepository) ListRoles(ctx context.Context) ([]*models.Role, error) {
	query := `SELECT * FROM roles ORDER BY name`

	var roles []*models.Role
	err := r.db.SelectContext(ctx, &roles, query)
	if err != nil {
		r.logger.WithError(err).Error("Failed to list roles")
		return nil, fmt.Errorf("failed to list roles: %w", err)
	}

	return roles, nil
}

// UpdateRole 更新角色
func (r *RBACRepository) UpdateRole(ctx context.Context, role *models.Role) error {
	role.UpdatedAt = time.Now()

	query := `
		UPDATE roles SET
			name = $2, display_name = $3, description = $4, updated_at = $5
		WHERE id = $1`

	result, err := r.db.ExecContext(ctx, query,
		role.ID, role.Name, role.DisplayName, role.Description, role.UpdatedAt)
	if err != nil {
		r.logger.WithError(err).WithField("role_id", role.ID).Error("Failed to update role")
		return fmt.Errorf("failed to update role: %w", err)
	}

	rowsAffected, err := result.RowsAffected()
	if err != nil {
		return fmt.Errorf("failed to get rows affected: %w", err)
	}

	if rowsAffected == 0 {
		return fmt.Errorf("role not found")
	}

	return nil
}

// DeleteRole 删除角色
func (r *RBACRepository) DeleteRole(ctx context.Context, id string) error {
	// 检查是否为系统角色
	role, err := r.GetRoleByID(ctx, id)
	if err != nil {
		return fmt.Errorf("failed to get role: %w", err)
	}
	if role == nil {
		return fmt.Errorf("role not found")
	}
	if role.IsSystem {
		return fmt.Errorf("cannot delete system role")
	}

	// 删除相关的角色权限关联
	_, err = r.db.ExecContext(ctx, "DELETE FROM role_permissions WHERE role_id = $1", id)
	if err != nil {
		r.logger.WithError(err).WithField("role_id", id).Error("Failed to delete role permissions")
		return fmt.Errorf("failed to delete role permissions: %w", err)
	}

	// 删除用户角色关联
	_, err = r.db.ExecContext(ctx, "DELETE FROM user_role_assignments WHERE role_id = $1", id)
	if err != nil {
		r.logger.WithError(err).WithField("role_id", id).Error("Failed to delete user role assignments")
		return fmt.Errorf("failed to delete user role assignments: %w", err)
	}

	// 删除角色
	result, err := r.db.ExecContext(ctx, "DELETE FROM roles WHERE id = $1", id)
	if err != nil {
		r.logger.WithError(err).WithField("role_id", id).Error("Failed to delete role")
		return fmt.Errorf("failed to delete role: %w", err)
	}

	rowsAffected, err := result.RowsAffected()
	if err != nil {
		return fmt.Errorf("failed to get rows affected: %w", err)
	}

	if rowsAffected == 0 {
		return fmt.Errorf("role not found")
	}

	return nil
}

// AssignPermissionsToRole 为角色分配权限
func (r *RBACRepository) AssignPermissionsToRole(ctx context.Context, roleID string, permissionIDs []string, assignedBy string) error {
	if len(permissionIDs) == 0 {
		return nil
	}

	tx, err := r.db.BeginTxx(ctx, nil)
	if err != nil {
		return fmt.Errorf("failed to begin transaction: %w", err)
	}
	defer tx.Rollback()

	// 删除现有的权限关联
	_, err = tx.ExecContext(ctx, "DELETE FROM role_permissions WHERE role_id = $1", roleID)
	if err != nil {
		return fmt.Errorf("failed to remove existing permissions: %w", err)
	}

	// 添加新的权限关联
	for _, permissionID := range permissionIDs {
		query := `
			INSERT INTO role_permissions (role_id, permission_id, created_by)
			VALUES ($1, $2, $3)
			ON CONFLICT (role_id, permission_id) DO NOTHING`

		_, err = tx.ExecContext(ctx, query, roleID, permissionID, assignedBy)
		if err != nil {
			return fmt.Errorf("failed to assign permission %s: %w", permissionID, err)
		}
	}

	return tx.Commit()
}

// RemovePermissionsFromRole 移除角色的权限
func (r *RBACRepository) RemovePermissionsFromRole(ctx context.Context, roleID string, permissionIDs []string) error {
	if len(permissionIDs) == 0 {
		return nil
	}

	placeholders := make([]string, len(permissionIDs))
	args := []interface{}{roleID}
	for i, permissionID := range permissionIDs {
		placeholders[i] = fmt.Sprintf("$%d", i+2)
		args = append(args, permissionID)
	}

	query := fmt.Sprintf(`
		DELETE FROM role_permissions
		WHERE role_id = $1 AND permission_id IN (%s)`,
		strings.Join(placeholders, ","))

	_, err := r.db.ExecContext(ctx, query, args...)
	if err != nil {
		r.logger.WithError(err).WithFields(logrus.Fields{
			"role_id":        roleID,
			"permission_ids": permissionIDs,
		}).Error("Failed to remove role permissions")
		return fmt.Errorf("failed to remove role permissions: %w", err)
	}

	return nil
}

// GetRolePermissions 获取角色的权限
func (r *RBACRepository) GetRolePermissions(ctx context.Context, roleID string) ([]*models.Permission, error) {
	query := `
		SELECT p.id, p.name, p.resource, p.action, p.description, p.created_at, p.updated_at
		FROM permissions p
		INNER JOIN role_permissions rp ON p.id = rp.permission_id
		WHERE rp.role_id = $1
		ORDER BY p.resource, p.action`

	var permissions []*models.Permission
	err := r.db.SelectContext(ctx, &permissions, query, roleID)
	if err != nil {
		r.logger.WithError(err).WithField("role_id", roleID).Error("Failed to get role permissions")
		return nil, fmt.Errorf("failed to get role permissions: %w", err)
	}

	return permissions, nil
}

// AssignRolesToUser 为用户分配角色
func (r *RBACRepository) AssignRolesToUser(ctx context.Context, userID string, roleIDs []string, assignedBy string) error {
	if len(roleIDs) == 0 {
		return nil
	}

	tx, err := r.db.BeginTxx(ctx, nil)
	if err != nil {
		return fmt.Errorf("failed to begin transaction: %w", err)
	}
	defer tx.Rollback()

	// 删除现有的角色关联
	_, err = tx.ExecContext(ctx, "DELETE FROM user_role_assignments WHERE user_id = $1", userID)
	if err != nil {
		return fmt.Errorf("failed to remove existing roles: %w", err)
	}

	// 添加新的角色关联
	for _, roleID := range roleIDs {
		query := `
			INSERT INTO user_role_assignments (user_id, role_id, assigned_by)
			VALUES ($1, $2, $3)
			ON CONFLICT (user_id, role_id) DO NOTHING`

		_, err = tx.ExecContext(ctx, query, userID, roleID, assignedBy)
		if err != nil {
			return fmt.Errorf("failed to assign role %s: %w", roleID, err)
		}
	}

	return tx.Commit()
}

// RemoveRolesFromUser 移除用户的角色
func (r *RBACRepository) RemoveRolesFromUser(ctx context.Context, userID string, roleIDs []string) error {
	if len(roleIDs) == 0 {
		return nil
	}

	placeholders := make([]string, len(roleIDs))
	args := []interface{}{userID}
	for i, roleID := range roleIDs {
		placeholders[i] = fmt.Sprintf("$%d", i+2)
		args = append(args, roleID)
	}

	query := fmt.Sprintf(`
		DELETE FROM user_role_assignments
		WHERE user_id = $1 AND role_id IN (%s)`,
		strings.Join(placeholders, ","))

	_, err := r.db.ExecContext(ctx, query, args...)
	if err != nil {
		r.logger.WithError(err).WithFields(logrus.Fields{
			"user_id":  userID,
			"role_ids": roleIDs,
		}).Error("Failed to remove user roles")
		return fmt.Errorf("failed to remove user roles: %w", err)
	}

	return nil
}

// GetUserRoles 获取用户的角色
func (r *RBACRepository) GetUserRoles(ctx context.Context, userID string) ([]*models.Role, error) {
	query := `
		SELECT r.id, r.name, r.display_name, r.description, r.is_system, r.created_at, r.updated_at, r.created_by
		FROM roles r
		INNER JOIN user_role_assignments ura ON r.id = ura.role_id
		WHERE ura.user_id = $1 AND (ura.expires_at IS NULL OR ura.expires_at > NOW())
		ORDER BY r.name`

	var roles []*models.Role
	err := r.db.SelectContext(ctx, &roles, query, userID)
	if err != nil {
		r.logger.WithError(err).WithField("user_id", userID).Error("Failed to get user roles")
		return nil, fmt.Errorf("failed to get user roles: %w", err)
	}

	return roles, nil
}
