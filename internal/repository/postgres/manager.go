package postgres

import (
	"context"

	"github.com/jmoiron/sqlx"
	"github.com/sirupsen/logrus"

	"proxyManager/internal/repository"
	"proxyManager/pkg/database"
)

// RepositoryManager PostgreSQL仓储管理器
type RepositoryManager struct {
	pgClient *database.PostgresClient
	logger   *logrus.Logger

	// 仓储实例
	proxy        repository.ProxyRepository
	proxyTag     repository.ProxyTagRepository
	user         repository.UserRepository
	healthCheck  repository.HealthCheckRepository
	task         repository.TaskRepository
	alert        repository.AlertRepository
	config       repository.SystemConfigRepository
	rbac         repository.RBACRepository
	apiKey       repository.APIKeyRepository
	userSettings repository.UserSettingsRepository
}

// NewRepositoryManager 创建PostgreSQL仓储管理器
func NewRepositoryManager(pgClient *database.PostgresClient, logger *logrus.Logger) repository.RepositoryManager {
	db := pgClient.GetDB()

	return &RepositoryManager{
		pgClient:     pgClient,
		logger:       logger,
		proxy:        NewProxyRepository(db, logger),
		proxyTag:     NewProxyTagRepository(db, logger),
		user:         NewUserRepository(db, logger),
		healthCheck:  NewHealthCheckRepository(db, logger),
		task:         NewTaskRepository(db, logger),
		alert:        NewAlertRepository(db, logger),
		config:       NewSystemConfigRepository(db, logger),
		rbac:         NewRBACRepository(db, logger),
		apiKey:       NewAPIKeyRepository(db, logger),
		userSettings: NewUserSettingsRepository(db, logger),
	}
}

// GetProxy 获取代理仓储
func (m *RepositoryManager) GetProxy() repository.ProxyRepository {
	return m.proxy
}

// GetProxyTag 获取代理标签仓储
func (m *RepositoryManager) GetProxyTag() repository.ProxyTagRepository {
	return m.proxyTag
}

// GetUser 获取用户仓储
func (m *RepositoryManager) GetUser() repository.UserRepository {
	return m.user
}

// GetHealthCheck 获取健康检查仓储
func (m *RepositoryManager) GetHealthCheck() repository.HealthCheckRepository {
	return m.healthCheck
}

// GetTask 获取任务仓储
func (m *RepositoryManager) GetTask() repository.TaskRepository {
	return m.task
}

// GetAlert 获取告警仓储
func (m *RepositoryManager) GetAlert() repository.AlertRepository {
	return m.alert
}

// GetConfig 获取系统配置仓储
func (m *RepositoryManager) GetConfig() repository.SystemConfigRepository {
	return m.config
}

// GetRBAC 获取RBAC仓储
func (m *RepositoryManager) GetRBAC() repository.RBACRepository {
	return m.rbac
}

// GetAPIKey 获取API密钥仓储
func (m *RepositoryManager) GetAPIKey() repository.APIKeyRepository {
	return m.apiKey
}

// GetUserSettings 获取用户设置仓储
func (m *RepositoryManager) GetUserSettings() repository.UserSettingsRepository {
	return m.userSettings
}

// Close 关闭仓储管理器
func (m *RepositoryManager) Close() error {
	if m.pgClient != nil {
		return m.pgClient.Close()
	}
	return nil
}

// GetDB 获取数据库连接（用于事务操作）
func (m *RepositoryManager) GetDB() *sqlx.DB {
	return m.pgClient.GetDB()
}

// WithTransaction 在事务中执行操作
func (m *RepositoryManager) WithTransaction(ctx context.Context, fn func(*sqlx.Tx) error) error {
	return m.pgClient.WithTransaction(ctx, fn)
}
