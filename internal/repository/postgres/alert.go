package postgres

import (
	"context"
	"database/sql"
	"fmt"
	"time"

	"github.com/jmoiron/sqlx"
	"github.com/sirupsen/logrus"

	"proxyManager/internal/models"
	"proxyManager/internal/repository"
)

// AlertRepository PostgreSQL告警仓储实现
type AlertRepository struct {
	db     *sqlx.DB
	logger *logrus.Logger
}

// NewAlertRepository 创建告警仓储
func NewAlertRepository(db *sqlx.DB, logger *logrus.Logger) repository.AlertRepository {
	return &AlertRepository{
		db:     db,
		logger: logger,
	}
}

// CreateRule 创建告警规则
func (r *AlertRepository) CreateRule(ctx context.Context, rule *models.AlertRule) error {
	query := `
		INSERT INTO alert_rules (
			id, name, description, metric, condition, threshold, duration,
			severity, enabled, notification_channels, created_at, updated_at
		) VALUES (
			:id, :name, :description, :metric, :condition, :threshold, :duration,
			:severity, :enabled, :notification_channels, :created_at, :updated_at
		)`

	_, err := r.db.NamedExecContext(ctx, query, rule)
	if err != nil {
		r.logger.WithError(err).WithField("rule_id", rule.ID).Error("Failed to create alert rule")
		return fmt.Errorf("failed to create alert rule: %w", err)
	}

	return nil
}

// GetRuleByID 根据ID获取告警规则
func (r *AlertRepository) GetRuleByID(ctx context.Context, id string) (*models.AlertRule, error) {
	var rule models.AlertRule
	query := `
		SELECT id, name, description, metric, condition, threshold, duration,
			   severity, enabled, notification_channels, created_at, updated_at
		FROM alert_rules WHERE id = $1`

	err := r.db.GetContext(ctx, &rule, query, id)
	if err != nil {
		if err == sql.ErrNoRows {
			return nil, nil
		}
		r.logger.WithError(err).WithField("rule_id", id).Error("Failed to get alert rule by ID")
		return nil, fmt.Errorf("failed to get alert rule by ID: %w", err)
	}

	return &rule, nil
}

// UpdateRule 更新告警规则
func (r *AlertRepository) UpdateRule(ctx context.Context, rule *models.AlertRule) error {
	query := `
		UPDATE alert_rules SET 
			name = :name, description = :description, metric = :metric, condition = :condition,
			threshold = :threshold, duration = :duration, severity = :severity, enabled = :enabled,
			notification_channels = :notification_channels, updated_at = :updated_at
		WHERE id = :id`

	result, err := r.db.NamedExecContext(ctx, query, rule)
	if err != nil {
		r.logger.WithError(err).WithField("rule_id", rule.ID).Error("Failed to update alert rule")
		return fmt.Errorf("failed to update alert rule: %w", err)
	}

	rowsAffected, err := result.RowsAffected()
	if err != nil {
		return fmt.Errorf("failed to get rows affected: %w", err)
	}

	if rowsAffected == 0 {
		return fmt.Errorf("alert rule not found")
	}

	return nil
}

// DeleteRule 删除告警规则
func (r *AlertRepository) DeleteRule(ctx context.Context, id string) error {
	query := `DELETE FROM alert_rules WHERE id = $1`

	result, err := r.db.ExecContext(ctx, query, id)
	if err != nil {
		r.logger.WithError(err).WithField("rule_id", id).Error("Failed to delete alert rule")
		return fmt.Errorf("failed to delete alert rule: %w", err)
	}

	rowsAffected, err := result.RowsAffected()
	if err != nil {
		return fmt.Errorf("failed to get rows affected: %w", err)
	}

	if rowsAffected == 0 {
		return fmt.Errorf("alert rule not found")
	}

	return nil
}

// ListRules 获取告警规则列表
func (r *AlertRepository) ListRules(ctx context.Context, filters repository.AlertRuleFilters) ([]*models.AlertRule, error) {
	var rules []*models.AlertRule

	query := `
		SELECT id, name, description, metric, condition, threshold, duration,
			   severity, enabled, notification_channels, created_at, updated_at
		FROM alert_rules WHERE 1=1`

	args := []interface{}{}
	argIndex := 1

	// 添加启用状态过滤
	if filters.Enabled != nil {
		query += fmt.Sprintf(" AND enabled = $%d", argIndex)
		args = append(args, *filters.Enabled)
		argIndex++
	}

	// 添加严重级别过滤
	if filters.Severity != nil {
		query += fmt.Sprintf(" AND severity = $%d", argIndex)
		args = append(args, *filters.Severity)
		argIndex++
	}

	// 添加指标过滤
	if filters.Metric != nil && *filters.Metric != "" {
		query += fmt.Sprintf(" AND metric = $%d", argIndex)
		args = append(args, *filters.Metric)
		argIndex++
	}

	// 添加排序
	query += " ORDER BY created_at DESC"

	// 添加分页
	if filters.Limit != nil && *filters.Limit > 0 {
		query += fmt.Sprintf(" LIMIT $%d", argIndex)
		args = append(args, *filters.Limit)
		argIndex++
	}

	if filters.Offset != nil && *filters.Offset > 0 {
		query += fmt.Sprintf(" OFFSET $%d", argIndex)
		args = append(args, *filters.Offset)
	}

	err := r.db.SelectContext(ctx, &rules, query, args...)
	if err != nil {
		r.logger.WithError(err).Error("Failed to list alert rules")
		return nil, fmt.Errorf("failed to list alert rules: %w", err)
	}

	return rules, nil
}

// GetEnabledRules 获取启用的告警规则
func (r *AlertRepository) GetEnabledRules(ctx context.Context) ([]*models.AlertRule, error) {
	var rules []*models.AlertRule
	query := `
		SELECT id, name, description, metric, condition, threshold, duration,
			   severity, enabled, notification_channels, created_at, updated_at
		FROM alert_rules 
		WHERE enabled = true
		ORDER BY severity DESC, created_at ASC`

	err := r.db.SelectContext(ctx, &rules, query)
	if err != nil {
		r.logger.WithError(err).Error("Failed to get enabled alert rules")
		return nil, fmt.Errorf("failed to get enabled alert rules: %w", err)
	}

	return rules, nil
}

// CreateAlert 创建告警记录
func (r *AlertRepository) CreateAlert(ctx context.Context, alert *models.Alert) error {
	query := `
		INSERT INTO alerts (
			id, rule_id, metric_value, status, message, fired_at,
			resolved_at, notification_sent, metadata
		) VALUES (
			:id, :rule_id, :metric_value, :status, :message, :fired_at,
			:resolved_at, :notification_sent, :metadata
		)`

	_, err := r.db.NamedExecContext(ctx, query, alert)
	if err != nil {
		r.logger.WithError(err).WithField("alert_id", alert.ID).Error("Failed to create alert")
		return fmt.Errorf("failed to create alert: %w", err)
	}

	return nil
}

// GetAlertByID 根据ID获取告警记录
func (r *AlertRepository) GetAlertByID(ctx context.Context, id string) (*models.Alert, error) {
	var alert models.Alert
	query := `
		SELECT id, rule_id, metric_value, status, message, fired_at,
			   resolved_at, notification_sent, metadata
		FROM alerts WHERE id = $1`

	err := r.db.GetContext(ctx, &alert, query, id)
	if err != nil {
		if err == sql.ErrNoRows {
			return nil, nil
		}
		r.logger.WithError(err).WithField("alert_id", id).Error("Failed to get alert by ID")
		return nil, fmt.Errorf("failed to get alert by ID: %w", err)
	}

	return &alert, nil
}

// UpdateAlert 更新告警记录
func (r *AlertRepository) UpdateAlert(ctx context.Context, alert *models.Alert) error {
	query := `
		UPDATE alerts SET 
			metric_value = :metric_value, status = :status, message = :message,
			resolved_at = :resolved_at, notification_sent = :notification_sent, metadata = :metadata
		WHERE id = :id`

	result, err := r.db.NamedExecContext(ctx, query, alert)
	if err != nil {
		r.logger.WithError(err).WithField("alert_id", alert.ID).Error("Failed to update alert")
		return fmt.Errorf("failed to update alert: %w", err)
	}

	rowsAffected, err := result.RowsAffected()
	if err != nil {
		return fmt.Errorf("failed to get rows affected: %w", err)
	}

	if rowsAffected == 0 {
		return fmt.Errorf("alert not found")
	}

	return nil
}

// ListAlerts 获取告警记录列表
func (r *AlertRepository) ListAlerts(ctx context.Context, filters repository.AlertFilters) ([]*models.Alert, error) {
	var alerts []*models.Alert

	query := `
		SELECT id, rule_id, metric_value, status, message, fired_at,
			   resolved_at, notification_sent, metadata
		FROM alerts WHERE 1=1`

	args := []interface{}{}
	argIndex := 1

	// 添加规则ID过滤
	if filters.RuleID != nil && *filters.RuleID != "" {
		query += fmt.Sprintf(" AND rule_id = $%d", argIndex)
		args = append(args, *filters.RuleID)
		argIndex++
	}

	// 添加状态过滤
	if filters.Status != nil {
		query += fmt.Sprintf(" AND status = $%d", argIndex)
		args = append(args, *filters.Status)
		argIndex++
	}

	// 添加时间范围过滤
	if filters.From != nil && !filters.From.IsZero() {
		query += fmt.Sprintf(" AND fired_at >= $%d", argIndex)
		args = append(args, *filters.From)
		argIndex++
	}

	if filters.To != nil && !filters.To.IsZero() {
		query += fmt.Sprintf(" AND fired_at <= $%d", argIndex)
		args = append(args, *filters.To)
		argIndex++
	}

	// 添加排序
	query += " ORDER BY fired_at DESC"

	// 添加分页
	if filters.Limit != nil && *filters.Limit > 0 {
		query += fmt.Sprintf(" LIMIT $%d", argIndex)
		args = append(args, *filters.Limit)
		argIndex++
	}

	if filters.Offset != nil && *filters.Offset > 0 {
		query += fmt.Sprintf(" OFFSET $%d", argIndex)
		args = append(args, *filters.Offset)
	}

	err := r.db.SelectContext(ctx, &alerts, query, args...)
	if err != nil {
		r.logger.WithError(err).Error("Failed to list alerts")
		return nil, fmt.Errorf("failed to list alerts: %w", err)
	}

	return alerts, nil
}

// ResolveAlert 解决告警
func (r *AlertRepository) ResolveAlert(ctx context.Context, id string) error {
	query := `
		UPDATE alerts SET 
			status = 'resolved', resolved_at = $2
		WHERE id = $1 AND status = 'firing'`

	result, err := r.db.ExecContext(ctx, query, id, time.Now())
	if err != nil {
		r.logger.WithError(err).WithField("alert_id", id).Error("Failed to resolve alert")
		return fmt.Errorf("failed to resolve alert: %w", err)
	}

	rowsAffected, err := result.RowsAffected()
	if err != nil {
		return fmt.Errorf("failed to get rows affected: %w", err)
	}

	if rowsAffected == 0 {
		return fmt.Errorf("alert not found or already resolved")
	}

	return nil
}

// GetActiveAlerts 获取活跃的告警
func (r *AlertRepository) GetActiveAlerts(ctx context.Context) ([]*models.Alert, error) {
	var alerts []*models.Alert
	query := `
		SELECT id, rule_id, metric_value, status, message, fired_at,
			   resolved_at, notification_sent, metadata
		FROM alerts 
		WHERE status = 'firing'
		ORDER BY fired_at DESC`

	err := r.db.SelectContext(ctx, &alerts, query)
	if err != nil {
		r.logger.WithError(err).Error("Failed to get active alerts")
		return nil, fmt.Errorf("failed to get active alerts: %w", err)
	}

	return alerts, nil
}
