package postgres

import (
	"context"
	"database/sql"
	"encoding/json"
	"fmt"
	"time"

	"github.com/jmoiron/sqlx"
	"github.com/sirupsen/logrus"

	"proxyManager/internal/repository"
)

// SystemConfigRepository PostgreSQL系统配置仓储实现
type SystemConfigRepository struct {
	db     *sqlx.DB
	logger *logrus.Logger
}

// NewSystemConfigRepository 创建系统配置仓储
func NewSystemConfigRepository(db *sqlx.DB, logger *logrus.Logger) repository.SystemConfigRepository {
	return &SystemConfigRepository{
		db:     db,
		logger: logger,
	}
}

// Get 获取配置值
func (r *SystemConfigRepository) Get(ctx context.Context, key string) (interface{}, error) {
	var valueJSON []byte
	query := `SELECT value FROM system_configs WHERE key = $1`

	err := r.db.QueryRowContext(ctx, query, key).Scan(&valueJSON)
	if err != nil {
		if err == sql.ErrNoRows {
			return nil, nil
		}
		r.logger.WithError(err).WithField("key", key).Error("Failed to get system config")
		return nil, fmt.Errorf("failed to get system config: %w", err)
	}

	var value interface{}
	if err := json.Unmarshal(valueJSON, &value); err != nil {
		r.logger.WithError(err).WithField("key", key).Error("Failed to unmarshal config value")
		return nil, fmt.Errorf("failed to unmarshal config value: %w", err)
	}

	return value, nil
}

// Set 设置配置值
func (r *SystemConfigRepository) Set(ctx context.Context, key string, value interface{}) error {
	valueJSON, err := json.Marshal(value)
	if err != nil {
		r.logger.WithError(err).WithField("key", key).Error("Failed to marshal config value")
		return fmt.Errorf("failed to marshal config value: %w", err)
	}

	query := `
		INSERT INTO system_configs (key, value, updated_at) 
		VALUES ($1, $2, $3)
		ON CONFLICT (key) 
		DO UPDATE SET value = EXCLUDED.value, updated_at = EXCLUDED.updated_at`

	_, err = r.db.ExecContext(ctx, query, key, valueJSON, time.Now())
	if err != nil {
		r.logger.WithError(err).WithField("key", key).Error("Failed to set system config")
		return fmt.Errorf("failed to set system config: %w", err)
	}

	return nil
}

// GetAll 获取所有配置
func (r *SystemConfigRepository) GetAll(ctx context.Context) (map[string]interface{}, error) {
	rows, err := r.db.QueryContext(ctx, `SELECT key, value FROM system_configs`)
	if err != nil {
		r.logger.WithError(err).Error("Failed to get all system configs")
		return nil, fmt.Errorf("failed to get all system configs: %w", err)
	}
	defer rows.Close()

	configs := make(map[string]interface{})
	for rows.Next() {
		var key string
		var valueJSON []byte
		
		if err := rows.Scan(&key, &valueJSON); err != nil {
			r.logger.WithError(err).Error("Failed to scan config row")
			continue
		}

		var value interface{}
		if err := json.Unmarshal(valueJSON, &value); err != nil {
			r.logger.WithError(err).WithField("key", key).Error("Failed to unmarshal config value")
			continue
		}

		configs[key] = value
	}

	if err := rows.Err(); err != nil {
		r.logger.WithError(err).Error("Error iterating config rows")
		return nil, fmt.Errorf("error iterating config rows: %w", err)
	}

	return configs, nil
}

// Delete 删除配置
func (r *SystemConfigRepository) Delete(ctx context.Context, key string) error {
	query := `DELETE FROM system_configs WHERE key = $1`

	result, err := r.db.ExecContext(ctx, query, key)
	if err != nil {
		r.logger.WithError(err).WithField("key", key).Error("Failed to delete system config")
		return fmt.Errorf("failed to delete system config: %w", err)
	}

	rowsAffected, err := result.RowsAffected()
	if err != nil {
		return fmt.Errorf("failed to get rows affected: %w", err)
	}

	if rowsAffected == 0 {
		return fmt.Errorf("config key not found")
	}

	return nil
}

// GetString 获取字符串配置值
func (r *SystemConfigRepository) GetString(ctx context.Context, key string, defaultValue string) (string, error) {
	value, err := r.Get(ctx, key)
	if err != nil {
		return defaultValue, err
	}
	if value == nil {
		return defaultValue, nil
	}

	if str, ok := value.(string); ok {
		return str, nil
	}

	return defaultValue, fmt.Errorf("config value is not a string")
}

// GetInt 获取整数配置值
func (r *SystemConfigRepository) GetInt(ctx context.Context, key string, defaultValue int) (int, error) {
	value, err := r.Get(ctx, key)
	if err != nil {
		return defaultValue, err
	}
	if value == nil {
		return defaultValue, nil
	}

	// JSON 数字通常被解析为 float64
	if f, ok := value.(float64); ok {
		return int(f), nil
	}

	if i, ok := value.(int); ok {
		return i, nil
	}

	return defaultValue, fmt.Errorf("config value is not a number")
}

// GetFloat 获取浮点数配置值
func (r *SystemConfigRepository) GetFloat(ctx context.Context, key string, defaultValue float64) (float64, error) {
	value, err := r.Get(ctx, key)
	if err != nil {
		return defaultValue, err
	}
	if value == nil {
		return defaultValue, nil
	}

	if f, ok := value.(float64); ok {
		return f, nil
	}

	if i, ok := value.(int); ok {
		return float64(i), nil
	}

	return defaultValue, fmt.Errorf("config value is not a number")
}

// GetBool 获取布尔配置值
func (r *SystemConfigRepository) GetBool(ctx context.Context, key string, defaultValue bool) (bool, error) {
	value, err := r.Get(ctx, key)
	if err != nil {
		return defaultValue, err
	}
	if value == nil {
		return defaultValue, nil
	}

	if b, ok := value.(bool); ok {
		return b, nil
	}

	return defaultValue, fmt.Errorf("config value is not a boolean")
}

// SetWithDescription 设置配置值并添加描述
func (r *SystemConfigRepository) SetWithDescription(ctx context.Context, key string, value interface{}, description string) error {
	valueJSON, err := json.Marshal(value)
	if err != nil {
		r.logger.WithError(err).WithField("key", key).Error("Failed to marshal config value")
		return fmt.Errorf("failed to marshal config value: %w", err)
	}

	query := `
		INSERT INTO system_configs (key, value, description, updated_at) 
		VALUES ($1, $2, $3, $4)
		ON CONFLICT (key) 
		DO UPDATE SET value = EXCLUDED.value, description = EXCLUDED.description, updated_at = EXCLUDED.updated_at`

	_, err = r.db.ExecContext(ctx, query, key, valueJSON, description, time.Now())
	if err != nil {
		r.logger.WithError(err).WithField("key", key).Error("Failed to set system config with description")
		return fmt.Errorf("failed to set system config with description: %w", err)
	}

	return nil
}

// GetWithDescription 获取配置值和描述
func (r *SystemConfigRepository) GetWithDescription(ctx context.Context, key string) (interface{}, string, error) {
	var valueJSON []byte
	var description sql.NullString
	query := `SELECT value, description FROM system_configs WHERE key = $1`

	err := r.db.QueryRowContext(ctx, query, key).Scan(&valueJSON, &description)
	if err != nil {
		if err == sql.ErrNoRows {
			return nil, "", nil
		}
		r.logger.WithError(err).WithField("key", key).Error("Failed to get system config with description")
		return nil, "", fmt.Errorf("failed to get system config with description: %w", err)
	}

	var value interface{}
	if err := json.Unmarshal(valueJSON, &value); err != nil {
		r.logger.WithError(err).WithField("key", key).Error("Failed to unmarshal config value")
		return nil, "", fmt.Errorf("failed to unmarshal config value: %w", err)
	}

	desc := ""
	if description.Valid {
		desc = description.String
	}

	return value, desc, nil
}
