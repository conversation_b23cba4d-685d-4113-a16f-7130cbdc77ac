package postgres

import (
	"context"
	"database/sql"
	"fmt"
	"strings"
	"time"

	"github.com/jmoiron/sqlx"
	"github.com/sirupsen/logrus"

	"proxyManager/internal/models"
	"proxyManager/internal/repository"
)

// ProxyRepository PostgreSQL代理仓储实现
type ProxyRepository struct {
	db        *sqlx.DB
	logger    *logrus.Logger
	validator *SecurityValidator
}

// NewProxyRepository 创建代理仓储
func NewProxyRepository(db *sqlx.DB, logger *logrus.Logger) repository.ProxyRepository {
	return &ProxyRepository{
		db:        db,
		logger:    logger,
		validator: NewSecurityValidator(),
	}
}

// Create 创建代理
func (r *ProxyRepository) Create(ctx context.Context, proxy *models.Proxy) error {
	var query string
	var err error

	if proxy.ID == "" {
		// 让数据库自动生成 UUID
		query = `
			INSERT INTO proxies (
				host, port, type, username, password, status, failures,
				last_check, last_success, created_at, updated_at, use_count,
				response_time, weight, country_code, city_name, asn_name,
				asn_number, high_country_confidence, quality_score, speed_score,
				stability_score, anonymity_level, reliability_score, last_quality_check,
				scenario, priority
			) VALUES (
				$1, $2, $3, $4, $5, $6, $7,
				$8, $9, $10, $11, $12,
				$13, $14, $15, $16, $17,
				$18, $19, $20, $21,
				$22, $23, $24, $25,
				$26, $27
			) RETURNING id`

		fmt.Printf("DEBUG: Creating proxy with values: Host=%s, Port=%d, Type=%s, AnonymityLevel=%s, ASNNumber=%v\n",
			proxy.Host, proxy.Port, proxy.Type, proxy.AnonymityLevel, proxy.ASNNumber)

		err = r.db.GetContext(ctx, &proxy.ID, query,
			proxy.Host, proxy.Port, proxy.Type, proxy.Username, proxy.Password, proxy.Status, proxy.Failures,
			proxy.LastCheck, proxy.LastSuccess, proxy.CreatedAt, proxy.UpdatedAt, proxy.UseCount,
			proxy.ResponseTime, proxy.Weight, proxy.CountryCode, proxy.CityName, proxy.ASNName,
			proxy.ASNNumber, proxy.HighCountryConfidence, proxy.QualityScore, proxy.SpeedScore,
			proxy.StabilityScore, proxy.AnonymityLevel, proxy.ReliabilityScore, proxy.LastQualityCheck,
			proxy.Scenario, proxy.Priority)
	} else {
		// 使用提供的 ID
		query = `
			INSERT INTO proxies (
				id, host, port, type, username, password, status, failures,
				last_check, last_success, created_at, updated_at, use_count,
				response_time, weight, country_code, city_name, asn_name,
				asn_number, high_country_confidence, quality_score, speed_score,
				stability_score, anonymity_level, reliability_score, last_quality_check,
				scenario, priority
			) VALUES (
				$1, $2, $3, $4, $5, $6, $7, $8,
				$9, $10, $11, $12, $13,
				$14, $15, $16, $17, $18,
				$19, $20, $21, $22,
				$23, $24, $25, $26,
				$27, $28
			)`

		_, err = r.db.ExecContext(ctx, query,
			proxy.ID, proxy.Host, proxy.Port, proxy.Type, proxy.Username, proxy.Password, proxy.Status, proxy.Failures,
			proxy.LastCheck, proxy.LastSuccess, proxy.CreatedAt, proxy.UpdatedAt, proxy.UseCount,
			proxy.ResponseTime, proxy.Weight, proxy.CountryCode, proxy.CityName, proxy.ASNName,
			proxy.ASNNumber, proxy.HighCountryConfidence, proxy.QualityScore, proxy.SpeedScore,
			proxy.StabilityScore, proxy.AnonymityLevel, proxy.ReliabilityScore, proxy.LastQualityCheck,
			proxy.Scenario, proxy.Priority)
	}

	if err != nil {
		r.logger.WithError(err).Error("Failed to create proxy")
		return fmt.Errorf("failed to create proxy: %w", err)
	}

	return nil
}

// GetByID 根据ID获取代理
func (r *ProxyRepository) GetByID(ctx context.Context, id string) (*models.Proxy, error) {
	var proxy models.Proxy
	query := `SELECT * FROM proxies WHERE id = $1`

	err := r.db.GetContext(ctx, &proxy, query, id)
	if err != nil {
		if err == sql.ErrNoRows {
			return nil, nil
		}
		r.logger.WithError(err).WithField("proxy_id", id).Error("Failed to get proxy by ID")
		return nil, fmt.Errorf("failed to get proxy by ID: %w", err)
	}

	return &proxy, nil
}

// GetByHostPort 根据主机和端口获取代理
func (r *ProxyRepository) GetByHostPort(ctx context.Context, host string, port int) (*models.Proxy, error) {
	var proxy models.Proxy
	query := `SELECT * FROM proxies WHERE host = $1 AND port = $2`

	err := r.db.GetContext(ctx, &proxy, query, host, port)
	if err != nil {
		if err == sql.ErrNoRows {
			return nil, nil
		}
		r.logger.WithError(err).WithFields(logrus.Fields{
			"host": host,
			"port": port,
		}).Error("Failed to get proxy by host and port")
		return nil, fmt.Errorf("failed to get proxy by host and port: %w", err)
	}

	return &proxy, nil
}

// Update 更新代理
func (r *ProxyRepository) Update(ctx context.Context, proxy *models.Proxy) error {
	proxy.UpdatedAt = time.Now()

	query := `
		UPDATE proxies SET
			host = :host, port = :port, type = :type, username = :username,
			password = :password, status = :status, failures = :failures,
			last_check = :last_check, last_success = :last_success,
			updated_at = :updated_at, use_count = :use_count,
			response_time = :response_time, weight = :weight,
			country_code = :country_code, city_name = :city_name,
			asn_name = :asn_name, asn_number = :asn_number,
			high_country_confidence = :high_country_confidence,
			quality_score = :quality_score, speed_score = :speed_score,
			stability_score = :stability_score, anonymity_level = :anonymity_level,
			reliability_score = :reliability_score, last_quality_check = :last_quality_check
		WHERE id = :id`

	result, err := r.db.NamedExecContext(ctx, query, proxy)
	if err != nil {
		r.logger.WithError(err).WithField("proxy_id", proxy.ID).Error("Failed to update proxy")
		return fmt.Errorf("failed to update proxy: %w", err)
	}

	rowsAffected, err := result.RowsAffected()
	if err != nil {
		return fmt.Errorf("failed to get rows affected: %w", err)
	}

	if rowsAffected == 0 {
		return fmt.Errorf("proxy not found")
	}

	return nil
}

// Delete 删除代理
func (r *ProxyRepository) Delete(ctx context.Context, id string) error {
	query := `DELETE FROM proxies WHERE id = $1`

	result, err := r.db.ExecContext(ctx, query, id)
	if err != nil {
		r.logger.WithError(err).WithField("proxy_id", id).Error("Failed to delete proxy")
		return fmt.Errorf("failed to delete proxy: %w", err)
	}

	rowsAffected, err := result.RowsAffected()
	if err != nil {
		return fmt.Errorf("failed to get rows affected: %w", err)
	}

	if rowsAffected == 0 {
		return fmt.Errorf("proxy not found")
	}

	return nil
}

// List 获取代理列表
func (r *ProxyRepository) List(ctx context.Context, filters repository.ProxyFilters) ([]*models.Proxy, error) {
	query := `SELECT * FROM proxies`
	args := []interface{}{}
	conditions := []string{}
	argIndex := 1

	// 构建WHERE条件
	if filters.Status != nil {
		conditions = append(conditions, fmt.Sprintf("status = $%d", argIndex))
		args = append(args, *filters.Status)
		argIndex++
	}

	if filters.Type != nil {
		conditions = append(conditions, fmt.Sprintf("type = $%d", argIndex))
		args = append(args, *filters.Type)
		argIndex++
	}

	if filters.CountryCode != nil {
		conditions = append(conditions, fmt.Sprintf("country_code = $%d", argIndex))
		args = append(args, *filters.CountryCode)
		argIndex++
	}

	if filters.CityName != nil {
		conditions = append(conditions, fmt.Sprintf("city_name = $%d", argIndex))
		args = append(args, *filters.CityName)
		argIndex++
	}

	if filters.MinQuality != nil {
		conditions = append(conditions, fmt.Sprintf("quality_score >= $%d", argIndex))
		args = append(args, *filters.MinQuality)
		argIndex++
	}

	if filters.MaxQuality != nil {
		conditions = append(conditions, fmt.Sprintf("quality_score <= $%d", argIndex))
		args = append(args, *filters.MaxQuality)
		argIndex++
	}

	if filters.Search != nil && *filters.Search != "" {
		searchCondition := fmt.Sprintf("(host ILIKE $%d OR city_name ILIKE $%d OR country_code ILIKE $%d)", argIndex, argIndex+1, argIndex+2)
		conditions = append(conditions, searchCondition)
		searchTerm := "%" + *filters.Search + "%"
		args = append(args, searchTerm, searchTerm, searchTerm)
		argIndex += 3
	}

	if len(conditions) > 0 {
		query += " WHERE " + strings.Join(conditions, " AND ")
	}

	// 排序 - 使用安全验证器防止 SQL 注入
	orderBy := "created_at"
	orderDir := "DESC"

	if filters.OrderBy != nil {
		if validOrderBy, err := r.validator.ValidateOrderBy(*filters.OrderBy, r.validator.GetAllowedProxyOrderByFields()); err == nil {
			orderBy = validOrderBy
		} else {
			r.logger.WithError(err).WithField("order_by", *filters.OrderBy).Warn("Invalid order by field")
		}
	}

	if filters.OrderDir != nil {
		if validOrderDir, err := r.validator.ValidateOrderDirection(*filters.OrderDir); err == nil {
			orderDir = validOrderDir
		} else {
			r.logger.WithError(err).WithField("order_dir", *filters.OrderDir).Warn("Invalid order direction")
		}
	}

	query += fmt.Sprintf(" ORDER BY %s %s", orderBy, orderDir)

	// 分页
	if filters.Limit != nil {
		query += fmt.Sprintf(" LIMIT $%d", argIndex)
		args = append(args, *filters.Limit)
		argIndex++
	}

	if filters.Offset != nil {
		query += fmt.Sprintf(" OFFSET $%d", argIndex)
		args = append(args, *filters.Offset)
	}

	var proxies []*models.Proxy
	err := r.db.SelectContext(ctx, &proxies, query, args...)
	if err != nil {
		r.logger.WithError(err).Error("Failed to list proxies")
		return nil, fmt.Errorf("failed to list proxies: %w", err)
	}

	return proxies, nil
}

// Count 获取代理数量
func (r *ProxyRepository) Count(ctx context.Context, filters repository.ProxyFilters) (int64, error) {
	query := `SELECT COUNT(*) FROM proxies`
	args := []interface{}{}
	conditions := []string{}
	argIndex := 1

	// 构建WHERE条件（与List方法相同的逻辑）
	if filters.Status != nil {
		conditions = append(conditions, fmt.Sprintf("status = $%d", argIndex))
		args = append(args, *filters.Status)
		argIndex++
	}

	if filters.Type != nil {
		conditions = append(conditions, fmt.Sprintf("type = $%d", argIndex))
		args = append(args, *filters.Type)
		argIndex++
	}

	if filters.CountryCode != nil {
		conditions = append(conditions, fmt.Sprintf("country_code = $%d", argIndex))
		args = append(args, *filters.CountryCode)
		argIndex++
	}

	if filters.CityName != nil {
		conditions = append(conditions, fmt.Sprintf("city_name = $%d", argIndex))
		args = append(args, *filters.CityName)
		argIndex++
	}

	if filters.MinQuality != nil {
		conditions = append(conditions, fmt.Sprintf("quality_score >= $%d", argIndex))
		args = append(args, *filters.MinQuality)
		argIndex++
	}

	if filters.MaxQuality != nil {
		conditions = append(conditions, fmt.Sprintf("quality_score <= $%d", argIndex))
		args = append(args, *filters.MaxQuality)
		argIndex++
	}

	if filters.Search != nil && *filters.Search != "" {
		searchCondition := fmt.Sprintf("(host ILIKE $%d OR city_name ILIKE $%d OR country_code ILIKE $%d)", argIndex, argIndex+1, argIndex+2)
		conditions = append(conditions, searchCondition)
		searchTerm := "%" + *filters.Search + "%"
		args = append(args, searchTerm, searchTerm, searchTerm)
	}

	if len(conditions) > 0 {
		query += " WHERE " + strings.Join(conditions, " AND ")
	}

	var count int64
	err := r.db.GetContext(ctx, &count, query, args...)
	if err != nil {
		r.logger.WithError(err).Error("Failed to count proxies")
		return 0, fmt.Errorf("failed to count proxies: %w", err)
	}

	return count, nil
}

// ListWithPagination 分页获取代理列表
func (r *ProxyRepository) ListWithPagination(ctx context.Context, filters repository.ProxyFilters, pagination models.PaginationParams) ([]*models.Proxy, int64, error) {
	// 设置默认分页参数
	if pagination.Page <= 0 {
		pagination.Page = 1
	}
	if pagination.Limit <= 0 {
		pagination.Limit = 20
	}
	if pagination.Limit > 100 {
		pagination.Limit = 100 // 最大限制100条
	}

	// 计算偏移量
	offset := (pagination.Page - 1) * pagination.Limit

	// 先获取总数
	total, err := r.Count(ctx, filters)
	if err != nil {
		return nil, 0, fmt.Errorf("failed to count proxies: %w", err)
	}

	// 构建查询
	query := `SELECT * FROM proxies`
	args := []interface{}{}
	conditions := []string{}
	argIndex := 1

	// 构建WHERE条件（与List方法相同的逻辑）
	if filters.Status != nil {
		conditions = append(conditions, fmt.Sprintf("status = $%d", argIndex))
		args = append(args, *filters.Status)
		argIndex++
	}

	if filters.Type != nil {
		conditions = append(conditions, fmt.Sprintf("type = $%d", argIndex))
		args = append(args, *filters.Type)
		argIndex++
	}

	if filters.CountryCode != nil {
		conditions = append(conditions, fmt.Sprintf("country_code = $%d", argIndex))
		args = append(args, *filters.CountryCode)
		argIndex++
	}

	if filters.CityName != nil {
		conditions = append(conditions, fmt.Sprintf("city_name = $%d", argIndex))
		args = append(args, *filters.CityName)
		argIndex++
	}

	if filters.MinQuality != nil {
		conditions = append(conditions, fmt.Sprintf("quality_score >= $%d", argIndex))
		args = append(args, *filters.MinQuality)
		argIndex++
	}

	if filters.MaxQuality != nil {
		conditions = append(conditions, fmt.Sprintf("quality_score <= $%d", argIndex))
		args = append(args, *filters.MaxQuality)
		argIndex++
	}

	if filters.Search != nil && *filters.Search != "" {
		searchCondition := fmt.Sprintf("(host ILIKE $%d OR city_name ILIKE $%d OR country_code ILIKE $%d)", argIndex, argIndex+1, argIndex+2)
		conditions = append(conditions, searchCondition)
		searchTerm := "%" + *filters.Search + "%"
		args = append(args, searchTerm, searchTerm, searchTerm)
		argIndex += 3
	}

	if len(conditions) > 0 {
		query += " WHERE " + strings.Join(conditions, " AND ")
	}

	// 排序 - 使用安全验证器防止 SQL 注入
	orderBy := "created_at"
	orderDir := "DESC"

	if filters.OrderBy != nil {
		if validOrderBy, err := r.validator.ValidateOrderBy(*filters.OrderBy, r.validator.GetAllowedProxyOrderByFields()); err == nil {
			orderBy = validOrderBy
		} else {
			r.logger.WithError(err).WithField("order_by", *filters.OrderBy).Warn("Invalid order by field")
		}
	}

	if filters.OrderDir != nil {
		if validOrderDir, err := r.validator.ValidateOrderDirection(*filters.OrderDir); err == nil {
			orderDir = validOrderDir
		} else {
			r.logger.WithError(err).WithField("order_dir", *filters.OrderDir).Warn("Invalid order direction")
		}
	}

	query += fmt.Sprintf(" ORDER BY %s %s", orderBy, orderDir)

	// 添加分页
	query += fmt.Sprintf(" LIMIT $%d OFFSET $%d", argIndex, argIndex+1)
	args = append(args, pagination.Limit, offset)

	var proxies []*models.Proxy
	err = r.db.SelectContext(ctx, &proxies, query, args...)
	if err != nil {
		r.logger.WithError(err).Error("Failed to list proxies with pagination")
		return nil, 0, fmt.Errorf("failed to list proxies with pagination: %w", err)
	}

	return proxies, total, nil
}

// CreateBatch 批量创建代理
func (r *ProxyRepository) CreateBatch(ctx context.Context, proxies []*models.Proxy) error {
	if len(proxies) == 0 {
		return nil
	}

	query := `
		INSERT INTO proxies (
			id, host, port, type, username, password, status, failures,
			last_check, last_success, created_at, updated_at, use_count,
			response_time, weight, country_code, city_name, asn_name,
			asn_number, high_country_confidence, quality_score, speed_score,
			stability_score, anonymity_level, reliability_score, last_quality_check
		) VALUES (
			:id, :host, :port, :type, :username, :password, :status, :failures,
			:last_check, :last_success, :created_at, :updated_at, :use_count,
			:response_time, :weight, :country_code, :city_name, :asn_name,
			:asn_number, :high_country_confidence, :quality_score, :speed_score,
			:stability_score, :anonymity_level, :reliability_score, :last_quality_check
		)`

	_, err := r.db.NamedExecContext(ctx, query, proxies)
	if err != nil {
		r.logger.WithError(err).Error("Failed to create proxies in batch")
		return fmt.Errorf("failed to create proxies in batch: %w", err)
	}

	return nil
}

// UpdateBatch 批量更新代理
func (r *ProxyRepository) UpdateBatch(ctx context.Context, proxies []*models.Proxy) error {
	if len(proxies) == 0 {
		return nil
	}

	tx, err := r.db.BeginTxx(ctx, nil)
	if err != nil {
		return fmt.Errorf("failed to begin transaction: %w", err)
	}
	defer tx.Rollback()

	query := `
		UPDATE proxies SET
			host = $2, port = $3, type = $4, username = $5, password = $6,
			status = $7, failures = $8, last_check = $9, last_success = $10,
			updated_at = $11, use_count = $12, response_time = $13, weight = $14,
			country_code = $15, city_name = $16, asn_name = $17, asn_number = $18,
			high_country_confidence = $19, quality_score = $20, speed_score = $21,
			stability_score = $22, anonymity_level = $23, reliability_score = $24,
			last_quality_check = $25
		WHERE id = $1`

	for _, proxy := range proxies {
		proxy.UpdatedAt = time.Now()
		_, err := tx.ExecContext(ctx, query,
			proxy.ID, proxy.Host, proxy.Port, proxy.Type, proxy.Username, proxy.Password,
			proxy.Status, proxy.Failures, proxy.LastCheck, proxy.LastSuccess,
			proxy.UpdatedAt, proxy.UseCount, proxy.ResponseTime, proxy.Weight,
			proxy.CountryCode, proxy.CityName, proxy.ASNName, proxy.ASNNumber,
			proxy.HighCountryConfidence, proxy.QualityScore, proxy.SpeedScore,
			proxy.StabilityScore, proxy.AnonymityLevel, proxy.ReliabilityScore,
			proxy.LastQualityCheck,
		)
		if err != nil {
			r.logger.WithError(err).WithField("proxy_id", proxy.ID).Error("Failed to update proxy in batch")
			return fmt.Errorf("failed to update proxy %s: %w", proxy.ID, err)
		}
	}

	if err := tx.Commit(); err != nil {
		return fmt.Errorf("failed to commit transaction: %w", err)
	}

	return nil
}

// DeleteBatch 批量删除代理
func (r *ProxyRepository) DeleteBatch(ctx context.Context, ids []string) error {
	if len(ids) == 0 {
		return nil
	}

	query := `DELETE FROM proxies WHERE id = ANY($1)`
	_, err := r.db.ExecContext(ctx, query, ids)
	if err != nil {
		r.logger.WithError(err).Error("Failed to delete proxies in batch")
		return fmt.Errorf("failed to delete proxies in batch: %w", err)
	}

	return nil
}

// GetByStatus 根据状态获取代理
func (r *ProxyRepository) GetByStatus(ctx context.Context, status models.ProxyStatus) ([]*models.Proxy, error) {
	var proxies []*models.Proxy
	query := `SELECT * FROM proxies WHERE status = $1 ORDER BY created_at DESC`

	err := r.db.SelectContext(ctx, &proxies, query, status)
	if err != nil {
		r.logger.WithError(err).WithField("status", status).Error("Failed to get proxies by status")
		return nil, fmt.Errorf("failed to get proxies by status: %w", err)
	}

	return proxies, nil
}

// UpdateStatus 更新代理状态
func (r *ProxyRepository) UpdateStatus(ctx context.Context, id string, status models.ProxyStatus) error {
	query := `UPDATE proxies SET status = $1, updated_at = $2 WHERE id = $3`

	result, err := r.db.ExecContext(ctx, query, status, time.Now(), id)
	if err != nil {
		r.logger.WithError(err).WithFields(logrus.Fields{
			"proxy_id": id,
			"status":   status,
		}).Error("Failed to update proxy status proxy.go")
		return fmt.Errorf("failed to update proxy status proxy.go: %w", err)
	}

	rowsAffected, err := result.RowsAffected()
	if err != nil {
		return fmt.Errorf("failed to get rows affected: %w", err)
	}

	if rowsAffected == 0 {
		return fmt.Errorf("proxy not found")
	}

	return nil
}

// UpdateLastCheck 更新最后检查时间
func (r *ProxyRepository) UpdateLastCheck(ctx context.Context, id string, lastCheck time.Time, responseTime int64) error {
	query := `UPDATE proxies SET last_check = $1, response_time = $2, updated_at = $3 WHERE id = $4`

	result, err := r.db.ExecContext(ctx, query, lastCheck, responseTime, time.Now(), id)
	if err != nil {
		r.logger.WithError(err).WithField("proxy_id", id).Error("Failed to update proxy last check")
		return fmt.Errorf("failed to update proxy last check: %w", err)
	}

	rowsAffected, err := result.RowsAffected()
	if err != nil {
		return fmt.Errorf("failed to get rows affected: %w", err)
	}

	if rowsAffected == 0 {
		return fmt.Errorf("proxy not found")
	}

	return nil
}

// GetByLocation 根据地理位置获取代理
func (r *ProxyRepository) GetByLocation(ctx context.Context, countryCode, cityName string) ([]*models.Proxy, error) {
	query := `SELECT * FROM proxies WHERE 1=1`
	args := []interface{}{}
	argIndex := 1

	if countryCode != "" {
		query += fmt.Sprintf(" AND country_code = $%d", argIndex)
		args = append(args, countryCode)
		argIndex++
	}

	if cityName != "" {
		query += fmt.Sprintf(" AND city_name = $%d", argIndex)
		args = append(args, cityName)
	}

	query += " ORDER BY created_at DESC"

	var proxies []*models.Proxy
	err := r.db.SelectContext(ctx, &proxies, query, args...)
	if err != nil {
		r.logger.WithError(err).WithFields(logrus.Fields{
			"country_code": countryCode,
			"city_name":    cityName,
		}).Error("Failed to get proxies by location")
		return nil, fmt.Errorf("failed to get proxies by location: %w", err)
	}

	return proxies, nil
}

// GetLocationStats 获取地理位置统计
func (r *ProxyRepository) GetLocationStats(ctx context.Context) (*models.ProxyLocationStats, error) {
	// 获取国家统计
	countryQuery := `
		SELECT
			country_code as location,
			country_code,
			'' as city_name,
			COUNT(*) as total,
			COUNT(CASE WHEN status = 'active' THEN 1 END) as active,
			COUNT(CASE WHEN status = 'inactive' THEN 1 END) as inactive,
			COUNT(CASE WHEN status = 'failed' THEN 1 END) as failed,
			COALESCE(AVG(CASE WHEN response_time IS NOT NULL THEN response_time END), 0) as avg_response
		FROM proxies
		WHERE country_code IS NOT NULL AND country_code != ''
		GROUP BY country_code
		ORDER BY total DESC`

	var countries []models.LocationStats
	err := r.db.SelectContext(ctx, &countries, countryQuery)
	if err != nil {
		r.logger.WithError(err).Error("Failed to get country stats")
		return nil, fmt.Errorf("failed to get country stats: %w", err)
	}

	// 计算成功率
	for i := range countries {
		if countries[i].Total > 0 {
			countries[i].SuccessRate = float64(countries[i].Active) / float64(countries[i].Total) * 100
		}
	}

	// 获取城市统计
	cityQuery := `
		SELECT
			CONCAT(country_code, '_', city_name) as location,
			country_code,
			city_name,
			COUNT(*) as total,
			COUNT(CASE WHEN status = 'active' THEN 1 END) as active,
			COUNT(CASE WHEN status = 'inactive' THEN 1 END) as inactive,
			COUNT(CASE WHEN status = 'failed' THEN 1 END) as failed,
			COALESCE(AVG(CASE WHEN response_time IS NOT NULL THEN response_time END), 0) as avg_response
		FROM proxies
		WHERE country_code IS NOT NULL AND country_code != ''
		  AND city_name IS NOT NULL AND city_name != ''
		GROUP BY country_code, city_name
		ORDER BY total DESC`

	var cities []models.LocationStats
	err = r.db.SelectContext(ctx, &cities, cityQuery)
	if err != nil {
		r.logger.WithError(err).Error("Failed to get city stats")
		return nil, fmt.Errorf("failed to get city stats: %w", err)
	}

	// 计算成功率
	for i := range cities {
		if cities[i].Total > 0 {
			cities[i].SuccessRate = float64(cities[i].Active) / float64(cities[i].Total) * 100
		}
	}

	// 获取未知位置统计
	unknownQuery := `
		SELECT
			'unknown' as location,
			'' as country_code,
			'' as city_name,
			COUNT(*) as total,
			COUNT(CASE WHEN status = 'active' THEN 1 END) as active,
			COUNT(CASE WHEN status = 'inactive' THEN 1 END) as inactive,
			COUNT(CASE WHEN status = 'failed' THEN 1 END) as failed,
			COALESCE(AVG(CASE WHEN response_time IS NOT NULL THEN response_time END), 0) as avg_response
		FROM proxies
		WHERE country_code IS NULL OR country_code = ''`

	var unknown models.LocationStats
	err = r.db.GetContext(ctx, &unknown, unknownQuery)
	if err != nil {
		r.logger.WithError(err).Error("Failed to get unknown location stats")
		return nil, fmt.Errorf("failed to get unknown location stats: %w", err)
	}

	if unknown.Total > 0 {
		unknown.SuccessRate = float64(unknown.Active) / float64(unknown.Total) * 100
	}

	return &models.ProxyLocationStats{
		Countries: countries,
		Cities:    cities,
		Unknown:   unknown,
	}, nil
}

// GroupByLocation 按地理位置分组代理
func (r *ProxyRepository) GroupByLocation(ctx context.Context, groupBy string) ([]models.ProxyGroupByLocation, error) {
	var groups []models.ProxyGroupByLocation

	if groupBy == "country" {
		// 按国家分组
		query := `
			SELECT
				country_code as location,
				country_code as name,
				COUNT(*) as count
			FROM proxies
			WHERE country_code IS NOT NULL AND country_code != ''
			GROUP BY country_code
			ORDER BY count DESC`

		type groupResult struct {
			Location string `db:"location"`
			Name     string `db:"name"`
			Count    int    `db:"count"`
		}

		var results []groupResult
		err := r.db.SelectContext(ctx, &results, query)
		if err != nil {
			return nil, fmt.Errorf("failed to group by country: %w", err)
		}

		for _, result := range results {
			// 获取该组的代理列表
			proxies, err := r.GetByLocation(ctx, result.Location, "")
			if err != nil {
				return nil, fmt.Errorf("failed to get proxies for country %s: %w", result.Location, err)
			}

			// 转换为响应格式
			var proxyResponses []models.ProxyResponse
			for _, p := range proxies {
				proxyResponses = append(proxyResponses, r.convertToResponse(p))
			}

			// 计算统计信息
			stats := r.calculateLocationStats(proxies)
			stats.Location = result.Name
			stats.CountryCode = result.Location

			groups = append(groups, models.ProxyGroupByLocation{
				Location: result.Location,
				Name:     result.Name,
				Count:    result.Count,
				Proxies:  proxyResponses,
				Stats:    stats,
			})
		}
	} else if groupBy == "city" {
		// 按城市分组
		query := `
			SELECT
				CONCAT(country_code, '_', city_name) as location,
				CONCAT(city_name, ' (', country_code, ')') as name,
				COUNT(*) as count
			FROM proxies
			WHERE country_code IS NOT NULL AND country_code != ''
			  AND city_name IS NOT NULL AND city_name != ''
			GROUP BY country_code, city_name
			ORDER BY count DESC`

		type groupResult struct {
			Location string `db:"location"`
			Name     string `db:"name"`
			Count    int    `db:"count"`
		}

		var results []groupResult
		err := r.db.SelectContext(ctx, &results, query)
		if err != nil {
			return nil, fmt.Errorf("failed to group by city: %w", err)
		}

		for _, result := range results {
			// 解析location获取country_code和city_name
			parts := strings.Split(result.Location, "_")
			if len(parts) != 2 {
				continue
			}
			countryCode, cityName := parts[0], parts[1]

			// 获取该组的代理列表
			proxies, err := r.GetByLocation(ctx, countryCode, cityName)
			if err != nil {
				return nil, fmt.Errorf("failed to get proxies for city %s: %w", result.Location, err)
			}

			// 转换为响应格式
			var proxyResponses []models.ProxyResponse
			for _, p := range proxies {
				proxyResponses = append(proxyResponses, r.convertToResponse(p))
			}

			// 计算统计信息
			stats := r.calculateLocationStats(proxies)
			stats.Location = result.Name
			stats.CountryCode = countryCode
			stats.CityName = cityName

			groups = append(groups, models.ProxyGroupByLocation{
				Location: result.Location,
				Name:     result.Name,
				Count:    result.Count,
				Proxies:  proxyResponses,
				Stats:    stats,
			})
		}
	}

	return groups, nil
}

// UpdateQualityScore 更新质量评分
func (r *ProxyRepository) UpdateQualityScore(ctx context.Context, id string, score models.QualityScore) error {
	query := `
		UPDATE proxies SET
			quality_score = $1, speed_score = $2, stability_score = $3,
			reliability_score = $4, anonymity_level = $5, last_quality_check = $6,
			updated_at = $7
		WHERE id = $8`

	result, err := r.db.ExecContext(ctx, query,
		score.Overall, score.Speed, score.Stability, score.Reliability,
		score.AnonymityLevel, time.Now(), time.Now(), id)
	if err != nil {
		r.logger.WithError(err).WithField("proxy_id", id).Error("Failed to update quality score")
		return fmt.Errorf("failed to update quality score: %w", err)
	}

	rowsAffected, err := result.RowsAffected()
	if err != nil {
		return fmt.Errorf("failed to get rows affected: %w", err)
	}

	if rowsAffected == 0 {
		return fmt.Errorf("proxy not found")
	}

	return nil
}

// GetByQualityRange 根据质量评分范围获取代理
func (r *ProxyRepository) GetByQualityRange(ctx context.Context, minScore, maxScore float64) ([]*models.Proxy, error) {
	query := `
		SELECT * FROM proxies
		WHERE quality_score >= $1 AND quality_score <= $2
		ORDER BY quality_score DESC`

	var proxies []*models.Proxy
	err := r.db.SelectContext(ctx, &proxies, query, minScore, maxScore)
	if err != nil {
		r.logger.WithError(err).WithFields(logrus.Fields{
			"min_score": minScore,
			"max_score": maxScore,
		}).Error("Failed to get proxies by quality range")
		return nil, fmt.Errorf("failed to get proxies by quality range: %w", err)
	}

	return proxies, nil
}

// GetTopQualityProxies 获取质量最高的代理
func (r *ProxyRepository) GetTopQualityProxies(ctx context.Context, limit int) ([]*models.Proxy, error) {
	query := `
		SELECT * FROM proxies
		WHERE quality_score > 0
		ORDER BY quality_score DESC
		LIMIT $1`

	var proxies []*models.Proxy
	err := r.db.SelectContext(ctx, &proxies, query, limit)
	if err != nil {
		r.logger.WithError(err).WithField("limit", limit).Error("Failed to get top quality proxies")
		return nil, fmt.Errorf("failed to get top quality proxies: %w", err)
	}

	return proxies, nil
}

// convertToResponse 转换为响应格式
func (r *ProxyRepository) convertToResponse(proxy *models.Proxy) models.ProxyResponse {
	response := models.ProxyResponse{
		ID:                    proxy.ID,
		Host:                  proxy.Host,
		Port:                  proxy.Port,
		Type:                  proxy.Type,
		Status:                proxy.Status,
		UseCount:              proxy.UseCount,
		Weight:                proxy.Weight,
		CountryCode:           proxy.CountryCode,
		CityName:              proxy.CityName,
		ASNName:               proxy.ASNName,
		HighCountryConfidence: proxy.HighCountryConfidence,
	}

	// 处理可能为nil的字段
	if proxy.LastCheck != nil {
		response.LastCheck = *proxy.LastCheck
	}
	if proxy.LastSuccess != nil {
		response.LastSuccess = *proxy.LastSuccess
	}
	if proxy.ResponseTime != nil {
		response.ResponseTime = *proxy.ResponseTime
	}
	if proxy.ASNNumber != nil {
		response.ASNNumber = *proxy.ASNNumber
	}

	return response
}

// calculateLocationStats 计算位置统计信息
func (r *ProxyRepository) calculateLocationStats(proxies []*models.Proxy) models.LocationStats {
	stats := models.LocationStats{
		Total: len(proxies),
	}

	var totalResponseTime float64
	var responseTimeCount int

	for _, proxy := range proxies {
		switch proxy.Status {
		case models.ProxyStatusActive:
			stats.Active++
		case models.ProxyStatusInactive:
			stats.Inactive++
		case models.ProxyStatusFailed:
			stats.Failed++
		}

		if proxy.ResponseTime != nil && *proxy.ResponseTime > 0 {
			totalResponseTime += float64(*proxy.ResponseTime)
			responseTimeCount++
		}
	}

	if responseTimeCount > 0 {
		stats.AvgResponse = totalResponseTime / float64(responseTimeCount)
	}

	if stats.Total > 0 {
		stats.SuccessRate = float64(stats.Active) / float64(stats.Total) * 100
	}

	return stats
}

// GetByTags 根据标签获取代理
func (r *ProxyRepository) GetByTags(ctx context.Context, tagNames []string) ([]*models.Proxy, error) {
	if len(tagNames) == 0 {
		return []*models.Proxy{}, nil
	}

	// 构建查询，获取包含任一指定标签的代理
	placeholders := make([]string, len(tagNames))
	args := make([]interface{}, len(tagNames))
	for i, tag := range tagNames {
		placeholders[i] = fmt.Sprintf("$%d", i+1)
		args[i] = tag
	}

	query := fmt.Sprintf(`
		SELECT DISTINCT p.* FROM proxies p
		INNER JOIN proxy_tag_assignments pta ON p.id = pta.proxy_id
		INNER JOIN proxy_tags pt ON pta.tag_id = pt.id
		WHERE pt.name IN (%s)
		ORDER BY p.priority DESC, p.created_at DESC`,
		strings.Join(placeholders, ","))

	var proxies []*models.Proxy
	err := r.db.SelectContext(ctx, &proxies, query, args...)
	if err != nil {
		r.logger.WithError(err).WithField("tags", tagNames).Error("Failed to get proxies by tags")
		return nil, fmt.Errorf("failed to get proxies by tags: %w", err)
	}

	// 为每个代理加载标签信息
	for _, proxy := range proxies {
		tags, err := r.GetProxyTags(ctx, proxy.ID)
		if err != nil {
			r.logger.WithError(err).WithField("proxy_id", proxy.ID).Error("Failed to load proxy tags")
			continue
		}
		proxy.Tags = tags
	}

	return proxies, nil
}

// GetByScenario 根据场景获取代理
func (r *ProxyRepository) GetByScenario(ctx context.Context, scenario string) ([]*models.Proxy, error) {
	query := `
		SELECT * FROM proxies
		WHERE scenario = $1
		ORDER BY priority DESC, created_at DESC`

	var proxies []*models.Proxy
	err := r.db.SelectContext(ctx, &proxies, query, scenario)
	if err != nil {
		r.logger.WithError(err).WithField("scenario", scenario).Error("Failed to get proxies by scenario")
		return nil, fmt.Errorf("failed to get proxies by scenario: %w", err)
	}

	// 为每个代理加载标签信息
	for _, proxy := range proxies {
		tags, err := r.GetProxyTags(ctx, proxy.ID)
		if err != nil {
			r.logger.WithError(err).WithField("proxy_id", proxy.ID).Error("Failed to load proxy tags")
			continue
		}
		proxy.Tags = tags
	}

	return proxies, nil
}

// AssignTags 为代理分配标签
func (r *ProxyRepository) AssignTags(ctx context.Context, proxyID string, tagIDs []string, assignedBy string) error {
	if len(tagIDs) == 0 {
		return nil
	}

	tx, err := r.db.BeginTxx(ctx, nil)
	if err != nil {
		return fmt.Errorf("failed to begin transaction: %w", err)
	}
	defer tx.Rollback()

	// 删除现有的标签关联
	_, err = tx.ExecContext(ctx, "DELETE FROM proxy_tag_assignments WHERE proxy_id = $1", proxyID)
	if err != nil {
		return fmt.Errorf("failed to remove existing tags: %w", err)
	}

	// 添加新的标签关联
	for _, tagID := range tagIDs {
		query := `
			INSERT INTO proxy_tag_assignments (proxy_id, tag_id, assigned_by)
			VALUES ($1, $2, $3)
			ON CONFLICT (proxy_id, tag_id) DO NOTHING`

		_, err = tx.ExecContext(ctx, query, proxyID, tagID, assignedBy)
		if err != nil {
			return fmt.Errorf("failed to assign tag %s: %w", tagID, err)
		}
	}

	return tx.Commit()
}

// RemoveTags 移除代理的标签
func (r *ProxyRepository) RemoveTags(ctx context.Context, proxyID string, tagIDs []string) error {
	if len(tagIDs) == 0 {
		return nil
	}

	placeholders := make([]string, len(tagIDs))
	args := []interface{}{proxyID}
	for i, tagID := range tagIDs {
		placeholders[i] = fmt.Sprintf("$%d", i+2)
		args = append(args, tagID)
	}

	query := fmt.Sprintf(`
		DELETE FROM proxy_tag_assignments
		WHERE proxy_id = $1 AND tag_id IN (%s)`,
		strings.Join(placeholders, ","))

	_, err := r.db.ExecContext(ctx, query, args...)
	if err != nil {
		r.logger.WithError(err).WithFields(logrus.Fields{
			"proxy_id": proxyID,
			"tag_ids":  tagIDs,
		}).Error("Failed to remove proxy tags")
		return fmt.Errorf("failed to remove proxy tags: %w", err)
	}

	return nil
}

// GetProxyTags 获取代理的标签
func (r *ProxyRepository) GetProxyTags(ctx context.Context, proxyID string) ([]*models.ProxyTag, error) {
	query := `
		SELECT pt.id, pt.name, pt.description, pt.color, pt.created_at, pt.updated_at, pt.created_by
		FROM proxy_tags pt
		INNER JOIN proxy_tag_assignments pta ON pt.id = pta.tag_id
		WHERE pta.proxy_id = $1
		ORDER BY pt.name`

	var tags []*models.ProxyTag
	err := r.db.SelectContext(ctx, &tags, query, proxyID)
	if err != nil {
		r.logger.WithError(err).WithField("proxy_id", proxyID).Error("Failed to get proxy tags")
		return nil, fmt.Errorf("failed to get proxy tags: %w", err)
	}

	return tags, nil
}
