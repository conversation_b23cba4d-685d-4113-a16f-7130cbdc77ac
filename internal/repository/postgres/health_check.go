package postgres

import (
	"context"
	"database/sql"
	"fmt"
	"time"

	"github.com/jmoiron/sqlx"
	"github.com/sirupsen/logrus"

	"proxyManager/internal/models"
	"proxyManager/internal/repository"
)

// HealthCheckRepository PostgreSQL健康检查记录仓储实现
type HealthCheckRepository struct {
	db     *sqlx.DB
	logger *logrus.Logger
}

// NewHealthCheckRepository 创建健康检查记录仓储
func NewHealthCheckRepository(db *sqlx.DB, logger *logrus.Logger) repository.HealthCheckRepository {
	return &HealthCheckRepository{
		db:     db,
		logger: logger,
	}
}

// Create 创建健康检查记录
func (r *HealthCheckRepository) Create(ctx context.Context, record *models.HealthCheckRecord) error {
	query := `
		INSERT INTO health_checks (
			id, proxy_id, check_time, status, response_time, error_message,
			test_url, http_status_code, anonymity_detected, real_ip, detected_location
		) VALUES (
			:id, :proxy_id, :check_time, :status, :response_time, :error_message,
			:test_url, :http_status_code, :anonymity_detected, :real_ip, :detected_location
		)`

	_, err := r.db.NamedExecContext(ctx, query, record)
	if err != nil {
		r.logger.WithError(err).WithField("proxy_id", record.ProxyID).Error("Failed to create health check record")
		return fmt.Errorf("failed to create health check record: %w", err)
	}

	return nil
}

// GetByProxyID 根据代理ID获取健康检查记录
func (r *HealthCheckRepository) GetByProxyID(ctx context.Context, proxyID string, limit int) ([]*models.HealthCheckRecord, error) {
	var records []*models.HealthCheckRecord
	query := `
		SELECT id, proxy_id, check_time, status, response_time, error_message,
			   test_url, http_status_code, anonymity_detected, real_ip, detected_location
		FROM health_checks 
		WHERE proxy_id = $1 
		ORDER BY check_time DESC 
		LIMIT $2`

	err := r.db.SelectContext(ctx, &records, query, proxyID, limit)
	if err != nil {
		r.logger.WithError(err).WithField("proxy_id", proxyID).Error("Failed to get health check records by proxy ID")
		return nil, fmt.Errorf("failed to get health check records by proxy ID: %w", err)
	}

	return records, nil
}

// GetByTimeRange 根据时间范围获取健康检查记录
func (r *HealthCheckRepository) GetByTimeRange(ctx context.Context, start, end time.Time) ([]*models.HealthCheckRecord, error) {
	var records []*models.HealthCheckRecord
	query := `
		SELECT id, proxy_id, check_time, status, response_time, error_message,
			   test_url, http_status_code, anonymity_detected, real_ip, detected_location
		FROM health_checks 
		WHERE check_time BETWEEN $1 AND $2 
		ORDER BY check_time DESC`

	err := r.db.SelectContext(ctx, &records, query, start, end)
	if err != nil {
		r.logger.WithError(err).Error("Failed to get health check records by time range")
		return nil, fmt.Errorf("failed to get health check records by time range: %w", err)
	}

	return records, nil
}

// DeleteOldRecords 删除旧的健康检查记录
func (r *HealthCheckRepository) DeleteOldRecords(ctx context.Context, before time.Time) error {
	query := `DELETE FROM health_checks WHERE check_time < $1`

	result, err := r.db.ExecContext(ctx, query, before)
	if err != nil {
		r.logger.WithError(err).Error("Failed to delete old health check records")
		return fmt.Errorf("failed to delete old health check records: %w", err)
	}

	rowsAffected, err := result.RowsAffected()
	if err != nil {
		return fmt.Errorf("failed to get rows affected: %w", err)
	}

	r.logger.WithField("rows_deleted", rowsAffected).Info("Deleted old health check records")
	return nil
}

// GetStats 获取健康检查统计信息
func (r *HealthCheckRepository) GetStats(ctx context.Context, proxyID string, duration time.Duration) (*models.HealthCheckStats, error) {
	since := time.Now().Add(-duration)
	
	query := `
		SELECT 
			COUNT(*) as total_checks,
			COUNT(CASE WHEN status = 'success' THEN 1 END) as success_count,
			COUNT(CASE WHEN status = 'failed' THEN 1 END) as failed_count,
			COUNT(CASE WHEN status = 'timeout' THEN 1 END) as timeout_count,
			AVG(CASE WHEN response_time IS NOT NULL THEN response_time END) as avg_response_time,
			MIN(CASE WHEN response_time IS NOT NULL THEN response_time END) as min_response_time,
			MAX(CASE WHEN response_time IS NOT NULL THEN response_time END) as max_response_time
		FROM health_checks 
		WHERE proxy_id = $1 AND check_time >= $2`

	var stats struct {
		TotalChecks       int64           `db:"total_checks"`
		SuccessCount      int64           `db:"success_count"`
		FailedCount       int64           `db:"failed_count"`
		TimeoutCount      int64           `db:"timeout_count"`
		AvgResponseTime   sql.NullFloat64 `db:"avg_response_time"`
		MinResponseTime   sql.NullInt64   `db:"min_response_time"`
		MaxResponseTime   sql.NullInt64   `db:"max_response_time"`
	}

	err := r.db.GetContext(ctx, &stats, query, proxyID, since)
	if err != nil {
		r.logger.WithError(err).WithField("proxy_id", proxyID).Error("Failed to get health check stats")
		return nil, fmt.Errorf("failed to get health check stats: %w", err)
	}

	result := &models.HealthCheckStats{
		ProxyID:      proxyID,
		Duration:     duration,
		TotalChecks:  stats.TotalChecks,
		SuccessCount: stats.SuccessCount,
		FailedCount:  stats.FailedCount,
		TimeoutCount: stats.TimeoutCount,
	}

	if stats.AvgResponseTime.Valid {
		result.AvgResponseTime = int64(stats.AvgResponseTime.Float64)
	}
	if stats.MinResponseTime.Valid {
		result.MinResponseTime = stats.MinResponseTime.Int64
	}
	if stats.MaxResponseTime.Valid {
		result.MaxResponseTime = stats.MaxResponseTime.Int64
	}

	if stats.TotalChecks > 0 {
		result.SuccessRate = float64(stats.SuccessCount) / float64(stats.TotalChecks)
	}

	return result, nil
}
