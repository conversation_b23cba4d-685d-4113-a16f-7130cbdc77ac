package postgres

import (
	"context"
	"database/sql"
	"fmt"
	"time"

	"github.com/jmoiron/sqlx"
	"github.com/sirupsen/logrus"

	"proxyManager/internal/models"
	"proxyManager/internal/repository"
)

// TaskRepository PostgreSQL任务仓储实现
type TaskRepository struct {
	db     *sqlx.DB
	logger *logrus.Logger
}

// NewTaskRepository 创建任务仓储
func NewTaskRepository(db *sqlx.DB, logger *logrus.Logger) repository.TaskRepository {
	return &TaskRepository{
		db:     db,
		logger: logger,
	}
}

// Create 创建任务
func (r *TaskRepository) Create(ctx context.Context, task *models.Task) error {
	query := `
		INSERT INTO tasks (
			id, user_id, name, description, type, status, config, result,
			progress, created_at, updated_at, started_at, completed_at, error_message
		) VALUES (
			:id, :user_id, :name, :description, :type, :status, :config, :result,
			:progress, :created_at, :updated_at, :started_at, :completed_at, :error_message
		)`

	_, err := r.db.NamedExecContext(ctx, query, task)
	if err != nil {
		r.logger.WithError(err).WithField("task_id", task.ID).Error("Failed to create task")
		return fmt.Errorf("failed to create task: %w", err)
	}

	return nil
}

// GetByID 根据ID获取任务
func (r *TaskRepository) GetByID(ctx context.Context, id string) (*models.Task, error) {
	var task models.Task
	query := `
		SELECT id, user_id, name, description, type, status, config, result,
			   progress, created_at, updated_at, started_at, completed_at, error_message
		FROM tasks WHERE id = $1`

	err := r.db.GetContext(ctx, &task, query, id)
	if err != nil {
		if err == sql.ErrNoRows {
			return nil, nil
		}
		r.logger.WithError(err).WithField("task_id", id).Error("Failed to get task by ID")
		return nil, fmt.Errorf("failed to get task by ID: %w", err)
	}

	return &task, nil
}

// GetByUserID 根据用户ID获取任务列表
func (r *TaskRepository) GetByUserID(ctx context.Context, userID string, filters repository.TaskFilters) ([]*models.Task, error) {
	var tasks []*models.Task

	query := `
		SELECT id, user_id, name, description, type, status, config, result,
			   progress, created_at, updated_at, started_at, completed_at, error_message
		FROM tasks WHERE user_id = $1`

	args := []interface{}{userID}
	argIndex := 2

	// 添加状态过滤
	if filters.Status != nil {
		query += fmt.Sprintf(" AND status = $%d", argIndex)
		args = append(args, *filters.Status)
		argIndex++
	}

	// 添加类型过滤
	if filters.Type != nil && *filters.Type != "" {
		query += fmt.Sprintf(" AND type = $%d", argIndex)
		args = append(args, *filters.Type)
		argIndex++
	}

	// 添加排序
	query += " ORDER BY created_at DESC"

	// 添加分页
	if filters.Limit != nil && *filters.Limit > 0 {
		query += fmt.Sprintf(" LIMIT $%d", argIndex)
		args = append(args, *filters.Limit)
		argIndex++
	}

	if filters.Offset != nil && *filters.Offset > 0 {
		query += fmt.Sprintf(" OFFSET $%d", argIndex)
		args = append(args, *filters.Offset)
	}

	err := r.db.SelectContext(ctx, &tasks, query, args...)
	if err != nil {
		r.logger.WithError(err).WithField("user_id", userID).Error("Failed to get tasks by user ID")
		return nil, fmt.Errorf("failed to get tasks by user ID: %w", err)
	}

	return tasks, nil
}

// Update 更新任务
func (r *TaskRepository) Update(ctx context.Context, task *models.Task) error {
	query := `
		UPDATE tasks SET 
			name = :name, description = :description, type = :type, status = :status,
			config = :config, result = :result, progress = :progress, updated_at = :updated_at,
			started_at = :started_at, completed_at = :completed_at, error_message = :error_message
		WHERE id = :id`

	result, err := r.db.NamedExecContext(ctx, query, task)
	if err != nil {
		r.logger.WithError(err).WithField("task_id", task.ID).Error("Failed to update task")
		return fmt.Errorf("failed to update task: %w", err)
	}

	rowsAffected, err := result.RowsAffected()
	if err != nil {
		return fmt.Errorf("failed to get rows affected: %w", err)
	}

	if rowsAffected == 0 {
		return fmt.Errorf("task not found")
	}

	return nil
}

// Delete 删除任务
func (r *TaskRepository) Delete(ctx context.Context, id string) error {
	query := `DELETE FROM tasks WHERE id = $1`

	result, err := r.db.ExecContext(ctx, query, id)
	if err != nil {
		r.logger.WithError(err).WithField("task_id", id).Error("Failed to delete task")
		return fmt.Errorf("failed to delete task: %w", err)
	}

	rowsAffected, err := result.RowsAffected()
	if err != nil {
		return fmt.Errorf("failed to get rows affected: %w", err)
	}

	if rowsAffected == 0 {
		return fmt.Errorf("task not found")
	}

	return nil
}

// UpdateStatus 更新任务状态
func (r *TaskRepository) UpdateStatus(ctx context.Context, id string, status models.TaskStatus, progress int) error {
	query := `
		UPDATE tasks SET 
			status = $2, progress = $3, updated_at = $4
		WHERE id = $1`

	result, err := r.db.ExecContext(ctx, query, id, status, progress, time.Now())
	if err != nil {
		r.logger.WithError(err).WithField("task_id", id).Error("Failed to update task status")
		return fmt.Errorf("failed to update task status: %w", err)
	}

	rowsAffected, err := result.RowsAffected()
	if err != nil {
		return fmt.Errorf("failed to get rows affected: %w", err)
	}

	if rowsAffected == 0 {
		return fmt.Errorf("task not found")
	}

	return nil
}

// GetRunningTasks 获取正在运行的任务
func (r *TaskRepository) GetRunningTasks(ctx context.Context) ([]*models.Task, error) {
	var tasks []*models.Task
	query := `
		SELECT id, user_id, name, description, type, status, config, result,
			   progress, created_at, updated_at, started_at, completed_at, error_message
		FROM tasks 
		WHERE status IN ('pending', 'running')
		ORDER BY created_at ASC`

	err := r.db.SelectContext(ctx, &tasks, query)
	if err != nil {
		r.logger.WithError(err).Error("Failed to get running tasks")
		return nil, fmt.Errorf("failed to get running tasks: %w", err)
	}

	return tasks, nil
}
