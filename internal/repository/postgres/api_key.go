package postgres

import (
	"context"
	"database/sql"
	"encoding/json"
	"fmt"
	"time"

	"proxyManager/internal/models"
	"proxyManager/internal/repository"

	"github.com/jmoiron/sqlx"
	"github.com/sirupsen/logrus"
)

// APIKeyRepository API密钥仓储实现
type APIKeyRepository struct {
	db     *sqlx.DB
	logger *logrus.Logger
}

// NewAPIKeyRepository 创建API密钥仓储
func NewAPIKeyRepository(db *sqlx.DB, logger *logrus.Logger) repository.APIKeyRepository {
	return &APIKeyRepository{
		db:     db,
		logger: logger,
	}
}

// Create 创建API密钥
func (r *APIKeyRepository) Create(ctx context.Context, apiKey *models.APIKey) error {
	permissionsJSON, err := json.Marshal(apiKey.Permissions)
	if err != nil {
		return fmt.Errorf("failed to marshal permissions: %w", err)
	}

	allowedIPsJSON, err := json.Marshal(apiKey.AllowedIPs)
	if err != nil {
		return fmt.Errorf("failed to marshal allowed IPs: %w", err)
	}

	metadataJSO<PERSON>, err := json.Marshal(apiKey.Metadata)
	if err != nil {
		return fmt.Errorf("failed to marshal metadata: %w", err)
	}

	query := `
		INSERT INTO api_keys (
			user_id, name, key_hash, key_prefix, full_api_key, permissions,
			is_active, expires_at, last_used_at, usage_count,
			rate_limit_per_minute, allowed_ips, metadata, created_at, updated_at
		) VALUES (
			$1, $2, $3, $4, $5, $6, $7,
			$8, $9, $10, $11, $12, $13, $14, $15
		) RETURNING id`

	err = r.db.QueryRowContext(ctx, query,
		apiKey.UserID, apiKey.Name, apiKey.KeyHash, apiKey.KeyPrefix, apiKey.FullAPIKey,
		permissionsJSON, apiKey.IsActive, apiKey.ExpiresAt, apiKey.LastUsedAt,
		apiKey.UsageCount, apiKey.RateLimitPerMinute, allowedIPsJSON, metadataJSON,
		apiKey.CreatedAt, apiKey.UpdatedAt,
	).Scan(&apiKey.ID)

	if err != nil {
		r.logger.WithError(err).Error("Failed to create API key")
		return fmt.Errorf("failed to create API key: %w", err)
	}

	return nil
}

// GetByID 根据ID获取API密钥
func (r *APIKeyRepository) GetByID(ctx context.Context, id string) (*models.APIKey, error) {
	query := `
		SELECT id, user_id, name, key_hash, key_prefix, full_api_key, permissions,
			   is_active, expires_at, last_used_at, usage_count,
			   rate_limit_per_minute, allowed_ips, metadata, created_at, updated_at
		FROM api_keys
		WHERE id = $1`

	apiKey := &models.APIKey{}
	var permissionsJSON, allowedIPsJSON, metadataJSON []byte

	err := r.db.QueryRowContext(ctx, query, id).Scan(
		&apiKey.ID, &apiKey.UserID, &apiKey.Name, &apiKey.KeyHash, &apiKey.KeyPrefix, &apiKey.FullAPIKey,
		&permissionsJSON, &apiKey.IsActive, &apiKey.ExpiresAt, &apiKey.LastUsedAt,
		&apiKey.UsageCount, &apiKey.RateLimitPerMinute, &allowedIPsJSON, &metadataJSON,
		&apiKey.CreatedAt, &apiKey.UpdatedAt,
	)

	if err != nil {
		if err == sql.ErrNoRows {
			return nil, nil
		}
		r.logger.WithError(err).WithField("id", id).Error("Failed to get API key by ID")
		return nil, fmt.Errorf("failed to get API key: %w", err)
	}

	// 解析JSON字段
	if err := json.Unmarshal(permissionsJSON, &apiKey.Permissions); err != nil {
		r.logger.WithError(err).Error("Failed to unmarshal permissions")
		apiKey.Permissions = make(map[string]interface{})
	}

	if err := json.Unmarshal(allowedIPsJSON, &apiKey.AllowedIPs); err != nil {
		r.logger.WithError(err).Error("Failed to unmarshal allowed IPs")
		apiKey.AllowedIPs = []string{}
	}

	if err := json.Unmarshal(metadataJSON, &apiKey.Metadata); err != nil {
		r.logger.WithError(err).Error("Failed to unmarshal metadata")
		apiKey.Metadata = make(map[string]interface{})
	}

	return apiKey, nil
}

// GetByUserID 根据用户ID获取API密钥列表
func (r *APIKeyRepository) GetByUserID(ctx context.Context, userID string) ([]*models.APIKey, error) {
	query := `
		SELECT id, user_id, name, key_hash, key_prefix, full_api_key, permissions,
			   is_active, expires_at, last_used_at, usage_count,
			   rate_limit_per_minute, allowed_ips, metadata, created_at, updated_at
		FROM api_keys
		WHERE user_id = $1
		ORDER BY created_at DESC`

	rows, err := r.db.QueryContext(ctx, query, userID)
	if err != nil {
		r.logger.WithError(err).WithField("user_id", userID).Error("Failed to get API keys by user ID")
		return nil, fmt.Errorf("failed to get API keys: %w", err)
	}
	defer rows.Close()

	var apiKeys []*models.APIKey
	for rows.Next() {
		apiKey := &models.APIKey{}
		var permissionsJSON, allowedIPsJSON, metadataJSON []byte

		err := rows.Scan(
			&apiKey.ID, &apiKey.UserID, &apiKey.Name, &apiKey.KeyHash, &apiKey.KeyPrefix, &apiKey.FullAPIKey,
			&permissionsJSON, &apiKey.IsActive, &apiKey.ExpiresAt, &apiKey.LastUsedAt,
			&apiKey.UsageCount, &apiKey.RateLimitPerMinute, &allowedIPsJSON, &metadataJSON,
			&apiKey.CreatedAt, &apiKey.UpdatedAt,
		)
		if err != nil {
			r.logger.WithError(err).Error("Failed to scan API key row")
			continue
		}

		// 解析JSON字段
		if err := json.Unmarshal(permissionsJSON, &apiKey.Permissions); err != nil {
			apiKey.Permissions = make(map[string]interface{})
		}
		if err := json.Unmarshal(allowedIPsJSON, &apiKey.AllowedIPs); err != nil {
			apiKey.AllowedIPs = []string{}
		}
		if err := json.Unmarshal(metadataJSON, &apiKey.Metadata); err != nil {
			apiKey.Metadata = make(map[string]interface{})
		}

		apiKeys = append(apiKeys, apiKey)
	}

	return apiKeys, nil
}

// GetByKeyHash 根据密钥哈希获取API密钥
func (r *APIKeyRepository) GetByKeyHash(ctx context.Context, keyHash string) (*models.APIKey, error) {
	query := `
		SELECT id, user_id, name, key_hash, key_prefix, full_api_key, permissions,
			   is_active, expires_at, last_used_at, usage_count,
			   rate_limit_per_minute, allowed_ips, metadata, created_at, updated_at
		FROM api_keys
		WHERE key_hash = $1 AND is_active = true`

	apiKey := &models.APIKey{}
	var permissionsJSON, allowedIPsJSON, metadataJSON []byte

	err := r.db.QueryRowContext(ctx, query, keyHash).Scan(
		&apiKey.ID, &apiKey.UserID, &apiKey.Name, &apiKey.KeyHash, &apiKey.KeyPrefix, &apiKey.FullAPIKey,
		&permissionsJSON, &apiKey.IsActive, &apiKey.ExpiresAt, &apiKey.LastUsedAt,
		&apiKey.UsageCount, &apiKey.RateLimitPerMinute, &allowedIPsJSON, &metadataJSON,
		&apiKey.CreatedAt, &apiKey.UpdatedAt,
	)

	if err != nil {
		if err == sql.ErrNoRows {
			return nil, nil
		}
		r.logger.WithError(err).WithField("key_hash", keyHash).Error("Failed to get API key by hash")
		return nil, fmt.Errorf("failed to get API key: %w", err)
	}

	// 解析JSON字段
	if err := json.Unmarshal(permissionsJSON, &apiKey.Permissions); err != nil {
		apiKey.Permissions = make(map[string]interface{})
	}
	if err := json.Unmarshal(allowedIPsJSON, &apiKey.AllowedIPs); err != nil {
		apiKey.AllowedIPs = []string{}
	}
	if err := json.Unmarshal(metadataJSON, &apiKey.Metadata); err != nil {
		apiKey.Metadata = make(map[string]interface{})
	}

	return apiKey, nil
}

// Update 更新API密钥
func (r *APIKeyRepository) Update(ctx context.Context, apiKey *models.APIKey) error {
	permissionsJSON, err := json.Marshal(apiKey.Permissions)
	if err != nil {
		return fmt.Errorf("failed to marshal permissions: %w", err)
	}

	allowedIPsJSON, err := json.Marshal(apiKey.AllowedIPs)
	if err != nil {
		return fmt.Errorf("failed to marshal allowed IPs: %w", err)
	}

	metadataJSON, err := json.Marshal(apiKey.Metadata)
	if err != nil {
		return fmt.Errorf("failed to marshal metadata: %w", err)
	}

	query := `
		UPDATE api_keys SET
			name = $2, permissions = $3, is_active = $4, expires_at = $5,
			last_used_at = $6, usage_count = $7, rate_limit_per_minute = $8,
			allowed_ips = $9, metadata = $10, updated_at = $11
		WHERE id = $1`

	_, err = r.db.ExecContext(ctx, query,
		apiKey.ID, apiKey.Name, permissionsJSON, apiKey.IsActive, apiKey.ExpiresAt,
		apiKey.LastUsedAt, apiKey.UsageCount, apiKey.RateLimitPerMinute,
		allowedIPsJSON, metadataJSON, time.Now(),
	)

	if err != nil {
		r.logger.WithError(err).WithField("id", apiKey.ID).Error("Failed to update API key")
		return fmt.Errorf("failed to update API key: %w", err)
	}

	return nil
}

// Delete 删除API密钥
func (r *APIKeyRepository) Delete(ctx context.Context, id string) error {
	query := `DELETE FROM api_keys WHERE id = $1`

	_, err := r.db.ExecContext(ctx, query, id)
	if err != nil {
		r.logger.WithError(err).WithField("id", id).Error("Failed to delete API key")
		return fmt.Errorf("failed to delete API key: %w", err)
	}

	return nil
}

// UpdateUsage 更新API密钥使用统计
func (r *APIKeyRepository) UpdateUsage(ctx context.Context, id string) error {
	query := `
		UPDATE api_keys SET
			usage_count = usage_count + 1,
			last_used_at = $2,
			updated_at = $2
		WHERE id = $1`

	now := time.Now()
	_, err := r.db.ExecContext(ctx, query, id, now)
	if err != nil {
		r.logger.WithError(err).WithField("id", id).Error("Failed to update API key usage")
		return fmt.Errorf("failed to update API key usage: %w", err)
	}

	return nil
}

// LogUsage 记录API密钥使用日志
func (r *APIKeyRepository) LogUsage(ctx context.Context, log *models.APIKeyUsageLog) error {
	query := `
		INSERT INTO api_key_usage_logs (
			id, api_key_id, endpoint, method, ip_address, user_agent,
			response_status, response_time_ms, request_size_bytes,
			response_size_bytes, created_at
		) VALUES (
			COALESCE(NULLIF($1, ''), gen_random_uuid()), $2, $3, $4, $5, $6,
			$7, $8, $9, $10, $11
		) RETURNING id`

	err := r.db.QueryRowContext(ctx, query,
		log.ID, log.APIKeyID, log.Endpoint, log.Method, log.IPAddress,
		log.UserAgent, log.ResponseStatus, log.ResponseTimeMs,
		log.RequestSizeBytes, log.ResponseSizeBytes, log.CreatedAt,
	).Scan(&log.ID)

	if err != nil {
		r.logger.WithError(err).Error("Failed to log API key usage")
		return fmt.Errorf("failed to log API key usage: %w", err)
	}

	return nil
}
