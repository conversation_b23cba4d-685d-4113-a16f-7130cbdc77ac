package repository

import (
	"context"
	"time"

	"proxyManager/internal/models"
)

// ProxyRepository 代理数据访问接口
type ProxyRepository interface {
	// 基础CRUD操作
	Create(ctx context.Context, proxy *models.Proxy) error
	GetByID(ctx context.Context, id string) (*models.Proxy, error)
	GetByHostPort(ctx context.Context, host string, port int) (*models.Proxy, error)
	Update(ctx context.Context, proxy *models.Proxy) error
	Delete(ctx context.Context, id string) error
	List(ctx context.Context, filters ProxyFilters) ([]*models.Proxy, error)
	Count(ctx context.Context, filters ProxyFilters) (int64, error)
	
	// 分页查询
	ListWithPagination(ctx context.Context, filters ProxyFilters, pagination models.PaginationParams) ([]*models.Proxy, int64, error)

	// 批量操作
	CreateBatch(ctx context.Context, proxies []*models.Proxy) error
	UpdateBatch(ctx context.Context, proxies []*models.Proxy) error
	DeleteBatch(ctx context.Context, ids []string) error

	// 状态相关
	GetByStatus(ctx context.Context, status models.ProxyStatus) ([]*models.Proxy, error)
	UpdateStatus(ctx context.Context, id string, status models.ProxyStatus) error
	UpdateLastCheck(ctx context.Context, id string, lastCheck time.Time, responseTime int64) error

	// 地理位置相关
	GetByLocation(ctx context.Context, countryCode, cityName string) ([]*models.Proxy, error)
	GetLocationStats(ctx context.Context) (*models.ProxyLocationStats, error)
	GroupByLocation(ctx context.Context, groupBy string) ([]models.ProxyGroupByLocation, error)

	// 质量评估相关
	UpdateQualityScore(ctx context.Context, id string, score models.QualityScore) error
	GetByQualityRange(ctx context.Context, minScore, maxScore float64) ([]*models.Proxy, error)
	GetTopQualityProxies(ctx context.Context, limit int) ([]*models.Proxy, error)

	// 标签相关
	GetByTags(ctx context.Context, tagNames []string) ([]*models.Proxy, error)
	GetByScenario(ctx context.Context, scenario string) ([]*models.Proxy, error)
	AssignTags(ctx context.Context, proxyID string, tagIDs []string, assignedBy string) error
	RemoveTags(ctx context.Context, proxyID string, tagIDs []string) error
	GetProxyTags(ctx context.Context, proxyID string) ([]*models.ProxyTag, error)
}

// ProxyTagRepository 代理标签数据访问接口
type ProxyTagRepository interface {
	Create(ctx context.Context, tag *models.ProxyTag) error
	GetByID(ctx context.Context, id string) (*models.ProxyTag, error)
	GetByName(ctx context.Context, name string) (*models.ProxyTag, error)
	Update(ctx context.Context, tag *models.ProxyTag) error
	Delete(ctx context.Context, id string) error
	List(ctx context.Context) ([]*models.ProxyTag, error)
	GetByProxyID(ctx context.Context, proxyID string) ([]*models.ProxyTag, error)
}

// UserRepository 用户数据访问接口
type UserRepository interface {
	Create(ctx context.Context, user *models.User) error
	GetByID(ctx context.Context, id string) (*models.User, error)
	GetByUsername(ctx context.Context, username string) (*models.User, error)
	GetByEmail(ctx context.Context, email string) (*models.User, error)
	Update(ctx context.Context, user *models.User) error
	Delete(ctx context.Context, id string) error
	List(ctx context.Context, filters UserFilters) ([]*models.User, error)
	UpdateLastLogin(ctx context.Context, id string, lastLogin time.Time) error
}

// HealthCheckRepository 健康检查记录数据访问接口
type HealthCheckRepository interface {
	Create(ctx context.Context, record *models.HealthCheckRecord) error
	GetByProxyID(ctx context.Context, proxyID string, limit int) ([]*models.HealthCheckRecord, error)
	GetByTimeRange(ctx context.Context, start, end time.Time) ([]*models.HealthCheckRecord, error)
	DeleteOldRecords(ctx context.Context, before time.Time) error
	GetStats(ctx context.Context, proxyID string, duration time.Duration) (*models.HealthCheckStats, error)
}

// TaskRepository 任务数据访问接口
type TaskRepository interface {
	Create(ctx context.Context, task *models.Task) error
	GetByID(ctx context.Context, id string) (*models.Task, error)
	GetByUserID(ctx context.Context, userID string, filters TaskFilters) ([]*models.Task, error)
	Update(ctx context.Context, task *models.Task) error
	Delete(ctx context.Context, id string) error
	UpdateStatus(ctx context.Context, id string, status models.TaskStatus, progress int) error
	GetRunningTasks(ctx context.Context) ([]*models.Task, error)
}

// AlertRepository 告警数据访问接口
type AlertRepository interface {
	// 告警规则
	CreateRule(ctx context.Context, rule *models.AlertRule) error
	GetRuleByID(ctx context.Context, id string) (*models.AlertRule, error)
	UpdateRule(ctx context.Context, rule *models.AlertRule) error
	DeleteRule(ctx context.Context, id string) error
	ListRules(ctx context.Context, filters AlertRuleFilters) ([]*models.AlertRule, error)
	GetEnabledRules(ctx context.Context) ([]*models.AlertRule, error)

	// 告警记录
	CreateAlert(ctx context.Context, alert *models.Alert) error
	GetAlertByID(ctx context.Context, id string) (*models.Alert, error)
	UpdateAlert(ctx context.Context, alert *models.Alert) error
	ListAlerts(ctx context.Context, filters AlertFilters) ([]*models.Alert, error)
	ResolveAlert(ctx context.Context, id string) error
	GetActiveAlerts(ctx context.Context) ([]*models.Alert, error)
}

// SystemConfigRepository 系统配置数据访问接口
type SystemConfigRepository interface {
	Get(ctx context.Context, key string) (interface{}, error)
	Set(ctx context.Context, key string, value interface{}) error
	GetAll(ctx context.Context) (map[string]interface{}, error)
	Delete(ctx context.Context, key string) error
}

// 过滤器结构
type ProxyFilters struct {
	Status      *models.ProxyStatus
	Type        *models.ProxyType
	CountryCode *string
	CityName    *string
	MinQuality  *float64
	MaxQuality  *float64
	Search      *string
	Limit       *int
	Offset      *int
	OrderBy     *string
	OrderDir    *string
}

type UserFilters struct {
	Role     *models.UserRole
	IsActive *bool
	Search   *string
	Limit    *int
	Offset   *int
}

type TaskFilters struct {
	Status *models.TaskStatus
	Type   *string
	Search *string
	Limit  *int
	Offset *int
}

type AlertRuleFilters struct {
	Enabled  *bool
	Severity *models.AlertSeverity
	Metric   *string
	Search   *string
	Limit    *int
	Offset   *int
}

type AlertFilters struct {
	RuleID   *string
	Status   *models.AlertStatus
	Severity *models.AlertSeverity
	From     *time.Time
	To       *time.Time
	Limit    *int
	Offset   *int
}

// Repository 仓储管理器
type Repository struct {
	Proxy       ProxyRepository
	User        UserRepository
	HealthCheck HealthCheckRepository
	Task        TaskRepository
	Alert       AlertRepository
	Config      SystemConfigRepository
	db          interface{ Close() error } // 数据库连接
}

// GetProxy 获取代理仓储
func (r *Repository) GetProxy() ProxyRepository {
	return r.Proxy
}

// GetUser 获取用户仓储
func (r *Repository) GetUser() UserRepository {
	return r.User
}

// GetHealthCheck 获取健康检查仓储
func (r *Repository) GetHealthCheck() HealthCheckRepository {
	return r.HealthCheck
}

// GetTask 获取任务仓储
func (r *Repository) GetTask() TaskRepository {
	return r.Task
}

// GetAlert 获取告警仓储
func (r *Repository) GetAlert() AlertRepository {
	return r.Alert
}

// GetConfig 获取配置仓储
func (r *Repository) GetConfig() SystemConfigRepository {
	return r.Config
}

// Close 关闭数据库连接
func (r *Repository) Close() error {
	if r.db != nil {
		return r.db.Close()
	}
	return nil
}

// RBACRepository RBAC权限管理数据访问接口
type RBACRepository interface {
	// 权限管理
	CreatePermission(ctx context.Context, permission *models.Permission) error
	GetPermissionByID(ctx context.Context, id string) (*models.Permission, error)
	GetPermissionByName(ctx context.Context, name string) (*models.Permission, error)
	ListPermissions(ctx context.Context) ([]*models.Permission, error)
	UpdatePermission(ctx context.Context, permission *models.Permission) error
	DeletePermission(ctx context.Context, id string) error

	// 角色管理
	CreateRole(ctx context.Context, role *models.Role) error
	GetRoleByID(ctx context.Context, id string) (*models.Role, error)
	GetRoleByName(ctx context.Context, name string) (*models.Role, error)
	ListRoles(ctx context.Context) ([]*models.Role, error)
	UpdateRole(ctx context.Context, role *models.Role) error
	DeleteRole(ctx context.Context, id string) error

	// 角色权限关联
	AssignPermissionsToRole(ctx context.Context, roleID string, permissionIDs []string, assignedBy string) error
	RemovePermissionsFromRole(ctx context.Context, roleID string, permissionIDs []string) error
	GetRolePermissions(ctx context.Context, roleID string) ([]*models.Permission, error)

	// 用户角色关联
	AssignRolesToUser(ctx context.Context, userID string, roleIDs []string, assignedBy string) error
	RemoveRolesFromUser(ctx context.Context, userID string, roleIDs []string) error
	GetUserRoles(ctx context.Context, userID string) ([]*models.Role, error)

	// 用户权限
	AssignPermissionToUser(ctx context.Context, userID string, permissionID string, granted bool, assignedBy string) error
	RemovePermissionFromUser(ctx context.Context, userID string, permissionID string) error
	GetUserPermissions(ctx context.Context, userID string) ([]*models.Permission, error)
	GetUserEffectivePermissions(ctx context.Context, userID string) ([]*models.Permission, error)

	// 团队管理
	CreateTeam(ctx context.Context, team *models.Team) error
	GetTeamByID(ctx context.Context, id string) (*models.Team, error)
	ListTeams(ctx context.Context) ([]*models.Team, error)
	UpdateTeam(ctx context.Context, team *models.Team) error
	DeleteTeam(ctx context.Context, id string) error

	// 团队成员管理
	AddTeamMember(ctx context.Context, teamID, userID, roleID string, addedBy string) error
	RemoveTeamMember(ctx context.Context, teamID, userID string) error
	GetTeamMembers(ctx context.Context, teamID string) ([]*models.UserWithRoles, error)
	GetUserTeams(ctx context.Context, userID string) ([]*models.Team, error)

	// 权限检查
	HasPermission(ctx context.Context, userID, permission string) (bool, error)
	HasResourceAccess(ctx context.Context, userID, resourceID, resourceType, permission string) (bool, error)
}

// APIKeyRepository API密钥仓储接口
type APIKeyRepository interface {
	Create(ctx context.Context, apiKey *models.APIKey) error
	GetByID(ctx context.Context, id string) (*models.APIKey, error)
	GetByUserID(ctx context.Context, userID string) ([]*models.APIKey, error)
	GetByKeyHash(ctx context.Context, keyHash string) (*models.APIKey, error)
	Update(ctx context.Context, apiKey *models.APIKey) error
	Delete(ctx context.Context, id string) error
	UpdateUsage(ctx context.Context, id string) error
	LogUsage(ctx context.Context, log *models.APIKeyUsageLog) error
}

// UserSettingsRepository 用户设置仓储接口
type UserSettingsRepository interface {
	Get(ctx context.Context, userID, key string) (interface{}, error)
	Set(ctx context.Context, userID, key string, value interface{}) error
	GetAll(ctx context.Context, userID string) (map[string]interface{}, error)
	SetMultiple(ctx context.Context, userID string, settings map[string]interface{}) error
	Delete(ctx context.Context, userID, key string) error
	DeleteAll(ctx context.Context, userID string) error
}

// RepositoryManager 仓储管理器接口
type RepositoryManager interface {
	GetProxy() ProxyRepository
	GetProxyTag() ProxyTagRepository
	GetUser() UserRepository
	GetHealthCheck() HealthCheckRepository
	GetTask() TaskRepository
	GetAlert() AlertRepository
	GetConfig() SystemConfigRepository
	GetRBAC() RBACRepository
	GetAPIKey() APIKeyRepository
	GetUserSettings() UserSettingsRepository
	Close() error
}
