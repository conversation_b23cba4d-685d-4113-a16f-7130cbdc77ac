package middleware

import (
	"bytes"
	"context"
	"encoding/json"
	"io"
	"net/http"
	"strings"
	"time"

	"proxyManager/internal/auth"
	"proxyManager/internal/models"
	"proxyManager/pkg/database"

	"github.com/gin-gonic/gin"
)

// AuthMiddleware 认证中间件
type AuthMiddleware struct {
	jwtManager  *auth.JWTManager
	superApiKey string
	db          *database.RedisClient
}

// NewAuthMiddleware 创建认证中间件
func NewAuthMiddleware(jwtManager *auth.JWTManager, superApiKey string, db *database.RedisClient) *AuthMiddleware {
	return &AuthMiddleware{
		jwtManager:  jwtManager,
		superApiKey: superApiKey,
		db:          db,
	}
}

// Authenticate JWT认证中间件
func (m *AuthMiddleware) Authenticate() gin.HandlerFunc {
	return func(c *gin.Context) {
		authHeader := c.<PERSON>eader("Authorization")
		apiKeyHeader := c.<PERSON>eader("X-API-Key")
		var apiKey string
		// 优先从header中获取API key
		if apiKeyHeader != "" {
			apiKey = apiKeyHeader
		} else if strings.HasPrefix(authHeader, "ApiKey ") {
			apiKey = strings.TrimPrefix(authHeader, "ApiKey ")
		}
		// 超级ApiKey（从body中读取，仅限POST/PUT/PATCH请求）
		if apiKey == "" && (c.Request.Method == "POST" || c.Request.Method == "PUT" || c.Request.Method == "PATCH") {
			// 读取body内容
			bodyBytes, err := io.ReadAll(c.Request.Body)
			if err == nil && len(bodyBytes) > 0 {
				// 重新设置body供后续handler使用
				c.Request.Body = io.NopCloser(bytes.NewBuffer(bodyBytes))

				// 尝试解析JSON以获取API key
				var bodyMap map[string]interface{}
				if json.Unmarshal(bodyBytes, &bodyMap) == nil {
					if v, ok := bodyMap["super_api_key"]; ok {
						if s, ok := v.(string); ok {
							apiKey = s
						}
					}
					if apiKey == "" {
						if v, ok := bodyMap["api_key"]; ok {
							if s, ok := v.(string); ok {
								apiKey = s
							}
						}
					}
				}
			}
		}
		if apiKey != "" && m.superApiKey != "" && apiKey == m.superApiKey {
			c.Set("user_id", "super_admin")
			c.Set("username", "super_admin")
			c.Set("role", models.UserRoleSuperAdmin)
			c.Set("user", nil)
			c.Next()
			return
		}

		// 检查是否是普通用户的 API Key
		if apiKey != "" {
			user, err := m.getUserByAPIKey(c.Request.Context(), apiKey)
			if err == nil && user != nil {
				// 更新 API Key 使用统计
				go m.updateAPIKeyUsage(user.ID)

				c.Set("user_id", user.ID)
				c.Set("username", user.Username)
				c.Set("role", user.Role)
				c.Set("user", user)
				c.Next()
				return
			}
		}

		// 如果提供了API key但不匹配，返回未授权错误
		if apiKey != "" {
			c.JSON(http.StatusUnauthorized, gin.H{"error": "Invalid API key"})
			c.Abort()
			return
		}

		if authHeader == "" {
			c.JSON(http.StatusUnauthorized, gin.H{"error": "Authorization header required"})
			c.Abort()
			return
		}

		// 检查Bearer前缀
		parts := strings.Split(authHeader, " ")
		if len(parts) != 2 || parts[0] != "Bearer" {
			c.JSON(http.StatusUnauthorized, gin.H{"error": "Invalid authorization header format"})
			c.Abort()
			return
		}

		token := parts[1]
		claims, err := m.jwtManager.ValidateToken(token)
		if err != nil {
			c.JSON(http.StatusUnauthorized, gin.H{"error": "Invalid token"})
			c.Abort()
			return
		}

		// 将用户信息存储到上下文中
		c.Set("user_id", claims.UserID)
		c.Set("username", claims.Username)
		c.Set("role", claims.Role)
		c.Next()
	}
}

// RequireRole 角色验证中间件
func (m *AuthMiddleware) RequireRole(roles ...models.UserRole) gin.HandlerFunc {
	return func(c *gin.Context) {
		userRole, exists := c.Get("role")
		if !exists {
			c.JSON(http.StatusUnauthorized, gin.H{"error": "User role not found"})
			c.Abort()
			return
		}

		role, ok := userRole.(models.UserRole)
		if !ok {
			c.JSON(http.StatusInternalServerError, gin.H{"error": "Invalid role type"})
			c.Abort()
			return
		}

		// 检查用户角色是否在允许的角色列表中
		// super_admin 拥有所有权限
		allowed := role == models.UserRoleSuperAdmin
		if !allowed {
			for _, allowedRole := range roles {
				if role == allowedRole {
					allowed = true
					break
				}
			}
		}

		if !allowed {
			c.JSON(http.StatusForbidden, gin.H{"error": "Insufficient permissions"})
			c.Abort()
			return
		}

		c.Next()
	}
}

// OptionalAuth 可选认证中间件
func (m *AuthMiddleware) OptionalAuth() gin.HandlerFunc {
	return func(c *gin.Context) {
		authHeader := c.GetHeader("Authorization")
		if authHeader == "" {
			c.Next()
			return
		}

		parts := strings.Split(authHeader, " ")
		if len(parts) != 2 || parts[0] != "Bearer" {
			c.Next()
			return
		}

		token := parts[1]
		claims, err := m.jwtManager.ValidateToken(token)
		if err != nil {
			c.Next()
			return
		}

		// 将用户信息存储到上下文中
		c.Set("user_id", claims.UserID)
		c.Set("username", claims.Username)
		c.Set("role", claims.Role)
		c.Next()
	}
}

// getUserByAPIKey 通过 API Key 获取用户 - 优化版本
func (m *AuthMiddleware) getUserByAPIKey(ctx context.Context, apiKey string) (*models.User, error) {
	if apiKey == "" {
		return nil, nil
	}

	// 首先尝试从API Key索引中获取用户ID
	userID, err := m.getUserIDByAPIKey(ctx, apiKey)
	if err != nil {
		// 如果索引查找失败，回退到遍历方式（向后兼容）
		return m.getUserByAPIKeyFallback(ctx, apiKey)
	}

	if userID == "" {
		return nil, nil
	}

	// 通过用户ID获取用户信息
	var user models.User
	userKey := "user:" + userID
	if err := m.db.Get(ctx, userKey, &user); err != nil {
		// 如果用户不存在，清理无效的API Key索引
		m.cleanupAPIKeyIndex(ctx, apiKey)
		return nil, nil
	}

	// 验证API Key是否仍然有效
	if user.APIKey != apiKey {
		// API Key已更改，清理旧索引
		m.cleanupAPIKeyIndex(ctx, apiKey)
		return nil, nil
	}

	return &user, nil
}

// getUserIDByAPIKey 通过API Key索引获取用户ID
func (m *AuthMiddleware) getUserIDByAPIKey(ctx context.Context, apiKey string) (string, error) {
	userID, err := m.db.HGet(ctx, "apikey_to_user", apiKey)
	if err != nil {
		return "", err
	}
	return userID, nil
}

// getUserByAPIKeyFallback 回退方法：遍历所有用户查找API Key
func (m *AuthMiddleware) getUserByAPIKeyFallback(ctx context.Context, apiKey string) (*models.User, error) {
	// 获取所有用户键
	keys, err := m.db.Keys(ctx, "user:*")
	if err != nil {
		return nil, err
	}

	for _, key := range keys {
		// 跳过索引键
		if strings.Contains(key, "username:") || strings.Contains(key, "email:") || strings.Contains(key, "apikey_") {
			continue
		}

		var user models.User
		if err := m.db.Get(ctx, key, &user); err != nil {
			continue
		}

		if user.APIKey == apiKey && user.APIKey != "" {
			// 找到用户后，建立索引以优化后续查找
			m.createAPIKeyIndex(ctx, apiKey, user.ID)
			return &user, nil
		}
	}

	return nil, nil
}

// createAPIKeyIndex 创建API Key索引
func (m *AuthMiddleware) createAPIKeyIndex(ctx context.Context, apiKey, userID string) {
	if apiKey == "" || userID == "" {
		return
	}

	// 异步创建索引，不阻塞主流程
	go func() {
		indexCtx := context.Background()
		if err := m.db.HSet(indexCtx, "apikey_to_user", apiKey, userID); err != nil {
			// 记录错误但不影响主流程
		}
	}()
}

// cleanupAPIKeyIndex 清理无效的API Key索引
func (m *AuthMiddleware) cleanupAPIKeyIndex(ctx context.Context, apiKey string) {
	if apiKey == "" {
		return
	}

	// 异步清理索引
	go func() {
		cleanupCtx := context.Background()
		m.db.HDel(cleanupCtx, "apikey_to_user", apiKey)
	}()
}

// updateAPIKeyUsage 更新 API Key 使用统计
func (m *AuthMiddleware) updateAPIKeyUsage(userID string) {
	ctx := context.Background()
	key := "user:" + userID

	var user models.User
	if err := m.db.Get(ctx, key, &user); err != nil {
		return
	}

	// 更新使用统计
	user.APIKeyLastUsed = time.Now()
	user.APIKeyUsageCount++
	user.UpdatedAt = time.Now()

	// 保存更新
	m.db.Set(ctx, key, &user, 0)
}
