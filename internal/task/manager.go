package task

import (
	"context"
	"crypto/tls"
	"encoding/json"
	"fmt"
	"io"
	"net/http"
	"net/url"
	"strconv"
	"sync"
	"time"

	"proxyManager/internal/models"
	"proxyManager/internal/proxy"
	"proxyManager/pkg/database"

	"github.com/sirupsen/logrus"
)

// Manager 任务管理器
type Manager struct {
	db           *database.RedisClient
	proxyManager *proxy.Manager
	logger       *logrus.Logger
	config       *Config
	workerPool   chan struct{}
	mu           sync.RWMutex
}

// Config 任务管理器配置
type Config struct {
	MaxConcurrent int
	QueueSize     int
	RetryAttempts int
	RetryDelay    time.Duration
}

// NewManager 创建任务管理器
func NewManager(db *database.RedisClient, proxyManager *proxy.Manager, config *Config, logger *logrus.Logger) *Manager {
	return &Manager{
		db:           db,
		proxyManager: proxyManager,
		logger:       logger,
		config:       config,
		workerPool:   make(chan struct{}, config.MaxConcurrent),
	}
}

// CreateTask 创建任务
func (m *Manager) CreateTask(ctx context.Context, req *models.TaskRequest, userID string) (*models.Task, error) {
	// 创建任务配置
	config := CreateTaskConfigFromRequest(req)
	if config.MaxRetries == 0 {
		config.MaxRetries = m.config.RetryAttempts
	}

	configJSON, err := json.Marshal(config)
	if err != nil {
		return nil, fmt.Errorf("marshal task config error: %w", err)
	}

	now := time.Now()
	task := &models.Task{
		ID:          generateID(),
		UserID:      userID,
		Name:        req.Name,
		Description: "", // TaskRequest 没有 Description 字段
		Type:        "http_request",
		Status:      "pending",
		Config:      string(configJSON),
		Progress:    0,
		CreatedAt:   now,
		UpdatedAt:   now,
	}

	// 存储任务
	key := fmt.Sprintf("task:%s", task.ID)
	if err := m.db.Set(ctx, key, task, 0); err != nil {
		return nil, fmt.Errorf("save task error: %w", err)
	}

	// 添加到任务队列
	queueKey := fmt.Sprintf("task_queue:%d", config.Priority)
	if err := m.db.LPush(ctx, queueKey, task.ID); err != nil {
		return nil, fmt.Errorf("add to task queue error: %w", err)
	}

	m.logger.WithFields(logrus.Fields{
		"task_id": task.ID,
		"name":    task.Name,
		"url":     config.URL,
		"user_id": task.UserID,
	}).Info("Task created")

	return task, nil
}

// GetTask 获取任务
func (m *Manager) GetTask(ctx context.Context, taskID string) (*models.Task, error) {
	key := fmt.Sprintf("task:%s", taskID)
	var task models.Task
	if err := m.db.Get(ctx, key, &task); err != nil {
		return nil, err
	}
	return &task, nil
}

// GetUserTasks 获取用户任务
func (m *Manager) GetUserTasks(ctx context.Context, userID string) ([]*models.Task, error) {
	// 获取所有任务键
	keys, err := m.db.Keys(ctx, "task:*")
	if err != nil {
		return nil, err
	}

	var tasks []*models.Task
	for _, key := range keys {
		var task models.Task
		if err := m.db.Get(ctx, key, &task); err != nil {
			m.logger.WithError(err).WithField("key", key).Error("Failed to get task")
			continue
		}
		if task.UserID == userID {
			tasks = append(tasks, &task)
		}
	}

	return tasks, nil
}

// UpdateTask 更新任务
func (m *Manager) UpdateTask(ctx context.Context, taskID string, req *models.TaskRequest, userID string) (*models.Task, error) {
	task, err := m.GetTask(ctx, taskID)
	if err != nil {
		return nil, err
	}

	if task.UserID != userID {
		return nil, fmt.Errorf("unauthorized to update this task")
	}

	if task.Status == "running" {
		return nil, fmt.Errorf("cannot update running task")
	}

	// 更新任务配置
	config := CreateTaskConfigFromRequest(req)
	if config.MaxRetries == 0 {
		config.MaxRetries = m.config.RetryAttempts
	}

	configJSON, err := json.Marshal(config)
	if err != nil {
		return nil, fmt.Errorf("marshal task config error: %w", err)
	}

	// 更新任务字段
	task.Name = req.Name
	task.Config = string(configJSON)
	task.UpdatedAt = time.Now()

	// 如果任务状态是失败或取消，重置为待处理
	if task.Status == "failed" || task.Status == "cancelled" {
		task.Status = "pending"
		task.Progress = 0
		task.ErrorMessage = ""
		task.Result = ""
		task.StartedAt = nil
		task.CompletedAt = nil
	}

	// 保存更新的任务
	key := fmt.Sprintf("task:%s", taskID)
	if err := m.db.Set(ctx, key, task, 0); err != nil {
		return nil, fmt.Errorf("save updated task error: %w", err)
	}

	m.logger.WithFields(logrus.Fields{
		"task_id": task.ID,
		"name":    task.Name,
		"user_id": task.UserID,
	}).Info("Task updated")

	return task, nil
}

// DeleteTask 删除任务
func (m *Manager) DeleteTask(ctx context.Context, taskID string, userID string) error {
	task, err := m.GetTask(ctx, taskID)
	if err != nil {
		return err
	}

	if task.UserID != userID {
		return fmt.Errorf("unauthorized to delete this task")
	}

	if task.Status == "running" {
		return fmt.Errorf("cannot delete running task")
	}

	// 从Redis中删除任务
	key := fmt.Sprintf("task:%s", taskID)
	if err := m.db.Del(ctx, key); err != nil {
		return fmt.Errorf("delete task error: %w", err)
	}

	m.logger.WithFields(logrus.Fields{
		"task_id": task.ID,
		"name":    task.Name,
		"user_id": task.UserID,
	}).Info("Task deleted")

	return nil
}

// CancelTask 取消任务
func (m *Manager) CancelTask(ctx context.Context, taskID string, userID string) error {
	task, err := m.GetTask(ctx, taskID)
	if err != nil {
		return err
	}

	if task.UserID != userID {
		return fmt.Errorf("unauthorized to cancel this task")
	}

	if task.Status == "completed" || task.Status == "failed" {
		return fmt.Errorf("cannot cancel completed or failed task")
	}

	task.Status = "cancelled"
	task.UpdatedAt = time.Now()

	key := fmt.Sprintf("task:%s", taskID)
	return m.db.Set(ctx, key, task, 0)
}

// BatchDeleteTasks 批量删除任务
func (m *Manager) BatchDeleteTasks(ctx context.Context, taskIDs []string, userID string) error {
	for _, taskID := range taskIDs {
		if err := m.DeleteTask(ctx, taskID, userID); err != nil {
			m.logger.WithError(err).WithField("task_id", taskID).Error("Failed to delete task in batch")
			// 继续删除其他任务，不因为一个失败而停止
		}
	}
	return nil
}

// BatchCancelTasks 批量取消任务
func (m *Manager) BatchCancelTasks(ctx context.Context, taskIDs []string, userID string) error {
	for _, taskID := range taskIDs {
		if err := m.CancelTask(ctx, taskID, userID); err != nil {
			m.logger.WithError(err).WithField("task_id", taskID).Error("Failed to cancel task in batch")
			// 继续取消其他任务，不因为一个失败而停止
		}
	}
	return nil
}

// StartWorker 启动工作器
func (m *Manager) StartWorker(ctx context.Context) {
	for {
		select {
		case <-ctx.Done():
			return
		default:
			m.processNextTask(ctx)
		}
	}
}

// 私有方法

func (m *Manager) processNextTask(ctx context.Context) {
	// 按优先级处理任务 (1=urgent, 2=high, 3=normal, 4=low)
	for priority := 1; priority <= 4; priority++ {
		queueKey := fmt.Sprintf("task_queue:%d", priority)
		taskID, err := m.db.RPop(ctx, queueKey)
		if err != nil {
			continue
		}

		if taskID != "" {
			m.workerPool <- struct{}{} // 获取工作槽
			go func(id string) {
				defer func() { <-m.workerPool }() // 释放工作槽
				m.executeTask(ctx, id)
			}(taskID)
			return
		}
	}

	// 如果没有任务，等待一段时间
	time.Sleep(1 * time.Second)
}

func (m *Manager) executeTask(ctx context.Context, taskID string) {
	task, err := m.GetTask(ctx, taskID)
	if err != nil {
		m.logger.WithError(err).WithField("task_id", taskID).Error("Failed to get task")
		return
	}

	// 检查任务状态
	if task.Status == "cancelled" {
		return
	}

	// 解析任务配置
	var config TaskConfig
	if err := json.Unmarshal([]byte(task.Config), &config); err != nil {
		m.handleTaskError(ctx, task, fmt.Errorf("failed to parse task config: %w", err))
		return
	}

	// 更新任务状态为运行中
	task.Status = "running"
	task.Progress = 25
	now := time.Now()
	task.StartedAt = &now
	task.UpdatedAt = now
	m.updateTask(ctx, task)

	// 获取代理
	proxyObj, err := m.proxyManager.GetProxy(ctx, proxy.Strategy(config.ProxyStrategy))
	if err != nil {
		m.handleTaskError(ctx, task, fmt.Errorf("failed to get proxy: %w", err))
		return
	}

	task.Progress = 50
	task.UpdatedAt = time.Now()
	m.updateTask(ctx, task)

	// 执行HTTP请求
	result, err := m.executeHTTPRequest(ctx, &config, proxyObj)
	if err != nil {
		m.handleTaskError(ctx, task, err)
		return
	}

	// 更新任务为完成
	task.Status = "completed"
	task.Progress = 100
	now = time.Now()
	task.CompletedAt = &now
	task.UpdatedAt = now

	// 保存结果
	resultJSON, _ := json.Marshal(result)
	task.Result = string(resultJSON)
	m.updateTask(ctx, task)

	m.logger.WithFields(logrus.Fields{
		"task_id":     task.ID,
		"status":      task.Status,
		"proxy_id":    proxyObj.ID,
		"duration":    result.Duration,
		"status_code": result.StatusCode,
	}).Info("Task completed")
}

func (m *Manager) executeHTTPRequest(ctx context.Context, config *TaskConfig, proxy *models.Proxy) (*TaskResult, error) {
	start := time.Now()

	// 创建代理URL
	proxyURL := fmt.Sprintf("%s://%s:%d", proxy.Type, proxy.Host, proxy.Port)
	proxyURLParsed, err := url.Parse(proxyURL)
	if err != nil {
		return nil, err
	}

	if proxy.Username != "" && proxy.Password != "" {
		proxyURLParsed.User = url.UserPassword(proxy.Username, proxy.Password)
	}

	// 创建HTTP客户端
	client := &http.Client{
		Timeout: time.Duration(config.Timeout) * time.Second,
		Transport: &http.Transport{
			Proxy: http.ProxyURL(proxyURLParsed),
			TLSClientConfig: &tls.Config{
				InsecureSkipVerify: true,
			},
		},
	}

	// 创建请求
	req, err := http.NewRequestWithContext(ctx, config.Method, config.URL, nil)
	if err != nil {
		return nil, err
	}

	// 设置请求头
	for key, value := range config.Headers {
		req.Header.Set(key, value)
	}

	// 设置请求体
	if config.Body != "" {
		req.Body = io.NopCloser(io.Reader(io.NewSectionReader(nil, 0, 0)))
		// 这里需要根据实际需求处理请求体
	}

	// 执行请求
	resp, err := client.Do(req)
	if err != nil {
		return nil, err
	}
	defer resp.Body.Close()

	// 读取响应体
	body, err := io.ReadAll(resp.Body)
	if err != nil {
		return nil, err
	}

	// 构建响应头
	responseHeaders := make(map[string]string)
	for key, values := range resp.Header {
		if len(values) > 0 {
			responseHeaders[key] = values[0]
		}
	}

	duration := time.Since(start).Milliseconds()

	return &TaskResult{
		StatusCode:      resp.StatusCode,
		ResponseBody:    string(body),
		ResponseHeaders: responseHeaders,
		ProxyID:         proxy.ID,
		Duration:        duration,
	}, nil
}

func (m *Manager) handleTaskError(ctx context.Context, task *models.Task, err error) {
	// 解析任务配置以获取重试信息
	var config TaskConfig
	if parseErr := json.Unmarshal([]byte(task.Config), &config); parseErr != nil {
		m.logger.WithError(parseErr).Error("Failed to parse task config for error handling")
		config.MaxRetries = 3 // 默认值
	}

	// 从结果中获取当前重试次数
	var result TaskResult
	retryCount := 0
	if task.Result != "" {
		if parseErr := json.Unmarshal([]byte(task.Result), &result); parseErr == nil {
			// 这里可以从结果中获取重试次数，但目前结构中没有，所以使用默认逻辑
		}
	}
	retryCount++ // 增加重试次数

	task.ErrorMessage = err.Error()
	task.UpdatedAt = time.Now()

	if retryCount >= config.MaxRetries {
		task.Status = "failed"
		now := time.Now()
		task.CompletedAt = &now
		m.updateTask(ctx, task)

		m.logger.WithFields(logrus.Fields{
			"task_id": task.ID,
			"error":   err.Error(),
			"retries": retryCount,
		}).Error("Task failed after max retries")
	} else {
		// 重新加入队列进行重试
		queueKey := fmt.Sprintf("task_queue:%d", config.Priority)
		m.db.LPush(ctx, queueKey, task.ID)

		m.logger.WithFields(logrus.Fields{
			"task_id": task.ID,
			"error":   err.Error(),
			"retry":   retryCount,
		}).Warn("Task retry scheduled")

		// 延迟重试
		time.Sleep(m.config.RetryDelay)
	}
}

func (m *Manager) updateTask(ctx context.Context, task *models.Task) error {
	key := fmt.Sprintf("task:%s", task.ID)
	return m.db.Set(ctx, key, task, 0)
}

func generateID() string {
	return strconv.FormatInt(time.Now().UnixNano(), 10)
}
