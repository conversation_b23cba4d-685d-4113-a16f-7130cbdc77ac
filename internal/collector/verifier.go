package collector

import (
	"context"
	"encoding/json"
	"fmt"
	"net"
	"net/http"
	"net/url"
	"os"
	"path/filepath"
	"strings"
	"sync"
	"sync/atomic"
	"time"

	"proxyManager/internal/models"

	"github.com/sirupsen/logrus"
)

// VerificationResult 验证结果
type VerificationResult struct {
	Proxy          *CollectedProxy       `json:"proxy"`
	Success        bool                  `json:"success"`
	ResponseTime   int64                 `json:"response_time"` // 毫秒
	Error          string                `json:"error,omitempty"`
	RealIP         string                `json:"real_ip,omitempty"`
	AnonymityLevel models.AnonymityLevel `json:"anonymity_level,omitempty"`
}

// IPResponse httpbin.org/ip 响应结构
type IPResponse struct {
	Origin string `json:"origin"`
}

// VerificationProgress 验证进度记录
type VerificationProgress struct {
	TotalProxies     int               `json:"total_proxies"`
	ProcessedProxies int               `json:"processed_proxies"`
	ValidNumber      int               `json:"valid_number"`
	ValidProxies     []*CollectedProxy `json:"valid_proxies"`
	ProcessedHosts   map[string]bool   `json:"processed_hosts"` // 记录已处理的代理
	StartTime        time.Time         `json:"start_time"`
	LastUpdateTime   time.Time         `json:"last_update_time"`
	BatchSize        int               `json:"batch_size"`
	Version          string            `json:"version"`
}

// getVerificationProgressPath 获取验证进度文件路径
func (c *ProxyCollector) getVerificationProgressPath() string {
	progressDir := "freeProxy"
	if err := os.MkdirAll(progressDir, 0755); err != nil {
		c.logger.WithError(err).Warn("Failed to create progress directory, using current directory")
		progressDir = "."
	}
	return filepath.Join(progressDir, "verification_progress.json")
}

// loadVerificationProgress 加载验证进度
func (c *ProxyCollector) loadVerificationProgress() (*VerificationProgress, error) {
	progressFile := c.getVerificationProgressPath()

	// 检查文件是否存在
	if _, err := os.Stat(progressFile); os.IsNotExist(err) {
		return nil, nil // 进度文件不存在
	}

	// 读取进度文件
	data, err := os.ReadFile(progressFile)
	if err != nil {
		return nil, fmt.Errorf("failed to read progress file: %w", err)
	}

	// 解析进度数据
	var progress VerificationProgress
	if err := json.Unmarshal(data, &progress); err != nil {
		return nil, fmt.Errorf("failed to unmarshal progress data: %w", err)
	}

	return &progress, nil
}

// saveVerificationProgress 保存验证进度
func (c *ProxyCollector) saveVerificationProgress(progress *VerificationProgress) error {
	progressFile := c.getVerificationProgressPath()

	progress.LastUpdateTime = time.Now()
	progress.ValidNumber = len(progress.ValidProxies)

	// 序列化进度数据
	data, err := json.MarshalIndent(progress, "", "  ")
	if err != nil {
		return fmt.Errorf("failed to marshal progress data: %w", err)
	}

	// 写入进度文件
	if err := os.WriteFile(progressFile, data, 0644); err != nil {
		return fmt.Errorf("failed to write progress file: %w", err)
	}

	return nil
}

// clearVerificationProgress 清除验证进度文件
func (c *ProxyCollector) clearVerificationProgress() error {
	// progressFile := c.getVerificationProgressPath()
	// if err := os.Remove(progressFile); err != nil && !os.IsNotExist(err) {
	// 	return fmt.Errorf("failed to remove progress file: %w", err)
	// }
	return nil
}

// verifyProxies 验证代理列表（增量验证优化版）
func (c *ProxyCollector) verifyProxies(ctx context.Context, proxies []*CollectedProxy) ([]*CollectedProxy, error) {
	c.logger.WithField("total_proxies", len(proxies)).Info("Starting incremental proxy verification")

	// 初始化缓存（如果启用）
	var cache *SimpleProxyCache
	if c.config.CacheEnabled {
		cache = NewSimpleProxyCache(c.config.CacheFilePath, c.config.CacheValidDuration, c.logger)
		defer func() {
			if err := cache.Save(); err != nil {
				c.logger.WithError(err).Error("Failed to save proxy cache")
			}
		}()
	}

	// 尝试加载验证进度
	progress, err := c.loadVerificationProgress()
	if err != nil {
		c.logger.WithError(err).Warn("Failed to load verification progress, starting fresh")
		progress = nil
	}

	// 检查进度是否有效（代理总数是否匹配）
	if progress != nil && progress.TotalProxies != len(proxies) {
		c.logger.WithFields(logrus.Fields{
			"progress_total": progress.TotalProxies,
			"current_total":  len(proxies),
		}).Info("Proxy count changed, starting fresh verification")
		// progress = nil
		progress.TotalProxies = len(proxies)
		c.clearVerificationProgress() // 清除旧进度
	}

	// 初始化或恢复进度
	if progress == nil {
		progress = &VerificationProgress{
			TotalProxies:     len(proxies),
			ProcessedProxies: 0,
			ValidProxies:     make([]*CollectedProxy, 0),
			ProcessedHosts:   make(map[string]bool),
			StartTime:        time.Now(),
			BatchSize:        c.config.BatchSize,
			Version:          "1.0",
		}
		c.logger.Info("Starting fresh verification")
	} else {
		c.logger.WithFields(logrus.Fields{
			"processed":    progress.ProcessedProxies,
			"total":        progress.TotalProxies,
			"valid_count":  len(progress.ValidProxies),
			"progress":     fmt.Sprintf("%.1f%%", float64(progress.ProcessedProxies)/float64(progress.TotalProxies)*100),
			"elapsed_time": time.Since(progress.StartTime).String(),
		}).Info("Resuming verification from previous progress")
	}

	// 从进度中恢复已验证的代理
	validProxies := progress.ValidProxies

	// 预处理：分离需要验证的代理（跳过已处理的）
	var toVerify []*CollectedProxy
	cached := 0

	for _, proxy := range proxies {
		proxyKey := fmt.Sprintf("%s:%d", proxy.Host, proxy.Port)

		// 如果已经处理过，跳过
		if progress.ProcessedHosts[proxyKey] {
			continue
		}

		// 检查缓存
		if cache != nil && cache.IsValid(proxy.Host, proxy.Port) {
			proxy.Verified = true
			proxy.Status = models.ProxyStatusActive
			validProxies = append(validProxies, proxy)
			progress.ProcessedHosts[proxyKey] = true
			progress.ProcessedProxies++
			cached++
		} else {
			toVerify = append(toVerify, proxy)
		}
	}

	c.logger.WithFields(logrus.Fields{
		"total_proxies":      len(proxies),
		"already_processed":  len(progress.ProcessedHosts) - len(toVerify) - cached,
		"cached_proxies":     cached,
		"to_verify":          len(toVerify),
		"valid_so_far":       len(validProxies),
		"concurrent_workers": c.config.ConcurrentWorkers,
	}).Info("Cache check completed, starting incremental verification")

	if len(toVerify) == 0 {
		c.logger.Info("All proxies processed, clearing progress file")
		c.clearVerificationProgress()
		return validProxies, nil
	}

	// 分批验证代理
	batchSize := progress.BatchSize
	if batchSize <= 0 {
		batchSize = 1000 // 默认批大小
	}

	for i := 0; i < len(toVerify); i += batchSize {
		// 检查上下文是否被取消
		select {
		case <-ctx.Done():
			c.logger.Info("Verification cancelled, saving progress")
			if err := c.saveVerificationProgress(progress); err != nil {
				c.logger.WithError(err).Error("Failed to save progress on cancellation")
			}
			return validProxies, ctx.Err()
		default:
		}

		// 计算当前批次
		end := i + batchSize
		if end > len(toVerify) {
			end = len(toVerify)
		}
		batch := toVerify[i:end]

		c.logger.WithFields(logrus.Fields{
			"batch_start": i + 1,
			"batch_end":   end,
			"batch_size":  len(batch),
			"total":       len(toVerify),
		}).Info("Processing verification batch")

		// 验证当前批次
		verifiedBatch, err := c.verifyProxiesConcurrently(ctx, batch, cache, progress)
		if err != nil {
			c.logger.WithError(err).Error("Batch verification failed, saving progress")
			if saveErr := c.saveVerificationProgress(progress); saveErr != nil {
				c.logger.WithError(saveErr).Error("Failed to save progress after batch error")
			}
			return validProxies, err
		}

		// 更新进度
		for _, proxy := range batch {
			proxyKey := fmt.Sprintf("%s:%d", proxy.Host, proxy.Port)
			progress.ProcessedHosts[proxyKey] = true
			progress.ProcessedProxies++

			// 更新缓存
			if cache != nil {
				cache.Set(proxy.Host, proxy.Port, proxy.Verified)
			}
		}

		// 合并有效代理
		validProxies = append(validProxies, verifiedBatch...)
		progress.ValidProxies = validProxies

		// 保存进度
		if err := c.saveVerificationProgress(progress); err != nil {
			c.logger.WithError(err).Error("Failed to save verification progress")
		} else {
			c.logger.WithFields(logrus.Fields{
				"processed":   progress.ProcessedProxies,
				"total":       progress.TotalProxies,
				"valid_count": len(validProxies),
				"progress":    fmt.Sprintf("%.1f%%", float64(progress.ProcessedProxies)/float64(progress.TotalProxies)*100),
			}).Info("Batch verification completed, progress saved")
		}
	}

	// 验证完成，清除进度文件
	c.logger.Info("All verification completed, clearing progress file")
	if err := c.clearVerificationProgress(); err != nil {
		c.logger.WithError(err).Warn("Failed to clear progress file")
	}

	c.logger.WithFields(logrus.Fields{
		"total_proxies": len(proxies),
		"valid_proxies": len(validProxies),
		"success_rate":  fmt.Sprintf("%.2f%%", float64(len(validProxies))/float64(len(proxies))*100),
		"total_time":    time.Since(progress.StartTime).String(),
	}).Info("Incremental proxy verification completed")

	return validProxies, nil
}

// verifyProxiesConcurrently 高并发验证代理列表
func (c *ProxyCollector) verifyProxiesConcurrently(ctx context.Context, proxies []*CollectedProxy, cache *SimpleProxyCache, progressMeta *VerificationProgress) ([]*CollectedProxy, error) {
	if len(proxies) == 0 {
		return nil, nil
	}

	var validProxies []*CollectedProxy
	var mu sync.Mutex
	var wg sync.WaitGroup

	// 创建工作池，使用更大的并发数
	maxWorkers := c.config.ConcurrentWorkers
	if maxWorkers <= 0 {
		maxWorkers = 200 // 默认更高的并发数
	}

	// 动态调整工作池大小：对于大量代理，增加并发数
	if len(proxies) > 10000 {
		maxWorkers = maxWorkers * 2 // 双倍并发数
	}

	semaphore := make(chan struct{}, maxWorkers)

	// 进度跟踪
	processed := int64(0)
	total := int64(len(proxies))

	// 启动进度报告goroutine
	progressTicker := time.NewTicker(5 * time.Second)
	defer progressTicker.Stop()

	go func() {
		for {
			select {
			case <-ctx.Done():
				return
			case <-progressTicker.C:
				current := atomic.LoadInt64(&processed)
				progress := float64(progressMeta.ProcessedProxies) / float64(progressMeta.TotalProxies)
				c.logger.WithFields(logrus.Fields{
					"processed":    current,
					"total":        total,
					"progress":     fmt.Sprintf("%.2f%%", progress),
					"workers":      maxWorkers,
					"validProxies": len(progressMeta.ValidProxies),
				}).Info("verifyProxiesConcurrently progress")
			}
		}
	}()

	// 并发验证所有代理
	for _, proxy := range proxies {
		wg.Add(1)
		go func(p *CollectedProxy) {
			defer wg.Done()
			defer atomic.AddInt64(&processed, 1)

			// 获取工作池信号量
			semaphore <- struct{}{}
			defer func() { <-semaphore }()

			// 验证代理
			result := c.verifyProxy(ctx, p)

			if result.Success {
				// 更新代理信息
				p.Verified = true
				p.ResponseTime = &result.ResponseTime
				p.Status = models.ProxyStatusActive
				p.AnonymityLevel = result.AnonymityLevel
				p.QualityScore = c.calculateQualityScore(result.ResponseTime, result.AnonymityLevel, p.Type)

				mu.Lock()
				validProxies = append(validProxies, p)
				mu.Unlock()
			} else {
				p.Verified = false
				p.Status = models.ProxyStatusFailed
				p.Error = result.Error
				p.AnonymityLevel = models.AnonymityLevelUnknown
				p.QualityScore = 0.0
			}
		}(proxy)
	}

	// 等待所有验证完成
	wg.Wait()

	return validProxies, nil
}

// verifyProxy 验证单个代理
func (c *ProxyCollector) verifyProxy(ctx context.Context, proxy *CollectedProxy) *VerificationResult {
	logger := c.logger.WithFields(logrus.Fields{
		"host": proxy.Host,
		"port": proxy.Port,
		"type": proxy.Type,
	})

	startTime := time.Now()

	// 创建代理URL
	proxyURL, err := c.buildProxyURL(proxy)
	if err != nil {
		logger.WithError(err).Debug("Failed to build proxy URL")
		return &VerificationResult{
			Proxy:   proxy,
			Success: false,
			Error:   fmt.Sprintf("invalid proxy URL: %v", err),
		}
	}

	// 创建HTTP客户端
	client, err := c.createProxyClient(proxyURL)
	if err != nil {
		logger.WithError(err).Debug("Failed to create proxy client")
		return &VerificationResult{
			Proxy:   proxy,
			Success: false,
			Error:   fmt.Sprintf("failed to create client: %v", err),
		}
	}

	// 创建请求上下文
	verifyCtx, cancel := context.WithTimeout(ctx, c.config.VerificationTimeout)
	defer cancel()

	// 发送测试请求
	req, err := http.NewRequestWithContext(verifyCtx, "GET", c.config.TestURL, nil)
	if err != nil {
		logger.WithError(err).Debug("Failed to create test request")
		return &VerificationResult{
			Proxy:   proxy,
			Success: false,
			Error:   fmt.Sprintf("failed to create request: %v", err),
		}
	}

	// 设置请求头
	req.Header.Set("User-Agent", "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36")
	req.Header.Set("Accept", "application/json")

	// 发送请求
	resp, err := client.Do(req)
	if err != nil {
		logger.WithError(err).Debug("Proxy verification failed")
		return &VerificationResult{
			Proxy:   proxy,
			Success: false,
			Error:   fmt.Sprintf("request failed: %v", err),
		}
	}
	defer resp.Body.Close()

	responseTime := time.Since(startTime).Milliseconds()

	// 检查响应状态
	if resp.StatusCode != http.StatusOK {
		logger.WithField("status_code", resp.StatusCode).Debug("Non-200 response")
		return &VerificationResult{
			Proxy:        proxy,
			Success:      false,
			ResponseTime: responseTime,
			Error:        fmt.Sprintf("HTTP %d", resp.StatusCode),
		}
	}

	// 解析响应获取真实IP
	var ipResp IPResponse
	if err := json.NewDecoder(resp.Body).Decode(&ipResp); err != nil {
		logger.WithError(err).Debug("Failed to decode response")
		return &VerificationResult{
			Proxy:        proxy,
			Success:      false,
			ResponseTime: responseTime,
			Error:        fmt.Sprintf("failed to decode response: %v", err),
		}
	}

	// 检测匿名级别
	anonymityLevel := c.detectAnonymityLevel(proxy.Host, ipResp.Origin)

	logger.WithFields(logrus.Fields{
		"response_time":   responseTime,
		"real_ip":         ipResp.Origin,
		"anonymity_level": anonymityLevel,
	}).Debug("Proxy verification successful")

	return &VerificationResult{
		Proxy:          proxy,
		Success:        true,
		ResponseTime:   responseTime,
		RealIP:         ipResp.Origin,
		AnonymityLevel: anonymityLevel,
	}
}

// buildProxyURL 构建代理URL
func (c *ProxyCollector) buildProxyURL(proxy *CollectedProxy) (*url.URL, error) {
	var scheme string
	switch proxy.Type {
	case models.ProxyTypeHTTP:
		scheme = "http"
	case models.ProxyTypeHTTPS:
		scheme = "https"
	case models.ProxyTypeSOCKS5:
		scheme = "socks5"
	default:
		return nil, fmt.Errorf("unsupported proxy type: %s", proxy.Type)
	}

	proxyURL := &url.URL{
		Scheme: scheme,
		Host:   fmt.Sprintf("%s:%d", proxy.Host, proxy.Port),
	}

	// 添加认证信息
	if proxy.Username != "" && proxy.Password != "" {
		proxyURL.User = url.UserPassword(proxy.Username, proxy.Password)
	}

	return proxyURL, nil
}

// createProxyClient 创建代理HTTP客户端
func (c *ProxyCollector) createProxyClient(proxyURL *url.URL) (*http.Client, error) {
	transport := &http.Transport{
		Proxy: http.ProxyURL(proxyURL),
		DialContext: (&net.Dialer{
			Timeout:   c.config.VerificationTimeout,
			KeepAlive: 30 * time.Second,
		}).DialContext,
		TLSHandshakeTimeout:   10 * time.Second,
		ResponseHeaderTimeout: c.config.VerificationTimeout,
		ExpectContinueTimeout: 1 * time.Second,
		MaxIdleConns:          10,
		IdleConnTimeout:       30 * time.Second,
		DisableCompression:    true,
	}

	return &http.Client{
		Transport: transport,
		Timeout:   c.config.VerificationTimeout,
	}, nil
}

// detectAnonymityLevel 检测匿名级别
func (c *ProxyCollector) detectAnonymityLevel(proxyIP, realIP string) models.AnonymityLevel {
	// 如果真实IP包含代理IP，说明是透明代理
	if strings.Contains(realIP, proxyIP) {
		return models.AnonymityLevelTransparent
	}

	// 如果真实IP是单个IP且不是代理IP，说明是匿名代理
	ips := strings.Split(realIP, ",")
	if len(ips) == 1 {
		cleanIP := strings.TrimSpace(ips[0])
		if cleanIP != proxyIP && c.isValidIP(cleanIP) {
			return models.AnonymityLevelAnonymous
		}
	}

	// 如果有多个IP或格式复杂，可能是高匿代理
	if len(ips) > 1 {
		return models.AnonymityLevelElite
	}

	return models.AnonymityLevelUnknown
}

// calculateQualityScore 计算质量分数
func (c *ProxyCollector) calculateQualityScore(responseTime int64, anonymityLevel models.AnonymityLevel, proxyType models.ProxyType) float64 {
	var score float64 = 0.0

	// 响应时间评分 (40%)
	if responseTime <= 1000 { // 1秒以内
		score += 0.4
	} else if responseTime <= 3000 { // 3秒以内
		score += 0.3
	} else if responseTime <= 5000 { // 5秒以内
		score += 0.2
	} else {
		score += 0.1
	}

	// 匿名级别评分 (30%)
	switch anonymityLevel {
	case models.AnonymityLevelElite:
		score += 0.3
	case models.AnonymityLevelAnonymous:
		score += 0.25
	case models.AnonymityLevelTransparent:
		score += 0.15
	default:
		score += 0.1
	}

	// 代理类型评分 (20%)
	switch proxyType {
	case models.ProxyTypeHTTPS:
		score += 0.2
	case models.ProxyTypeHTTP:
		score += 0.15
	case models.ProxyTypeSOCKS5:
		score += 0.18
	}

	// 基础可用性评分 (10%) - 如果调用此方法说明代理可用
	score += 0.1

	// 确保分数在0-1之间
	if score > 1.0 {
		score = 1.0
	}
	if score < 0.0 {
		score = 0.0
	}

	return score
}
