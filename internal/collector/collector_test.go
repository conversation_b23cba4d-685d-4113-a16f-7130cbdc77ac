package collector

import (
	"context"
	"encoding/json"
	"net/http"
	"net/http/httptest"
	"os"
	"testing"
	"time"

	"proxyManager/internal/models"
	"proxyManager/internal/repository"
	"proxyManager/pkg/config"

	"github.com/stretchr/testify/assert"
	"github.com/stretchr/testify/mock"
)

// MockProxyRepository 模拟代理仓储
type MockProxyRepository struct {
	mock.Mock
}

func (m *MockProxyRepository) Create(ctx context.Context, proxy *models.Proxy) error {
	args := m.Called(ctx, proxy)
	return args.Error(0)
}

func (m *MockProxyRepository) GetByID(ctx context.Context, id string) (*models.Proxy, error) {
	args := m.Called(ctx, id)
	return args.Get(0).(*models.Proxy), args.Error(1)
}

func (m *MockProxyRepository) GetByHostPort(ctx context.Context, host string, port int) (*models.Proxy, error) {
	args := m.Called(ctx, host, port)
	if args.Get(0) == nil {
		return nil, args.Error(1)
	}
	return args.Get(0).(*models.Proxy), args.Error(1)
}

func (m *MockProxyRepository) Update(ctx context.Context, proxy *models.Proxy) error {
	args := m.Called(ctx, proxy)
	return args.Error(0)
}

func (m *MockProxyRepository) Delete(ctx context.Context, id string) error {
	args := m.Called(ctx, id)
	return args.Error(0)
}

// 实现其他必需的方法（简化版）
func (m *MockProxyRepository) List(ctx context.Context, filters repository.ProxyFilters) ([]*models.Proxy, error) {
	return nil, nil
}
func (m *MockProxyRepository) Count(ctx context.Context, filters repository.ProxyFilters) (int64, error) {
	return 0, nil
}
func (m *MockProxyRepository) CreateBatch(ctx context.Context, proxies []*models.Proxy) error {
	return nil
}
func (m *MockProxyRepository) UpdateBatch(ctx context.Context, proxies []*models.Proxy) error {
	return nil
}
func (m *MockProxyRepository) DeleteBatch(ctx context.Context, ids []string) error {
	return nil
}
func (m *MockProxyRepository) GetByStatus(ctx context.Context, status models.ProxyStatus) ([]*models.Proxy, error) {
	return nil, nil
}
func (m *MockProxyRepository) UpdateStatus(ctx context.Context, id string, status models.ProxyStatus) error {
	return nil
}
func (m *MockProxyRepository) UpdateLastCheck(ctx context.Context, id string, lastCheck time.Time, responseTime int64) error {
	return nil
}
func (m *MockProxyRepository) GetByType(ctx context.Context, proxyType models.ProxyType) ([]*models.Proxy, error) {
	return nil, nil
}
func (m *MockProxyRepository) GetByLocation(ctx context.Context, countryCode, cityName string) ([]*models.Proxy, error) {
	return nil, nil
}
func (m *MockProxyRepository) GetLocationStats(ctx context.Context) (*models.ProxyLocationStats, error) {
	return nil, nil
}
func (m *MockProxyRepository) GroupByLocation(ctx context.Context, groupBy string) ([]models.ProxyGroupByLocation, error) {
	return nil, nil
}
func (m *MockProxyRepository) UpdateQualityScore(ctx context.Context, id string, score models.QualityScore) error {
	return nil
}
func (m *MockProxyRepository) GetByQualityRange(ctx context.Context, minScore, maxScore float64) ([]*models.Proxy, error) {
	return nil, nil
}
func (m *MockProxyRepository) GetTopQualityProxies(ctx context.Context, limit int) ([]*models.Proxy, error) {
	return nil, nil
}
func (m *MockProxyRepository) GetByTags(ctx context.Context, tagNames []string) ([]*models.Proxy, error) {
	return nil, nil
}
func (m *MockProxyRepository) GetByScenario(ctx context.Context, scenario string) ([]*models.Proxy, error) {
	return nil, nil
}
func (m *MockProxyRepository) AssignTags(ctx context.Context, proxyID string, tagIDs []string, assignedBy string) error {
	return nil
}
func (m *MockProxyRepository) RemoveTags(ctx context.Context, proxyID string, tagIDs []string) error {
	return nil
}
func (m *MockProxyRepository) GetProxyTags(ctx context.Context, proxyID string) ([]*models.ProxyTag, error) {
	return nil, nil
}

// MockRedisClient 模拟Redis客户端
type MockRedisClient struct {
	mock.Mock
}

func (m *MockRedisClient) Ping(ctx context.Context) error {
	args := m.Called(ctx)
	return args.Error(0)
}

func (m *MockRedisClient) Set(ctx context.Context, key string, value interface{}, expiration time.Duration) error {
	args := m.Called(ctx, key, value, expiration)
	return args.Error(0)
}

func (m *MockRedisClient) Get(ctx context.Context, key string) (string, error) {
	args := m.Called(ctx, key)
	return args.String(0), args.Error(1)
}

func (m *MockRedisClient) Close() error {
	args := m.Called()
	return args.Error(0)
}

// 其他必需的方法
func (m *MockRedisClient) Del(ctx context.Context, keys ...string) error             { return nil }
func (m *MockRedisClient) Exists(ctx context.Context, keys ...string) (int64, error) { return 0, nil }
func (m *MockRedisClient) Expire(ctx context.Context, key string, expiration time.Duration) error {
	return nil
}
func (m *MockRedisClient) ZAdd(ctx context.Context, key string, score float64, member string) error {
	return nil
}
func (m *MockRedisClient) ZRange(ctx context.Context, key string, start, stop int64) ([]string, error) {
	return nil, nil
}

// TestCollectorConfig 测试采集器配置
func TestCollectorConfig(t *testing.T) {
	config := &config.CollectorConfig{
		Enabled:             true,
		CollectInterval:     60 * time.Minute,
		ConcurrentWorkers:   10,
		RequestTimeout:      30 * time.Second,
		VerificationTimeout: 10 * time.Second,
		TestURL:             "http://httpbin.org/ip",
		MaxRetries:          3,
		RetryDelay:          5 * time.Second,
		SaveRawData:         true,
		RawDataPath:         "test_data/raw_proxies",
		MinQualityScore:     0.3,
		EnableGeoLocation:   true,
	}

	assert.True(t, config.Enabled)
	assert.Equal(t, 60*time.Minute, config.CollectInterval)
	assert.Equal(t, 10, config.ConcurrentWorkers)
	assert.Equal(t, 30*time.Second, config.RequestTimeout)
	assert.Equal(t, 10*time.Second, config.VerificationTimeout)
	assert.Equal(t, "http://httpbin.org/ip", config.TestURL)
	assert.Equal(t, 3, config.MaxRetries)
	assert.Equal(t, 5*time.Second, config.RetryDelay)
	assert.True(t, config.SaveRawData)
	assert.Equal(t, "test_data/raw_proxies", config.RawDataPath)
	assert.Equal(t, 0.3, config.MinQualityScore)
	assert.True(t, config.EnableGeoLocation)
}

// TestValidateConfig 测试配置验证
func TestValidateConfig(t *testing.T) {
	tests := []struct {
		name    string
		config  *config.CollectorConfig
		wantErr bool
	}{
		{
			name: "valid config",
			config: &config.CollectorConfig{
				CollectInterval:     60 * time.Minute,
				ConcurrentWorkers:   10,
				RequestTimeout:      30 * time.Second,
				VerificationTimeout: 10 * time.Second,
				MaxRetries:          3,
				MinQualityScore:     0.5,
				TestURL:             "http://httpbin.org/ip",
				SourcesFile:         "sources.json",
			},
			wantErr: false,
		},
		{
			name: "invalid collect interval",
			config: &config.CollectorConfig{
				CollectInterval:     0,
				ConcurrentWorkers:   10,
				RequestTimeout:      30 * time.Second,
				VerificationTimeout: 10 * time.Second,
				MaxRetries:          3,
				MinQualityScore:     0.5,
				TestURL:             "http://httpbin.org/ip",
				SourcesFile:         "sources.json",
			},
			wantErr: true,
		},
		{
			name: "invalid concurrent workers",
			config: &config.CollectorConfig{
				CollectInterval:     60 * time.Minute,
				ConcurrentWorkers:   0,
				RequestTimeout:      30 * time.Second,
				VerificationTimeout: 10 * time.Second,
				MaxRetries:          3,
				MinQualityScore:     0.5,
				TestURL:             "http://httpbin.org/ip",
				SourcesFile:         "sources.json",
			},
			wantErr: true,
		},
		{
			name: "invalid quality score",
			config: &config.CollectorConfig{
				CollectInterval:     60 * time.Minute,
				ConcurrentWorkers:   10,
				RequestTimeout:      30 * time.Second,
				VerificationTimeout: 10 * time.Second,
				MaxRetries:          3,
				MinQualityScore:     1.5, // 超出范围
				TestURL:             "http://httpbin.org/ip",
				SourcesFile:         "sources.json",
			},
			wantErr: true,
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			err := ValidateConfig(tt.config)
			if tt.wantErr {
				assert.Error(t, err)
			} else {
				assert.NoError(t, err)
			}
		})
	}
}

// TestParseProxyData 测试代理数据解析
func TestParseProxyData(t *testing.T) {
	testData := `***********:8080
***********:3128
# 这是注释
***********:1080
invalid-line
***********:8888`

	// 创建临时HTTP服务器
	server := httptest.NewServer(http.HandlerFunc(func(w http.ResponseWriter, r *http.Request) {
		w.WriteHeader(http.StatusOK)
		w.Write([]byte(testData))
	}))
	defer server.Close()

	// 测试HTTP客户端请求
	client := &http.Client{Timeout: 10 * time.Second}
	resp, err := client.Get(server.URL)
	assert.NoError(t, err)
	defer resp.Body.Close()

	assert.Equal(t, http.StatusOK, resp.StatusCode)
}

// TestIsValidIP 测试IP地址验证
func TestIsValidIP(t *testing.T) {
	collector := &ProxyCollector{}

	tests := []struct {
		ip    string
		valid bool
	}{
		{"***********", true},
		{"********", true},
		{"***************", true},
		{"0.0.0.0", true},
		{"***********56", false},   // 超出范围
		{"192.168.1", false},       // 格式错误
		{"***********.1", false},   // 格式错误
		{"abc.def.ghi.jkl", false}, // 非数字
		{"", false},                // 空字符串
	}

	for _, tt := range tests {
		t.Run(tt.ip, func(t *testing.T) {
			result := collector.isValidIP(tt.ip)
			assert.Equal(t, tt.valid, result)
		})
	}
}

// 创建测试用的源文件
func createTestSourcesFile(t *testing.T) string {
	sources := ProxySource{
		HTTP:   []string{"http://example.com/http.txt"},
		HTTPS:  []string{"http://example.com/https.txt"},
		SOCKS5: []string{"http://example.com/socks5.txt"},
	}

	file, err := os.CreateTemp("", "sources_*.json")
	assert.NoError(t, err)

	encoder := json.NewEncoder(file)
	err = encoder.Encode(sources)
	assert.NoError(t, err)

	file.Close()
	return file.Name()
}

// TestProxySourcesFormat 测试代理源格式
func TestProxySourcesFormat(t *testing.T) {
	// 创建测试源文件
	sourcesFile := createTestSourcesFile(t)
	defer os.Remove(sourcesFile)

	// 直接读取和解析文件
	file, err := os.Open(sourcesFile)
	assert.NoError(t, err)
	defer file.Close()

	var sources ProxySource
	decoder := json.NewDecoder(file)
	err = decoder.Decode(&sources)
	assert.NoError(t, err)

	assert.Len(t, sources.HTTP, 1)
	assert.Len(t, sources.HTTPS, 1)
	assert.Len(t, sources.SOCKS5, 1)
	assert.Equal(t, "http://example.com/http.txt", sources.HTTP[0])
}
