package collector

import (
	"encoding/json"
	"fmt"
	"os"
	"sync"
	"time"

	"github.com/sirupsen/logrus"
)

// SimpleCacheEntry 简化的代理缓存条目
type SimpleCacheEntry struct {
	Host         string    `json:"host"`
	Port         int       `json:"port"`
	LastVerified time.Time `json:"last_verified"`
	IsValid      bool      `json:"is_valid"`
}

// SimpleProxyCache 简化的代理缓存
type SimpleProxyCache struct {
	filePath      string
	validDuration time.Duration
	entries       map[string]*SimpleCacheEntry
	mutex         sync.RWMutex
	logger        *logrus.Logger
}

// NewSimpleProxyCache 创建简化的代理缓存
func NewSimpleProxyCache(filePath string, validDuration time.Duration, logger *logrus.Logger) *SimpleProxyCache {
	cache := &SimpleProxyCache{
		filePath:      filePath,
		validDuration: validDuration,
		entries:       make(map[string]*SimpleCacheEntry),
		logger:        logger,
	}
	cache.Load()
	return cache
}

// Load 从文件加载缓存
func (c *SimpleProxyCache) Load() {
	c.mutex.Lock()
	defer c.mutex.Unlock()

	data, err := os.ReadFile(c.filePath)
	if err != nil {
		return // 文件不存在或读取失败，使用空缓存
	}

	if err := json.Unmarshal(data, &c.entries); err != nil {
		c.logger.WithError(err).Warn("Failed to unmarshal cache data")
	}
}

// Save 保存缓存到文件
func (c *SimpleProxyCache) Save() error {
	c.mutex.RLock()
	defer c.mutex.RUnlock()

	data, err := json.MarshalIndent(c.entries, "", "  ")
	if err != nil {
		return err
	}

	return os.WriteFile(c.filePath, data, 0644)
}

// IsValid 检查代理是否在缓存中且有效
func (c *SimpleProxyCache) IsValid(host string, port int) bool {
	c.mutex.RLock()
	defer c.mutex.RUnlock()

	key := fmt.Sprintf("%s:%d", host, port)
	entry, exists := c.entries[key]
	if !exists {
		return false
	}

	// 检查是否过期
	if time.Since(entry.LastVerified) > c.validDuration {
		return false
	}

	return entry.IsValid
}

// Set 设置缓存条目
func (c *SimpleProxyCache) Set(host string, port int, isValid bool) {
	c.mutex.Lock()
	defer c.mutex.Unlock()

	key := fmt.Sprintf("%s:%d", host, port)
	c.entries[key] = &SimpleCacheEntry{
		Host:         host,
		Port:         port,
		LastVerified: time.Now(),
		IsValid:      isValid,
	}
}
