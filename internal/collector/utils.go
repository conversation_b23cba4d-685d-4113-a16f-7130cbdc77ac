package collector

import (
	"context"
	"fmt"
	"net"
	"net/http"
	"proxyManager/pkg/config"
	"time"

	"github.com/sirupsen/logrus"
)

// HealthCheck 健康检查
func (c *ProxyCollector) HealthCheck() error {
	// 检查配置
	if c.config == nil {
		return fmt.Errorf("collector config is nil")
	}

	// 检查数据库连接
	if c.proxyRepo == nil {
		return fmt.Errorf("proxy repository is nil")
	}

	// 检查Redis连接
	if c.redisClient == nil {
		return fmt.Errorf("redis client is nil")
	}

	// 测试Redis连接
	ctx, cancel := context.WithTimeout(context.Background(), 5*time.Second)
	defer cancel()

	if err := c.redisClient.Ping(ctx); err != nil {
		return fmt.Errorf("redis ping failed: %w", err)
	}

	// 检查源文件是否存在
	if err := c.checkSourcesFile(); err != nil {
		return fmt.Errorf("sources file check failed: %w", err)
	}

	return nil
}

// checkSourcesFile 检查源文件
func (c *ProxyCollector) checkSourcesFile() error {
	sources, err := c.loadProxySources()
	if err != nil {
		return err
	}

	totalSources := c.getTotalSourceCount(sources)
	if totalSources == 0 {
		return fmt.Errorf("no proxy sources configured")
	}

	c.logger.WithField("total_sources", totalSources).Debug("Sources file validation passed")
	return nil
}

// TestConnection 测试网络连接
func (c *ProxyCollector) TestConnection() error {
	// 测试到测试URL的连接
	client := &http.Client{
		Timeout: 10 * time.Second,
	}

	resp, err := client.Get(c.config.TestURL)
	if err != nil {
		return fmt.Errorf("failed to connect to test URL: %w", err)
	}
	defer resp.Body.Close()

	if resp.StatusCode != http.StatusOK {
		return fmt.Errorf("test URL returned status %d", resp.StatusCode)
	}

	return nil
}

// GetMetrics 获取采集器指标
func (c *ProxyCollector) GetMetrics() map[string]interface{} {
	stats := c.GetStats()

	return map[string]interface{}{
		"is_running":          c.IsRunning(),
		"last_collection":     stats.LastCollection,
		"total_sources":       stats.TotalSources,
		"success_sources":     stats.SuccessSources,
		"total_proxies":       stats.TotalProxies,
		"valid_proxies":       stats.ValidProxies,
		"duplicate_count":     stats.DuplicateCount,
		"collection_duration": stats.Duration,
		"config": map[string]interface{}{
			"enabled":              c.config.Enabled,
			"collect_interval":     c.config.CollectInterval.String(),
			"concurrent_workers":   c.config.ConcurrentWorkers,
			"request_timeout":      c.config.RequestTimeout.String(),
			"verification_timeout": c.config.VerificationTimeout.String(),
			"min_quality_score":    c.config.MinQualityScore,
		},
	}
}

// ValidateConfig 验证配置
func ValidateConfig(config *config.CollectorConfig) error {
	if config.CollectInterval <= 0 {
		return fmt.Errorf("collect_interval must be positive")
	}

	if config.ConcurrentWorkers <= 0 {
		return fmt.Errorf("concurrent_workers must be positive")
	}

	if config.ConcurrentWorkers > 10000 {
		return fmt.Errorf("concurrent_workers should not exceed 10000")
	}

	if config.RequestTimeout <= 0 {
		return fmt.Errorf("request_timeout must be positive")
	}

	if config.VerificationTimeout <= 0 {
		return fmt.Errorf("verification_timeout must be positive")
	}

	if config.MaxRetries < 0 {
		return fmt.Errorf("max_retries cannot be negative")
	}

	if config.MinQualityScore < 0 || config.MinQualityScore > 1 {
		return fmt.Errorf("min_quality_score must be between 0 and 1")
	}

	if config.TestURL == "" {
		return fmt.Errorf("test_url cannot be empty")
	}

	if config.SourcesFile == "" {
		return fmt.Errorf("sources_file cannot be empty")
	}

	return nil
}

// IsValidProxyType 检查代理类型是否有效
func IsValidProxyType(proxyType string) bool {
	switch proxyType {
	case "http", "https", "socks4", "socks5":
		return true
	default:
		return false
	}
}

// ParseProxyAddress 解析代理地址
func ParseProxyAddress(address string) (host string, port int, err error) {
	host, portStr, err := net.SplitHostPort(address)
	if err != nil {
		return "", 0, fmt.Errorf("invalid address format: %w", err)
	}

	// 验证IP地址
	if net.ParseIP(host) == nil {
		return "", 0, fmt.Errorf("invalid IP address: %s", host)
	}

	// 解析端口
	port = 0
	if portStr != "" {
		if p, err := net.LookupPort("tcp", portStr); err == nil {
			port = p
		} else {
			return "", 0, fmt.Errorf("invalid port: %s", portStr)
		}
	}

	if port <= 0 || port > 65535 {
		return "", 0, fmt.Errorf("port out of range: %d", port)
	}

	return host, port, nil
}

// LogCollectionSummary 记录采集摘要
func (c *ProxyCollector) LogCollectionSummary(stats *CollectionStats) {
	successRate := 0.0
	if stats.TotalProxies > 0 {
		successRate = float64(stats.ValidProxies) / float64(stats.TotalProxies) * 100
	}

	c.logger.WithFields(logrus.Fields{
		"total_sources":   stats.TotalSources,
		"success_sources": stats.SuccessSources,
		"total_proxies":   stats.TotalProxies,
		"valid_proxies":   stats.ValidProxies,
		"duplicate_count": stats.DuplicateCount,
		"success_rate":    fmt.Sprintf("%.2f%%", successRate),
		"duration":        stats.Duration,
		"start_time":      stats.StartTime.Format(time.RFC3339),
		"end_time":        stats.EndTime.Format(time.RFC3339),
	}).Info("Collection summary")
}

// GetDefaultCollectorConfig 获取默认采集器配置
func GetDefaultCollectorConfig() *config.CollectorConfig {
	return &config.CollectorConfig{
		Enabled:             true,
		CollectInterval:     60 * time.Minute,
		SourcesFile:         "freeProxy/sources.json",
		ConcurrentWorkers:   20,
		RequestTimeout:      30 * time.Second,
		VerificationTimeout: 10 * time.Second,
		TestURL:             "http://httpbin.org/ip",
		MaxRetries:          3,
		RetryDelay:          5 * time.Second,
		SaveRawData:         true,
		RawDataPath:         "data/raw_proxies",
		MinQualityScore:     0.3,
		EnableGeoLocation:   true,
	}
}

// FormatDuration 格式化持续时间
func FormatDuration(d time.Duration) string {
	if d < time.Minute {
		return fmt.Sprintf("%.1fs", d.Seconds())
	} else if d < time.Hour {
		return fmt.Sprintf("%.1fm", d.Minutes())
	} else {
		return fmt.Sprintf("%.1fh", d.Hours())
	}
}

// GetCollectorStatus 获取采集器状态
func (c *ProxyCollector) GetCollectorStatus() map[string]interface{} {
	stats := c.GetStats()

	status := map[string]interface{}{
		"running":             c.IsRunning(),
		"enabled":             c.config.Enabled,
		"last_collection":     stats.LastCollection,
		"next_collection":     stats.LastCollection.Add(c.config.CollectInterval),
		"total_sources":       stats.TotalSources,
		"total_proxies":       stats.TotalProxies,
		"valid_proxies":       stats.ValidProxies,
		"duplicate_count":     stats.DuplicateCount,
		"success_rate":        0.0,
		"collection_duration": stats.Duration,
	}

	if stats.TotalProxies > 0 {
		status["success_rate"] = float64(stats.ValidProxies) / float64(stats.TotalProxies) * 100
	}

	return status
}

// LogError 记录错误日志
func (c *ProxyCollector) LogError(err error, context string, fields logrus.Fields) {
	if fields == nil {
		fields = logrus.Fields{}
	}
	fields["context"] = context

	c.logger.WithFields(fields).WithError(err).Error("Collector error")
}

// LogInfo 记录信息日志
func (c *ProxyCollector) LogInfo(message string, fields logrus.Fields) {
	if fields == nil {
		fields = logrus.Fields{}
	}

	c.logger.WithFields(fields).Info(message)
}
