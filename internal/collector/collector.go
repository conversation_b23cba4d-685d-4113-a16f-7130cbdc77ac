package collector

import (
	"context"
	"encoding/json"
	"fmt"
	"net/http"
	"os"
	"sync"
	"time"

	"proxyManager/internal/models"
	"proxyManager/internal/repository"
	"proxyManager/pkg/config"
	"proxyManager/pkg/database"

	"github.com/prometheus/client_golang/prometheus"
	"github.com/prometheus/client_golang/prometheus/promauto"
	"github.com/sirupsen/logrus"
)

// ProxySource 代理源配置
type ProxySource struct {
	HTTP   []string `json:"http"`
	HTTPS  []string `json:"https"`
	SOCKS4 []string `json:"socks4"`
	SOCKS5 []string `json:"socks5"`
	VMESS  []string `json:"vmess"`
	VLESS  []string `json:"vless"`
}

// CollectedProxy 采集到的代理数据
type CollectedProxy struct {
	Host           string                `json:"host"`
	Port           int                   `json:"port"`
	Type           models.ProxyType      `json:"type"`
	Username       string                `json:"username,omitempty"`
	Password       string                `json:"password,omitempty"`
	Source         string                `json:"source"`
	CollectedAt    time.Time             `json:"collected_at"`
	Verified       bool                  `json:"verified"`
	ResponseTime   *int64                `json:"response_time,omitempty"`
	Status         models.ProxyStatus    `json:"status"`
	Error          string                `json:"error,omitempty"`
	QualityScore   float64               `json:"quality_score"`
	AnonymityLevel models.AnonymityLevel `json:"anonymity_level"`
}

// CollectionStats 采集统计
type CollectionStats struct {
	TotalSources   int       `json:"total_sources"`
	SuccessSources int       `json:"success_sources"`
	TotalProxies   int       `json:"total_proxies"`
	ValidProxies   int       `json:"valid_proxies"`
	DuplicateCount int       `json:"duplicate_count"`
	StartTime      time.Time `json:"start_time"`
	EndTime        time.Time `json:"end_time"`
	Duration       string    `json:"duration"`
	LastCollection time.Time `json:"last_collection"`
}

// Collector 代理采集器接口
type Collector interface {
	// Start 启动采集器
	Start(ctx context.Context) error
	// Stop 停止采集器
	Stop() error
	// CollectOnce 执行一次采集
	CollectOnce(ctx context.Context) (*CollectionStats, error)
	// GetStats 获取采集统计
	GetStats() *CollectionStats
	// IsRunning 检查是否正在运行
	IsRunning() bool
}

// ProxyCollector 代理采集器实现
type ProxyCollector struct {
	config      *config.CollectorConfig
	proxyRepo   repository.ProxyRepository
	redisClient *database.RedisClient
	logger      *logrus.Logger
	httpClient  *http.Client

	// 运行状态
	running bool
	mu      sync.RWMutex
	stopCh  chan struct{}

	// 统计信息
	stats   *CollectionStats
	statsMu sync.RWMutex

	// Prometheus 指标
	metricsCollected    prometheus.Counter
	metricsValid        prometheus.Counter
	metricsDuplicate    prometheus.Counter
	metricsErrors       prometheus.Counter
	metricsSourceErrors prometheus.Counter
	collectionDuration  prometheus.Histogram
}

// Prometheus 指标定义
var (
	proxiesCollectedTotal = promauto.NewCounter(prometheus.CounterOpts{
		Name: "proxy_collector_proxies_collected_total",
		Help: "Total number of proxies collected",
	})

	proxiesValidTotal = promauto.NewCounter(prometheus.CounterOpts{
		Name: "proxy_collector_proxies_valid_total",
		Help: "Total number of valid proxies",
	})

	proxiesDuplicateTotal = promauto.NewCounter(prometheus.CounterOpts{
		Name: "proxy_collector_proxies_duplicate_total",
		Help: "Total number of duplicate proxies",
	})

	collectionErrorsTotal = promauto.NewCounter(prometheus.CounterOpts{
		Name: "proxy_collector_errors_total",
		Help: "Total number of collection errors",
	})

	sourceErrorsTotal = promauto.NewCounter(prometheus.CounterOpts{
		Name: "proxy_collector_source_errors_total",
		Help: "Total number of source errors",
	})

	collectionDurationSeconds = promauto.NewHistogram(prometheus.HistogramOpts{
		Name:    "proxy_collector_collection_duration_seconds",
		Help:    "Duration of proxy collection in seconds",
		Buckets: prometheus.DefBuckets,
	})
)

// NewProxyCollector 创建新的代理采集器
func NewProxyCollector(
	config *config.CollectorConfig,
	proxyRepo repository.ProxyRepository,
	redisClient *database.RedisClient,
	logger *logrus.Logger,
) *ProxyCollector {
	// 创建HTTP客户端
	httpClient := &http.Client{
		Timeout: config.RequestTimeout,
		Transport: &http.Transport{
			MaxIdleConns:        100,
			MaxIdleConnsPerHost: 10,
			IdleConnTimeout:     90 * time.Second,
		},
	}

	return &ProxyCollector{
		config:      config,
		proxyRepo:   proxyRepo,
		redisClient: redisClient,
		logger:      logger,
		httpClient:  httpClient,
		stopCh:      make(chan struct{}),
		stats: &CollectionStats{
			StartTime: time.Now(),
		},
		metricsCollected:    proxiesCollectedTotal,
		metricsValid:        proxiesValidTotal,
		metricsDuplicate:    proxiesDuplicateTotal,
		metricsErrors:       collectionErrorsTotal,
		metricsSourceErrors: sourceErrorsTotal,
		collectionDuration:  collectionDurationSeconds,
	}
}

// Start 启动采集器
func (c *ProxyCollector) Start(ctx context.Context) error {
	c.mu.Lock()
	defer c.mu.Unlock()

	if c.running {
		return fmt.Errorf("collector is already running")
	}

	if !c.config.Enabled {
		c.logger.Info("Proxy collector is disabled")
		return nil
	}

	c.running = true
	c.logger.Info("Starting proxy collector")

	// 启动采集循环
	go c.collectLoop(ctx)

	return nil
}

// Stop 停止采集器
func (c *ProxyCollector) Stop() error {
	c.mu.Lock()
	defer c.mu.Unlock()

	if !c.running {
		return nil
	}

	c.logger.Info("Stopping proxy collector")
	close(c.stopCh)
	c.running = false

	return nil
}

// IsRunning 检查是否正在运行
func (c *ProxyCollector) IsRunning() bool {
	c.mu.RLock()
	defer c.mu.RUnlock()
	return c.running
}

// GetStats 获取采集统计
func (c *ProxyCollector) GetStats() *CollectionStats {
	c.statsMu.RLock()
	defer c.statsMu.RUnlock()

	// 创建副本以避免并发访问问题
	stats := *c.stats
	return &stats
}

// collectLoop 采集循环
func (c *ProxyCollector) collectLoop(ctx context.Context) {
	ticker := time.NewTicker(c.config.CollectInterval)
	defer ticker.Stop()

	// 启动时立即执行一次采集
	if _, err := c.CollectOnce(ctx); err != nil {
		c.logger.WithError(err).Error("Initial collection failed")
	}

	for {
		select {
		case <-ctx.Done():
			c.logger.Info("Collection loop stopped due to context cancellation")
			return
		case <-c.stopCh:
			c.logger.Info("Collection loop stopped")
			return
		case <-ticker.C:
			if _, err := c.CollectOnce(ctx); err != nil {
				c.logger.WithError(err).Error("Scheduled collection failed")
			}
		}
	}
}

// CollectOnce 执行一次采集
func (c *ProxyCollector) CollectOnce(ctx context.Context) (*CollectionStats, error) {
	startTime := time.Now()
	c.logger.Info("Starting proxy collection")

	// 重置统计信息
	c.statsMu.Lock()
	c.stats = &CollectionStats{
		StartTime: startTime,
	}
	c.statsMu.Unlock()

	// 加载代理源配置
	sources, err := c.loadProxySources()
	if err != nil {
		c.metricsErrors.Inc()
		return nil, fmt.Errorf("failed to load proxy sources: %w", err)
	}

	// 采集所有代理
	collectedProxies, err := c.collectFromSources(ctx, sources)
	if err != nil {
		c.metricsErrors.Inc()
		return nil, fmt.Errorf("failed to collect proxies: %w", err)
	}

	// 验证代理
	validProxies, err := c.verifyProxies(ctx, collectedProxies)
	if err != nil {
		c.metricsErrors.Inc()
		return nil, fmt.Errorf("failed to verify proxies: %w", err)
	}

	// 保存到数据库
	savedCount, duplicateCount, err := c.saveProxies(ctx, validProxies)
	if err != nil {
		c.metricsErrors.Inc()
		return nil, fmt.Errorf("failed to save proxies: %w", err)
	}

	// 保存原始数据（如果启用）
	if c.config.SaveRawData {
		if err := c.saveRawData(collectedProxies); err != nil {
			c.logger.WithError(err).Error("Failed to save raw data")
		}
	}

	// 更新统计信息
	endTime := time.Now()
	duration := endTime.Sub(startTime)

	c.statsMu.Lock()
	c.stats.TotalSources = c.getTotalSourceCount(sources)
	c.stats.TotalProxies = len(collectedProxies)
	c.stats.ValidProxies = savedCount
	c.stats.DuplicateCount = duplicateCount
	c.stats.EndTime = endTime
	c.stats.Duration = duration.String()
	c.stats.LastCollection = endTime
	stats := *c.stats
	c.statsMu.Unlock()

	// 更新 Prometheus 指标
	c.metricsCollected.Add(float64(len(collectedProxies)))
	c.metricsValid.Add(float64(savedCount))
	c.metricsDuplicate.Add(float64(duplicateCount))
	c.collectionDuration.Observe(duration.Seconds())

	c.logger.WithFields(logrus.Fields{
		"total_proxies":   len(collectedProxies),
		"valid_proxies":   savedCount,
		"duplicate_count": duplicateCount,
		"duration":        duration.String(),
	}).Info("Proxy collection completed")

	return &stats, nil
}

// loadProxySources 加载代理源配置
func (c *ProxyCollector) loadProxySources() (*ProxySource, error) {
	file, err := os.Open(c.config.SourcesFile)
	if err != nil {
		return nil, fmt.Errorf("failed to open sources file: %w", err)
	}
	defer file.Close()

	var sources ProxySource
	decoder := json.NewDecoder(file)
	if err := decoder.Decode(&sources); err != nil {
		return nil, fmt.Errorf("failed to decode sources file: %w", err)
	}

	return &sources, nil
}

// getTotalSourceCount 获取总源数量
func (c *ProxyCollector) getTotalSourceCount(sources *ProxySource) int {
	return len(sources.HTTP) + len(sources.HTTPS) + len(sources.SOCKS4) + len(sources.SOCKS5)
}
