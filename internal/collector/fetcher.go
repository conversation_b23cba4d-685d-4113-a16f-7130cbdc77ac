package collector

import (
	"bufio"
	"context"
	"encoding/json"
	"fmt"
	"net/http"
	"os"
	"path/filepath"
	"regexp"
	"strconv"
	"strings"
	"sync"
	"time"

	"proxyManager/internal/models"

	"github.com/sirupsen/logrus"
)

// SourceResult 源采集结果
type SourceResult struct {
	URL     string            `json:"url"`
	Type    models.ProxyType  `json:"type"`
	Proxies []*CollectedProxy `json:"proxies"`
	Error   string            `json:"error,omitempty"`
	Success bool              `json:"success"`
}

// CompactProxy 紧凑的代理数据结构（用于缓存）
type CompactProxy struct {
	Host   string           `json:"h"` // 主机地址
	Port   int              `json:"p"` // 端口
	Type   models.ProxyType `json:"t"` // 代理类型
	Source string           `json:"s"` // 来源（简化为域名）
}

// ProxySourceCache 代理源缓存结构（优化版）
type ProxySourceCache struct {
	Proxies   []*CompactProxy `json:"proxies"`
	Timestamp time.Time       `json:"timestamp"`
	Version   string          `json:"version"`
	Count     int             `json:"count"` // 代理数量
}

// toCompactProxy 将 CollectedProxy 转换为 CompactProxy
func toCompactProxy(proxy *CollectedProxy) *CompactProxy {
	return &CompactProxy{
		Host:   proxy.Host,
		Port:   proxy.Port,
		Type:   proxy.Type,
		Source: proxy.Source,
	}
}

// fromCompactProxy 将 CompactProxy 转换为 CollectedProxy
func fromCompactProxy(compact *CompactProxy) *CollectedProxy {
	return &CollectedProxy{
		Host:         compact.Host,
		Port:         compact.Port,
		Type:         compact.Type,
		Source:       compact.Source,
		CollectedAt:  time.Now(),
		Verified:     false,
		Status:       models.ProxyStatusInactive, // 缓存的代理默认为未激活状态
		QualityScore: 0.0,
	}
}

// getCacheFilePath 获取缓存文件路径
func (c *ProxyCollector) getCacheFilePath() string {
	cacheDir := "freeProxy"
	if err := os.MkdirAll(cacheDir, 0755); err != nil {
		c.logger.WithError(err).Warn("Failed to create cache directory, using current directory")
		cacheDir = "."
	}
	return filepath.Join(cacheDir, "proxy_sources_cache.json")
}

// loadProxySourceCache 加载代理源缓存
func (c *ProxyCollector) loadProxySourceCache() (*ProxySourceCache, error) {
	cacheFile := c.getCacheFilePath()

	// 检查文件是否存在
	if _, err := os.Stat(cacheFile); os.IsNotExist(err) {
		return nil, nil // 缓存文件不存在
	}

	// 读取缓存文件
	data, err := os.ReadFile(cacheFile)
	if err != nil {
		return nil, fmt.Errorf("failed to read cache file: %w", err)
	}

	// 解析缓存数据
	var cache ProxySourceCache
	if err := json.Unmarshal(data, &cache); err != nil {
		return nil, fmt.Errorf("failed to unmarshal cache data: %w", err)
	}

	// 检查缓存是否过期（24小时）
	if time.Since(cache.Timestamp) > 24*time.Hour {
		c.logger.Info("Proxy source cache expired, will fetch fresh data")
		return nil, nil
	}

	return &cache, nil
}

// saveProxySourceCache 保存代理源缓存
func (c *ProxyCollector) saveProxySourceCache(proxies []*CollectedProxy) error {
	cacheFile := c.getCacheFilePath()

	// 转换为紧凑格式
	compactProxies := make([]*CompactProxy, len(proxies))
	for i, proxy := range proxies {
		compactProxies[i] = toCompactProxy(proxy)
	}

	cache := ProxySourceCache{
		Proxies:   compactProxies,
		Timestamp: time.Now(),
		Version:   "2.0", // 升级版本号表示使用紧凑格式
		Count:     len(proxies),
	}

	// 序列化缓存数据
	data, err := json.MarshalIndent(cache, "", "  ")
	if err != nil {
		return fmt.Errorf("failed to marshal cache data: %w", err)
	}

	// 写入缓存文件
	if err := os.WriteFile(cacheFile, data, 0644); err != nil {
		return fmt.Errorf("failed to write cache file: %w", err)
	}

	c.logger.WithFields(logrus.Fields{
		"cache_file":  cacheFile,
		"proxy_count": len(proxies),
		"cache_size":  len(data),
	}).Info("Proxy source cache saved successfully")

	return nil
}

// collectFromSources 从所有源采集代理（带缓存支持）
func (c *ProxyCollector) collectFromSources(ctx context.Context, sources *ProxySource) ([]*CollectedProxy, error) {
	// 尝试加载缓存
	cache, err := c.loadProxySourceCache()
	if err != nil {
		c.logger.WithError(err).Warn("Failed to load proxy source cache, will fetch fresh data")
	}

	// 如果缓存有效，直接返回缓存数据
	if cache != nil {
		// 转换为完整格式
		collectedProxies := make([]*CollectedProxy, len(cache.Proxies))
		for i, compact := range cache.Proxies {
			collectedProxies[i] = fromCompactProxy(compact)
		}

		c.logger.WithFields(logrus.Fields{
			"cached_proxies": len(cache.Proxies),
			"cache_age":      time.Since(cache.Timestamp).String(),
			"cache_version":  cache.Version,
		}).Info("Using cached proxy sources data")
		return collectedProxies, nil
	}

	// 缓存无效或不存在，从源采集数据
	c.logger.Info("Cache not available or expired, fetching fresh proxy data from sources")

	var allProxies []*CollectedProxy
	var mu sync.Mutex
	var wg sync.WaitGroup

	// 创建工作池
	semaphore := make(chan struct{}, c.config.ConcurrentWorkers)

	// 采集HTTP代理
	for _, url := range sources.HTTP {
		wg.Add(1)
		go func(url string) {
			defer wg.Done()
			semaphore <- struct{}{}
			defer func() { <-semaphore }()

			proxies := c.fetchFromSource(ctx, url, models.ProxyTypeHTTP)
			mu.Lock()
			allProxies = append(allProxies, proxies...)
			mu.Unlock()
		}(url)
	}

	// 采集HTTPS代理
	for _, url := range sources.HTTPS {
		wg.Add(1)
		go func(url string) {
			defer wg.Done()
			semaphore <- struct{}{}
			defer func() { <-semaphore }()

			proxies := c.fetchFromSource(ctx, url, models.ProxyTypeHTTPS)
			mu.Lock()
			allProxies = append(allProxies, proxies...)
			mu.Unlock()
		}(url)
	}

	// 采集SOCKS5代理
	for _, url := range sources.SOCKS5 {
		wg.Add(1)
		go func(url string) {
			defer wg.Done()
			semaphore <- struct{}{}
			defer func() { <-semaphore }()

			proxies := c.fetchFromSource(ctx, url, models.ProxyTypeSOCKS5)
			mu.Lock()
			allProxies = append(allProxies, proxies...)
			mu.Unlock()
		}(url)
	}

	// 等待所有采集完成
	wg.Wait()

	c.logger.WithField("total_collected", len(allProxies)).Info("Finished collecting from all sources")

	// 保存到缓存
	if len(allProxies) > 0 {
		if err := c.saveProxySourceCache(allProxies); err != nil {
			c.logger.WithError(err).Warn("Failed to save proxy source cache")
		}
	}

	return allProxies, nil
}

// fetchFromSource 从单个源获取代理
func (c *ProxyCollector) fetchFromSource(ctx context.Context, sourceURL string, proxyType models.ProxyType) []*CollectedProxy {
	logger := c.logger.WithFields(logrus.Fields{
		"source": sourceURL,
		"type":   proxyType,
	})

	logger.Debug("Fetching proxies from source")

	// 创建请求
	req, err := http.NewRequestWithContext(ctx, "GET", sourceURL, nil)
	if err != nil {
		logger.WithError(err).Error("Failed to create request")
		c.metricsSourceErrors.Inc()
		return nil
	}

	// 设置User-Agent
	req.Header.Set("User-Agent", "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36")

	// 发送请求
	resp, err := c.httpClient.Do(req)
	if err != nil {
		logger.WithError(err).Error("Failed to fetch from source")
		c.metricsSourceErrors.Inc()
		return nil
	}
	defer resp.Body.Close()

	if resp.StatusCode != http.StatusOK {
		logger.WithField("status_code", resp.StatusCode).Error("Non-200 response from source")
		c.metricsSourceErrors.Inc()
		return nil
	}

	// 解析响应
	proxies, err := c.parseProxyList(resp, sourceURL, proxyType)
	if err != nil {
		logger.WithError(err).Error("Failed to parse proxy list")
		c.metricsSourceErrors.Inc()
		return nil
	}

	logger.WithField("count", len(proxies)).Debug("Successfully fetched proxies from source")
	return proxies
}

// parseProxyList 解析代理列表
func (c *ProxyCollector) parseProxyList(resp *http.Response, sourceURL string, proxyType models.ProxyType) ([]*CollectedProxy, error) {
	var proxies []*CollectedProxy
	scanner := bufio.NewScanner(resp.Body)

	// 正则表达式匹配 IP:PORT 格式
	ipPortRegex := regexp.MustCompile(`^(\d{1,3}\.\d{1,3}\.\d{1,3}\.\d{1,3}):(\d{1,5})$`)
	// 匹配带协议前缀的格式，如 http://ip:port
	protocolRegex := regexp.MustCompile(`^(?:https?|socks[45]?)://(\d{1,3}\.\d{1,3}\.\d{1,3}\.\d{1,3}):(\d{1,5})$`)
	// 匹配带用户名密码的格式，如 username:password@ip:port
	authRegex := regexp.MustCompile(`^([^:]+):([^@]+)@(\d{1,3}\.\d{1,3}\.\d{1,3}\.\d{1,3}):(\d{1,5})$`)

	lineCount := 0
	for scanner.Scan() {
		lineCount++
		line := strings.TrimSpace(scanner.Text())

		// 跳过空行和注释
		if line == "" || strings.HasPrefix(line, "#") || strings.HasPrefix(line, "//") {
			continue
		}

		var host string
		var port int
		var username, password string
		var err error

		// 尝试匹配不同格式
		if matches := authRegex.FindStringSubmatch(line); matches != nil {
			// 格式: username:password@ip:port
			username = matches[1]
			password = matches[2]
			host = matches[3]
			port, err = strconv.Atoi(matches[4])
		} else if matches := protocolRegex.FindStringSubmatch(line); matches != nil {
			// 格式: protocol://ip:port
			host = matches[1]
			port, err = strconv.Atoi(matches[2])
		} else if matches := ipPortRegex.FindStringSubmatch(line); matches != nil {
			// 格式: ip:port
			host = matches[1]
			port, err = strconv.Atoi(matches[2])
		} else {
			// 尝试按空格或制表符分割
			parts := regexp.MustCompile(`\s+`).Split(line, -1)
			if len(parts) >= 2 {
				if matches := ipPortRegex.FindStringSubmatch(parts[0]); matches != nil {
					host = matches[1]
					port, err = strconv.Atoi(matches[2])
				}
			}
		}

		if err != nil || host == "" || port <= 0 || port > 65535 {
			continue
		}

		// 验证IP地址格式
		if !c.isValidIP(host) {
			continue
		}

		proxy := &CollectedProxy{
			Host:        host,
			Port:        port,
			Type:        proxyType,
			Username:    username,
			Password:    password,
			Source:      sourceURL,
			CollectedAt: time.Now(),
			Status:      models.ProxyStatusInactive, // 初始状态为未验证
		}

		proxies = append(proxies, proxy)
	}

	if err := scanner.Err(); err != nil {
		return nil, fmt.Errorf("error reading response: %w", err)
	}

	return proxies, nil
}

// isValidIP 验证IP地址格式
func (c *ProxyCollector) isValidIP(ip string) bool {
	parts := strings.Split(ip, ".")
	if len(parts) != 4 {
		return false
	}

	for _, part := range parts {
		num, err := strconv.Atoi(part)
		if err != nil || num < 0 || num > 255 {
			return false
		}
	}

	return true
}

// saveRawData 保存原始数据
func (c *ProxyCollector) saveRawData(proxies []*CollectedProxy) error {
	// 确保目录存在
	if err := c.ensureDataDir(); err != nil {
		return err
	}

	// 创建带时间戳的文件名
	timestamp := time.Now().Format("20060102_150405")
	filename := fmt.Sprintf("raw_proxies_%s.json", timestamp)
	filepath := fmt.Sprintf("%s/%s", c.config.RawDataPath, filename)

	// 保存数据
	data := map[string]interface{}{
		"timestamp":     time.Now(),
		"total_count":   len(proxies),
		"proxies":       proxies,
		"collection_id": timestamp,
	}

	return c.saveJSONFile(filepath, data)
}
