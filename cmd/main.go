package main

import (
	"context"
	"fmt"
	"log"
	"net/http"
	"os"
	"os/signal"
	"path/filepath"
	"runtime"
	"syscall"
	"time"

	"proxyManager/api/handlers"
	"proxyManager/api/routes"
	"proxyManager/internal/auth"
	"proxyManager/internal/collector"
	"proxyManager/internal/middleware"
	"proxyManager/internal/models"
	"proxyManager/internal/proxy"
	"proxyManager/internal/repository"
	"proxyManager/internal/repository/postgres"
	"proxyManager/internal/task"
	"proxyManager/pkg/config"
	"proxyManager/pkg/database"

	"github.com/gin-gonic/gin"
	"github.com/joho/godotenv"
	"github.com/sirupsen/logrus"
)

func getCurrentFileDir() string {
	_, file, _, ok := runtime.Caller(1) // 获取调用者的文件路径
	if !ok {
		panic("unable to get current file path")
	}
	return filepath.Dir(file) // 获取文件所在目录
}
func main() {
	currentDir := getCurrentFileDir()
	envPath := filepath.Join(currentDir, "..", ".env")
	configPath := filepath.Join(currentDir, "..", "config", "config.yaml")

	_ = godotenv.Load(envPath)

	cfg, err := config.Load(configPath)
	if err != nil {
		cfg = config.GetDefaultConfig()
		log.Printf("Using default config: %v", err)
	}

	logger := setupLogger(cfg.Logging)
	logger.Info("Starting Proxy Manager...")

	postgresClient, err := database.NewPostgresClient(&database.PostgresConfig{
		Host:            cfg.Postgres.Host,
		Port:            cfg.Postgres.Port,
		User:            cfg.Postgres.User,
		Password:        cfg.Postgres.Password,
		Database:        cfg.Postgres.Database,
		SSLMode:         cfg.Postgres.SSLMode,
		MaxOpenConns:    cfg.Postgres.MaxOpenConns,
		MaxIdleConns:    cfg.Postgres.MaxIdleConns,
		ConnMaxLifetime: cfg.Postgres.ConnMaxLifetime,
		ConnMaxIdleTime: cfg.Postgres.ConnMaxIdleTime,
	}, logger)
	if err != nil {
		logger.Fatalf("Failed to connect to PostgreSQL: %v", err)
	}
	logger.Info("Connected to PostgreSQL")

	redisClient := database.NewRedisClient(
		cfg.Redis.Addr,
		cfg.Redis.Password,
		cfg.Redis.DB,
		cfg.Redis.PoolSize,
		cfg.Redis.MinIdleConns,
		logger,
	)

	ctx := context.Background()
	if err := redisClient.Ping(ctx); err != nil {
		logger.Fatalf("Failed to connect to Redis: %v", err)
	}
	logger.Info("Connected to Redis")

	// 运行数据库迁移
	logger.Info("Running database migrations...")
	migrationManager := database.NewMigrationManager(postgresClient.GetDB(), logger)
	migrationsDir := filepath.Join(getCurrentFileDir(), "..", "migrations")
	if err := migrationManager.RunMigrations(ctx, migrationsDir); err != nil {
		logger.Fatalf("Failed to run database migrations: %v", err)
	}
	logger.Info("Database migrations completed successfully")

	// 创建仓储管理器
	repoManager := postgres.NewRepositoryManager(postgresClient, logger)

	// 初始化管理员用户
	logger.Info("Initializing admin user...")
	if err := initializeAdminUser(ctx, repoManager, cfg, logger); err != nil {
		logger.Fatalf("Failed to initialize admin user: %v", err)
	}
	logger.Info("Admin user initialization completed")

	// 创建JWT管理器
	jwtManager := auth.NewJWTManager(cfg.Auth.JWTSecret, cfg.Auth.TokenExpiry)

	// 创建代理管理器
	proxyConfig := &proxy.Config{
		HealthCheckInterval: cfg.Proxy.HealthCheckInterval,
		MaxFailures:         cfg.Proxy.MaxFailures,
		Timeout:             cfg.Proxy.Timeout,
		MaxRetries:          cfg.Proxy.MaxRetries,
		// 并发健康检查配置
		ConcurrentWorkers: 20,               // 20个并发工作者
		BatchTimeout:      60 * time.Second, // 60秒批量检查超时
		RetryDelay:        2 * time.Second,  // 2秒重试延迟
	}
	proxyManager := proxy.NewManager(redisClient, repoManager.GetProxy(), repoManager.GetProxyTag(), proxyConfig, logger)

	// 创建任务管理器
	taskConfig := &task.Config{
		MaxConcurrent: cfg.Task.MaxConcurrent,
		QueueSize:     cfg.Task.QueueSize,
		RetryAttempts: cfg.Task.RetryAttempts,
		RetryDelay:    cfg.Task.RetryDelay,
	}
	taskManager := task.NewManager(redisClient, proxyManager, taskConfig, logger)

	// 创建代理采集器
	proxyCollector := collector.NewProxyCollector(
		&cfg.Collector,
		repoManager.GetProxy(),
		redisClient,
		logger,
	)

	// 创建认证中间件
	authMiddleware := middleware.NewAuthMiddleware(jwtManager, cfg.Auth.SuperApiKey, redisClient)

	// 创建处理器
	authHandler := handlers.NewAuthHandler(repoManager, redisClient, jwtManager, logger)
	proxyHandler := handlers.NewProxyHandler(proxyManager, logger)
	taskHandler := handlers.NewTaskHandler(taskManager, logger)
	apiKeyHandler := handlers.NewAPIKeyHandler(repoManager, redisClient, logger)
	settingsHandler := handlers.NewSettingsHandler(repoManager, logger)
	collectorHandler := handlers.NewCollectorHandler(proxyCollector, logger)

	// 设置Gin模式
	gin.SetMode(cfg.Server.Mode)

	// 创建路由
	router := gin.New()
	router.Use(middleware.CORS())
	router.Use(middleware.Logger(logger))
	router.Use(gin.Recovery())

	// 设置路由
	routes.SetupRoutes(router, authHandler, proxyHandler, taskHandler, apiKeyHandler, settingsHandler, collectorHandler, authMiddleware, proxyManager, logger)

	// 启动健康检查
	healthCtx, healthCancel := context.WithCancel(context.Background())
	defer healthCancel()
	go proxyManager.StartHealthCheck(healthCtx)
	logger.Info("Started proxy health check")

	// 启动任务工作器
	workerCtx, workerCancel := context.WithCancel(context.Background())
	defer workerCancel()
	go taskManager.StartWorker(workerCtx)
	logger.Info("Started task worker")

	// 启动质量评估服务
	qualityCtx, qualityCancel := context.WithCancel(context.Background())
	defer qualityCancel()
	go proxyManager.StartQualityAssessment(qualityCtx)
	logger.Info("Started proxy quality assessment")

	// 启动代理采集器
	collectorCtx, collectorCancel := context.WithCancel(context.Background())
	defer collectorCancel()
	if err := proxyCollector.Start(collectorCtx); err != nil {
		logger.WithError(err).Error("Failed to start proxy collector")
	} else {
		logger.Info("Started proxy collector")
	}

	// 启动HTTP服务器
	server := &http.Server{
		Addr:         fmt.Sprintf(":%d", cfg.Server.Port),
		Handler:      router,
		ReadTimeout:  cfg.Server.ReadTimeout,
		WriteTimeout: cfg.Server.WriteTimeout,
	}

	// 优雅关闭
	go func() {
		if err := server.ListenAndServe(); err != nil && err != http.ErrServerClosed {
			logger.Fatalf("Failed to start server: %v", err)
		}
	}()

	logger.Infof("Server started on port %d, visit http://localhost:%d", cfg.Server.Port, cfg.Server.Port)

	// 等待中断信号
	quit := make(chan os.Signal, 1)
	signal.Notify(quit, syscall.SIGINT, syscall.SIGTERM)
	<-quit

	logger.Info("Shutting down server...")

	// 优雅关闭
	shutdownCtx, cancel := context.WithTimeout(context.Background(), 30*time.Second)
	defer cancel()

	if err := server.Shutdown(shutdownCtx); err != nil {
		logger.Errorf("Server forced to shutdown: %v", err)
	}

	// 停止后台任务
	healthCancel()
	workerCancel()
	qualityCancel()
	collectorCancel()

	// 停止采集器
	if err := proxyCollector.Stop(); err != nil {
		logger.Errorf("Failed to stop proxy collector: %v", err)
	}

	// 关闭数据库连接
	if err := postgresClient.Close(); err != nil {
		logger.Errorf("Failed to close PostgreSQL connection: %v", err)
	}

	// 关闭Redis连接
	if err := redisClient.Close(); err != nil {
		logger.Errorf("Failed to close Redis connection: %v", err)
	}

	logger.Info("Server exited")
}

func setupLogger(cfg config.LoggingConfig) *logrus.Logger {
	logger := logrus.New()

	// 设置日志级别
	level, err := logrus.ParseLevel(cfg.Level)
	if err != nil {
		level = logrus.InfoLevel
	}
	logger.SetLevel(level)

	// 设置日志格式
	if cfg.Format == "json" {
		logger.SetFormatter(&logrus.JSONFormatter{})
	} else {
		logger.SetFormatter(&logrus.TextFormatter{
			FullTimestamp: true,
		})
	}

	// 设置日志输出
	if cfg.Output == "file" {
		file, err := os.OpenFile(cfg.FilePath, os.O_CREATE|os.O_WRONLY|os.O_APPEND, 0666)
		if err != nil {
			log.Printf("Failed to open log file: %v", err)
		} else {
			logger.SetOutput(file)
		}
	}

	return logger
}

// initializeAdminUser 初始化管理员用户
func initializeAdminUser(ctx context.Context, repoManager repository.RepositoryManager, cfg *config.Config, logger *logrus.Logger) error {
	// 从环境变量获取管理员账户信息
	adminUsername := os.Getenv("PROXY_MANAGER_ADMIN_USERNAME")
	adminPassword := os.Getenv("PROXY_MANAGER_ADMIN_PASSWORD")

	if adminUsername == "" {
		adminUsername = "admin" // 默认用户名
	}

	if adminPassword == "" {
		adminPassword = "admin123" // 默认密码
	}

	// 检查管理员用户是否已存在
	existingUser, err := repoManager.GetUser().GetByUsername(ctx, adminUsername)
	if err != nil {
		return fmt.Errorf("failed to check existing admin user: %w", err)
	}

	if existingUser != nil {
		logger.WithField("username", adminUsername).Info("Admin user already exists, skipping creation")
		return nil
	}

	// 哈希密码
	hashedPassword, err := auth.HashPassword(adminPassword)
	if err != nil {
		return fmt.Errorf("failed to hash admin password: %w", err)
	}

	// 生成API密钥
	apiKey, err := auth.GenerateAPIKey()
	if err != nil {
		return fmt.Errorf("failed to generate admin API key: %w", err)
	}

	// 创建管理员用户
	now := time.Now()
	adminUser := &models.User{
		Username:         adminUsername,
		Email:            adminUsername + "@proxymanager.local", // 默认邮箱
		Password:         hashedPassword,
		Role:             models.UserRoleAdmin,
		Status:           models.UserStatusActive,
		CreatedAt:        now,
		UpdatedAt:        now,
		APIKey:           apiKey,
		APIKeyCreated:    now,
		APIKeyLastUsed:   time.Time{},
		APIKeyUsageCount: 0,
	}

	// 保存管理员用户
	if err := repoManager.GetUser().Create(ctx, adminUser); err != nil {
		return fmt.Errorf("failed to create admin user: %w", err)
	}

	logger.WithFields(logrus.Fields{
		"username": adminUsername,
		"user_id":  adminUser.ID,
	}).Info("Admin user created successfully")

	return nil
}
