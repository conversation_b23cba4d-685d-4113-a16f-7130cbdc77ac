import React, { createContext, useContext, useState, useEffect, ReactNode } from 'react'
import { authAPI } from '../services/api'
import { enhancedToast } from '../utils/toastConfig'
import { apiCall } from '../utils/apiWrapper'
import type { User, LoginCredentials, RegisterData } from '../types'

interface AuthContextType {
  user: User | null
  loading: boolean
  isAuthenticated: boolean
  login: (credentials: LoginCredentials) => Promise<{ success: boolean; error?: string }>
  register: (userData: RegisterData) => Promise<{ success: boolean; error?: string }>
  logout: () => void
  updateUser: (userData: User) => void
}

interface AuthProviderProps {
  children: ReactNode
}

const AuthContext = createContext<AuthContextType | undefined>(undefined)

export const useAuth = (): AuthContextType => {
  const context = useContext(AuthContext)
  if (!context) {
    throw new Error('useAuth must be used within an AuthProvider')
  }
  return context
}

export const AuthProvider: React.FC<AuthProviderProps> = ({ children }) => {
  const [user, setUser] = useState<User | null>(null)
  const [loading, setLoading] = useState(true)
  const [isAuthenticated, setIsAuthenticated] = useState(false)

  // 初始化时检查本地存储的token
  useEffect(() => {
    const initAuth = async () => {
      const token = localStorage.getItem('token')
      const userData = localStorage.getItem('user')
      
      if (token && userData) {
        try {
          // 验证token是否有效
          const response = await authAPI.getProfile()
          setUser(response.data.data!)
          setIsAuthenticated(true)
        } catch (error) {
          // token无效，清除本地存储
          localStorage.removeItem('token')
          localStorage.removeItem('user')
          setUser(null)
          setIsAuthenticated(false)
        }
      }
      setLoading(false)
    }

    initAuth()
  }, [])

  // 登录
  const login = async (credentials: LoginCredentials): Promise<{ success: boolean; error?: string }> => {
    setLoading(true)

    const result = await apiCall(
      () => authAPI.login(credentials),
      {
        showLoading: true,
        loadingMessage: '登录中...',
        showSuccessToast: true,
        successMessage: '登录成功',
        customErrorMessage: '登录失败，请检查用户名和密码',
      }
    )

    setLoading(false)

    if (result.success && result.data) {
      const { access_token, user: userData } = result.data

      // 保存token和用户信息
      localStorage.setItem('token', access_token)
      localStorage.setItem('user', JSON.stringify(userData))

      setUser(userData)
      setIsAuthenticated(true)

      return { success: true }
    }

    return {
      success: false,
      error: result.error?.message || '登录失败'
    }
  }

  // 注册
  const register = async (userData: RegisterData): Promise<{ success: boolean; error?: string }> => {
    setLoading(true)

    const result = await apiCall(
      () => authAPI.register(userData),
      {
        showLoading: true,
        loadingMessage: '注册中...',
        showSuccessToast: true,
        successMessage: '注册成功，请登录',
        customErrorMessage: '注册失败，请检查输入信息',
      }
    )

    setLoading(false)

    return {
      success: result.success,
      error: result.error?.message
    }
  }

  // 登出
  const logout = (): void => {
    localStorage.removeItem('token')
    localStorage.removeItem('user')
    setUser(null)
    setIsAuthenticated(false)
    enhancedToast.success('已退出登录')
  }

  // 更新用户信息
  const updateUser = (userData: User): void => {
    setUser(userData)
    localStorage.setItem('user', JSON.stringify(userData))
  }

  const value: AuthContextType = {
    user,
    loading,
    isAuthenticated,
    login,
    register,
    logout,
    updateUser,
  }

  return (
    <AuthContext.Provider value={value}>
      {children}
    </AuthContext.Provider>
  )
}
