import { createContext, useContext, useState, useEffect, ReactNode } from 'react'

// 导入翻译文件
import zhCN from './locales/zh-CN.json'
import enUS from './locales/en-US.json'

// 支持的语言
export const SUPPORTED_LANGUAGES = {
  'zh-CN': {
    name: '简体中文',
    nativeName: '简体中文',
    flag: '🇨🇳'
  },
  'en-US': {
    name: 'English',
    nativeName: 'English',
    flag: '🇺🇸'
  }
} as const

export type SupportedLanguage = keyof typeof SUPPORTED_LANGUAGES

// 翻译资源
const translations = {
  'zh-CN': zhCN,
  'en-US': enUS
}

// 类型定义
type TranslationKey = string
type TranslationParams = Record<string, string | number>

// 国际化上下文
interface I18nContextType {
  language: SupportedLanguage
  setLanguage: (language: SupportedLanguage) => void
  t: (key: TranslationKey, params?: TranslationParams) => string
  languages: typeof SUPPORTED_LANGUAGES
}

const I18nContext = createContext<I18nContextType | undefined>(undefined)

// 获取嵌套对象的值
function getNestedValue(obj: any, path: string): string | undefined {
  return path.split('.').reduce((current, key) => {
    return current && current[key] !== undefined ? current[key] : undefined
  }, obj)
}

// 翻译函数
function translate(
  language: SupportedLanguage,
  key: TranslationKey,
  params?: TranslationParams
): string {
  const translation = translations[language]
  const value = getNestedValue(translation, key)
  
  if (value === undefined) {
    // 如果当前语言没有翻译，尝试使用英文
    if (language !== 'en-US') {
      const fallbackValue = getNestedValue(translations['en-US'], key)
      if (fallbackValue !== undefined) {
        return interpolate(fallbackValue, params)
      }
    }
    
    // 如果都没有，返回key本身
    console.warn(`Translation missing for key: ${key} in language: ${language}`)
    return key
  }
  
  return interpolate(value, params)
}

// 参数插值
function interpolate(template: string, params?: TranslationParams): string {
  if (!params) return template
  
  return template.replace(/\{(\w+)\}/g, (match, key) => {
    return params[key] !== undefined ? String(params[key]) : match
  })
}

// 检测浏览器语言
function detectBrowserLanguage(): SupportedLanguage {
  const browserLang = navigator.language || navigator.languages?.[0] || 'en-US'
  
  // 精确匹配
  if (browserLang in SUPPORTED_LANGUAGES) {
    return browserLang as SupportedLanguage
  }
  
  // 语言代码匹配（如 zh 匹配 zh-CN）
  const langCode = browserLang.split('-')[0]
  const matchedLang = Object.keys(SUPPORTED_LANGUAGES).find(lang => 
    lang.startsWith(langCode)
  )
  
  return (matchedLang as SupportedLanguage) || 'en-US'
}

// 国际化提供者组件
interface I18nProviderProps {
  children: ReactNode
  defaultLanguage?: SupportedLanguage
}

export function I18nProvider({ children, defaultLanguage }: I18nProviderProps) {
  const [language, setLanguageState] = useState<SupportedLanguage>(() => {
    // 优先级：localStorage > defaultLanguage > 浏览器检测 > 英文
    const savedLanguage = localStorage.getItem('language') as SupportedLanguage
    if (savedLanguage && savedLanguage in SUPPORTED_LANGUAGES) {
      return savedLanguage
    }
    
    return defaultLanguage || detectBrowserLanguage()
  })

  const setLanguage = (newLanguage: SupportedLanguage) => {
    setLanguageState(newLanguage)
    localStorage.setItem('language', newLanguage)
    
    // 更新HTML lang属性
    document.documentElement.lang = newLanguage
  }

  const t = (key: TranslationKey, params?: TranslationParams) => {
    return translate(language, key, params)
  }

  // 初始化时设置HTML lang属性
  useEffect(() => {
    document.documentElement.lang = language
  }, [language])

  const contextValue: I18nContextType = {
    language,
    setLanguage,
    t,
    languages: SUPPORTED_LANGUAGES
  }

  return (
    <I18nContext.Provider value={contextValue}>
      {children}
    </I18nContext.Provider>
  )
}

// 使用国际化的Hook
export function useI18n() {
  const context = useContext(I18nContext)
  if (context === undefined) {
    throw new Error('useI18n must be used within an I18nProvider')
  }
  return context
}

// 翻译Hook（简化版本）
export function useTranslation() {
  const { t, language } = useI18n()
  return { t, language }
}

// 语言选择器Hook
export function useLanguageSelector() {
  const { language, setLanguage, languages } = useI18n()
  
  const languageOptions = Object.entries(languages).map(([code, info]) => ({
    value: code as SupportedLanguage,
    label: info.name,
    nativeLabel: info.nativeName,
    flag: info.flag
  }))
  
  return {
    currentLanguage: language,
    setLanguage,
    languageOptions,
    isLanguageSupported: (lang: string): lang is SupportedLanguage => {
      return lang in SUPPORTED_LANGUAGES
    }
  }
}

// 格式化工具函数
export const formatters = {
  // 格式化数字
  number: (value: number, language: SupportedLanguage = 'en-US') => {
    return new Intl.NumberFormat(language).format(value)
  },
  
  // 格式化货币
  currency: (value: number, currency = 'USD', language: SupportedLanguage = 'en-US') => {
    return new Intl.NumberFormat(language, {
      style: 'currency',
      currency
    }).format(value)
  },
  
  // 格式化日期
  date: (date: Date | string, language: SupportedLanguage = 'en-US', options?: Intl.DateTimeFormatOptions) => {
    const dateObj = typeof date === 'string' ? new Date(date) : date
    return new Intl.DateTimeFormat(language, options).format(dateObj)
  },
  
  // 格式化相对时间
  relativeTime: (date: Date | string, language: SupportedLanguage = 'en-US') => {
    const dateObj = typeof date === 'string' ? new Date(date) : date
    const now = new Date()
    const diffInSeconds = Math.floor((now.getTime() - dateObj.getTime()) / 1000)
    
    const rtf = new Intl.RelativeTimeFormat(language, { numeric: 'auto' })
    
    if (diffInSeconds < 60) {
      return rtf.format(-diffInSeconds, 'second')
    } else if (diffInSeconds < 3600) {
      return rtf.format(-Math.floor(diffInSeconds / 60), 'minute')
    } else if (diffInSeconds < 86400) {
      return rtf.format(-Math.floor(diffInSeconds / 3600), 'hour')
    } else {
      return rtf.format(-Math.floor(diffInSeconds / 86400), 'day')
    }
  },
  
  // 格式化文件大小
  fileSize: (bytes: number, language: SupportedLanguage = 'en-US') => {
    const units = ['bytes', 'KB', 'MB', 'GB', 'TB']
    let size = bytes
    let unitIndex = 0
    
    while (size >= 1024 && unitIndex < units.length - 1) {
      size /= 1024
      unitIndex++
    }
    
    const formattedSize = new Intl.NumberFormat(language, {
      maximumFractionDigits: 2
    }).format(size)
    
    return `${formattedSize} ${units[unitIndex]}`
  }
}

// 导出默认翻译函数（用于非组件环境）
export function createTranslator(language: SupportedLanguage = 'en-US') {
  return (key: TranslationKey, params?: TranslationParams) => {
    return translate(language, key, params)
  }
}

export default {
  I18nProvider,
  useI18n,
  useTranslation,
  useLanguageSelector,
  formatters,
  createTranslator,
  SUPPORTED_LANGUAGES
}
