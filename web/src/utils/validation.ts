import type {
  ProxyFormData,
  ProxyTagRequest,
  LoginCredentials,
  RegisterData,
  ProxyFilterByTags,
  LocationFilter,
  BatchImportProxiesRequest
} from '../types'

// 验证结果类型
export interface ValidationResult {
  valid: boolean
  errors: Record<string, string[]>
  warnings: Record<string, string[]>
}

// 验证规则类型
export interface ValidationRule<T = any> {
  required?: boolean
  minLength?: number
  maxLength?: number
  min?: number
  max?: number
  pattern?: RegExp
  custom?: (value: T) => string | null
  message?: string
}

// 验证器类
export class Validator {
  private errors: Record<string, string[]> = {}
  private warnings: Record<string, string[]> = {}

  /**
   * 验证字段
   */
  validateField<T>(
    fieldName: string,
    value: T,
    rules: ValidationRule<T>
  ): this {
    const fieldErrors: string[] = []
    const fieldWarnings: string[] = []

    // 必填验证
    if (rules.required && this.isEmpty(value)) {
      fieldErrors.push(rules.message || `${fieldName}是必填项`)
      this.errors[fieldName] = fieldErrors
      return this
    }

    // 如果值为空且不是必填，跳过其他验证
    if (this.isEmpty(value)) {
      return this
    }

    // 字符串长度验证
    if (typeof value === 'string') {
      if (rules.minLength && value.length < rules.minLength) {
        fieldErrors.push(`${fieldName}长度不能少于${rules.minLength}个字符`)
      }
      if (rules.maxLength && value.length > rules.maxLength) {
        fieldErrors.push(`${fieldName}长度不能超过${rules.maxLength}个字符`)
      }
      if (rules.pattern && !rules.pattern.test(value)) {
        fieldErrors.push(rules.message || `${fieldName}格式不正确`)
      }
    }

    // 数值范围验证
    if (typeof value === 'number') {
      if (rules.min !== undefined && value < rules.min) {
        fieldErrors.push(`${fieldName}不能小于${rules.min}`)
      }
      if (rules.max !== undefined && value > rules.max) {
        fieldErrors.push(`${fieldName}不能大于${rules.max}`)
      }
    }

    // 自定义验证
    if (rules.custom) {
      const customError = rules.custom(value)
      if (customError) {
        fieldErrors.push(customError)
      }
    }

    if (fieldErrors.length > 0) {
      this.errors[fieldName] = fieldErrors
    }
    if (fieldWarnings.length > 0) {
      this.warnings[fieldName] = fieldWarnings
    }

    return this
  }

  /**
   * 获取验证结果
   */
  getResult(): ValidationResult {
    return {
      valid: Object.keys(this.errors).length === 0,
      errors: { ...this.errors },
      warnings: { ...this.warnings },
    }
  }

  /**
   * 重置验证器
   */
  reset(): this {
    this.errors = {}
    this.warnings = {}
    return this
  }

  /**
   * 检查值是否为空
   */
  private isEmpty(value: any): boolean {
    return value === null || value === undefined || value === '' || 
           (Array.isArray(value) && value.length === 0)
  }
}

// 代理表单数据验证
export const validateProxyFormData = (data: ProxyFormData): ValidationResult => {
  const validator = new Validator()

  validator
    .validateField('host', data.host, {
      required: true,
      minLength: 1,
      maxLength: 255,
      pattern: /^[a-zA-Z0-9.-]+$/,
      message: '主机地址格式不正确，只能包含字母、数字、点和连字符',
    })
    .validateField('port', Number(data.port), {
      required: true,
      min: 1,
      max: 65535,
      custom: (value) => {
        if (!Number.isInteger(value)) {
          return '端口必须是整数'
        }
        return null
      },
    })
    .validateField('type', data.type, {
      required: true,
      custom: (value) => {
        if (!['http', 'https', 'socks5'].includes(value)) {
          return '代理类型必须是 http、https 或 socks5'
        }
        return null
      },
    })
    .validateField('username', data.username, {
      maxLength: 100,
    })
    .validateField('password', data.password, {
      maxLength: 100,
    })
    .validateField('weight', data.weight, {
      min: 1,
      max: 100,
    })
    .validateField('country_code', data.country_code, {
      pattern: /^[A-Z]{2}$/,
      message: '国家代码必须是2位大写字母',
    })
    .validateField('city_name', data.city_name, {
      maxLength: 100,
      pattern: /^[a-zA-Z\u4e00-\u9fa5\s-]+$/,
      message: '城市名称只能包含字母、中文、空格和连字符',
    })
    .validateField('asn_number', data.asn_number, {
      min: 1,
      max: 4294967295,
    })

  return validator.getResult()
}

// 标签请求数据验证
export const validateProxyTagRequest = (data: ProxyTagRequest): ValidationResult => {
  const validator = new Validator()

  validator
    .validateField('name', data.name, {
      required: true,
      minLength: 1,
      maxLength: 50,
      pattern: /^[a-zA-Z0-9\u4e00-\u9fa5_-]+$/,
      message: '标签名称只能包含字母、数字、中文、下划线和连字符',
    })
    .validateField('description', data.description, {
      maxLength: 200,
    })
    .validateField('color', data.color, {
      pattern: /^#[0-9A-Fa-f]{6}$/,
      message: '颜色必须是有效的十六进制格式（如 #FF0000）',
    })

  return validator.getResult()
}

// 登录凭据验证
export const validateLoginCredentials = (data: LoginCredentials): ValidationResult => {
  const validator = new Validator()

  validator
    .validateField('username', data.username, {
      required: true,
      minLength: 3,
      maxLength: 50,
      pattern: /^[a-zA-Z0-9_-]+$/,
      message: '用户名只能包含字母、数字、下划线和连字符',
    })
    .validateField('password', data.password, {
      required: true,
      minLength: 6,
      maxLength: 100,
    })

  return validator.getResult()
}

// 注册数据验证
export const validateRegisterData = (data: RegisterData): ValidationResult => {
  const validator = new Validator()

  validator
    .validateField('username', data.username, {
      required: true,
      minLength: 3,
      maxLength: 50,
      pattern: /^[a-zA-Z0-9_-]+$/,
      message: '用户名只能包含字母、数字、下划线和连字符',
    })
    .validateField('email', data.email, {
      required: true,
      maxLength: 255,
      pattern: /^[^\s@]+@[^\s@]+\.[^\s@]+$/,
      message: '邮箱格式不正确',
    })
    .validateField('password', data.password, {
      required: true,
      minLength: 8,
      maxLength: 100,
      custom: (value) => {
        // 密码强度验证
        const hasLower = /[a-z]/.test(value)
        const hasUpper = /[A-Z]/.test(value)
        const hasNumber = /\d/.test(value)
        const hasSpecial = /[!@#$%^&*(),.?":{}|<>]/.test(value)
        
        const strength = [hasLower, hasUpper, hasNumber, hasSpecial].filter(Boolean).length
        
        if (strength < 3) {
          return '密码必须包含大写字母、小写字母、数字和特殊字符中的至少3种'
        }
        return null
      },
    })
    .validateField('role', data.role, {
      custom: (value) => {
        if (value && !['admin', 'user', 'guest'].includes(value)) {
          return '角色必须是 admin、user 或 guest'
        }
        return null
      },
    })

  return validator.getResult()
}

// 代理筛选参数验证
export const validateProxyFilterByTags = (data: ProxyFilterByTags): ValidationResult => {
  const validator = new Validator()

  validator
    .validateField('tags', data.tags, {
      custom: (value) => {
        if (value && (!Array.isArray(value) || value.some(tag => typeof tag !== 'string'))) {
          return '标签必须是字符串数组'
        }
        return null
      },
    })
    .validateField('scenario', data.scenario, {
      maxLength: 100,
    })
    .validateField('strategy', data.strategy, {
      custom: (value) => {
        if (value && !['quality', 'speed', 'stability', 'priority', 'round_robin', 'random', 'least_used'].includes(value)) {
          return '策略类型无效'
        }
        return null
      },
    })

  return validator.getResult()
}

// 地理位置筛选验证
export const validateLocationFilter = (data: LocationFilter): ValidationResult => {
  const validator = new Validator()

  validator
    .validateField('countries', data.countries, {
      custom: (value) => {
        if (!Array.isArray(value)) {
          return '国家列表必须是数组'
        }
        if (value.some(country => typeof country !== 'string' || !/^[A-Z]{2}$/.test(country))) {
          return '国家代码必须是2位大写字母'
        }
        return null
      },
    })
    .validateField('cities', data.cities, {
      custom: (value) => {
        if (!Array.isArray(value)) {
          return '城市列表必须是数组'
        }
        if (value.some(city => typeof city !== 'string')) {
          return '城市名称必须是字符串'
        }
        return null
      },
    })
    .validateField('searchTerm', data.searchTerm, {
      maxLength: 100,
    })

  return validator.getResult()
}

// 批量导入代理验证
export const validateBatchImportProxiesRequest = (data: BatchImportProxiesRequest): ValidationResult => {
  const validator = new Validator()

  validator.validateField('proxies', data.proxies, {
    required: true,
    custom: (value) => {
      if (!Array.isArray(value)) {
        return '代理列表必须是数组'
      }
      if (value.length === 0) {
        return '代理列表不能为空'
      }
      if (value.length > 1000) {
        return '单次最多只能导入1000个代理'
      }
      
      // 验证每个代理数据
      for (let i = 0; i < value.length; i++) {
        const proxyValidation = validateProxyFormData(value[i])
        if (!proxyValidation.valid) {
          const errors = Object.values(proxyValidation.errors).flat()
          return `第${i + 1}个代理数据无效：${errors.join(', ')}`
        }
      }
      
      return null
    },
  })

  return validator.getResult()
}

// 通用数据类型验证
export const validateDataType = {
  isString: (value: any): value is string => typeof value === 'string',
  isNumber: (value: any): value is number => typeof value === 'number' && !isNaN(value),
  isBoolean: (value: any): value is boolean => typeof value === 'boolean',
  isArray: (value: any): value is any[] => Array.isArray(value),
  isObject: (value: any): value is object => typeof value === 'object' && value !== null && !Array.isArray(value),
  isEmail: (value: string): boolean => /^[^\s@]+@[^\s@]+\.[^\s@]+$/.test(value),
  isUrl: (value: string): boolean => {
    try {
      new URL(value)
      return true
    } catch {
      return false
    }
  },
  isIP: (value: string): boolean => {
    const ipv4Regex = /^(?:(?:25[0-5]|2[0-4][0-9]|[01]?[0-9][0-9]?)\.){3}(?:25[0-5]|2[0-4][0-9]|[01]?[0-9][0-9]?)$/
    const ipv6Regex = /^(?:[0-9a-fA-F]{1,4}:){7}[0-9a-fA-F]{1,4}$/
    return ipv4Regex.test(value) || ipv6Regex.test(value)
  },
  isPort: (value: number): boolean => Number.isInteger(value) && value >= 1 && value <= 65535,
  isUUID: (value: string): boolean => /^[0-9a-f]{8}-[0-9a-f]{4}-[1-5][0-9a-f]{3}-[89ab][0-9a-f]{3}-[0-9a-f]{12}$/i.test(value),
}

// 数据清理工具
export const sanitizeData = {
  trimString: (value: string): string => value.trim(),
  normalizeEmail: (value: string): string => value.toLowerCase().trim(),
  normalizeUsername: (value: string): string => value.toLowerCase().trim(),
  removeExtraSpaces: (value: string): string => value.replace(/\s+/g, ' ').trim(),
  sanitizeHtml: (value: string): string => {
    // 简单的HTML标签移除
    return value.replace(/<[^>]*>/g, '')
  },
  normalizePort: (value: string | number): number => {
    const port = typeof value === 'string' ? parseInt(value, 10) : value
    return isNaN(port) ? 0 : port
  },
  normalizeCountryCode: (value: string): string => value.toUpperCase().trim(),
}

// 表单数据预处理
export const preprocessFormData = <T extends Record<string, any>>(data: T): T => {
  const processed = { ...data } as any

  Object.keys(processed).forEach(key => {
    const value = processed[key]

    if (typeof value === 'string') {
      // 清理字符串
      processed[key] = sanitizeData.trimString(value)

      // 特殊字段处理
      if (key === 'email') {
        processed[key] = sanitizeData.normalizeEmail(value)
      } else if (key === 'username') {
        processed[key] = sanitizeData.normalizeUsername(value)
      } else if (key === 'country_code') {
        processed[key] = sanitizeData.normalizeCountryCode(value)
      }
    } else if (key === 'port' && (typeof value === 'string' || typeof value === 'number')) {
      processed[key] = sanitizeData.normalizePort(value)
    }
  })

  return processed as T
}
