import { ProxyService } from '../services/proxyService'
import { TagService } from '../services/tagService'
import { enhancedToast } from './toastConfig'
import { clearApiCache } from './apiWrapper'
import type { AppAction } from '../contexts/AppStateContext'
import type { Proxy, ProxyStats, ProxyTag } from '../types'

// 数据同步配置
export interface SyncConfig {
  autoSync: boolean
  syncInterval: number // 毫秒
  retryAttempts: number
  retryDelay: number // 毫秒
  enableNotifications: boolean
}

// 同步状态
export interface SyncStatus {
  isActive: boolean
  lastSync: number | null
  nextSync: number | null
  errors: string[]
  retryCount: number
}

// 数据验证结果
export interface ValidationResult {
  valid: boolean
  errors: string[]
  warnings: string[]
}

export class DataSyncManager {
  private config: SyncConfig
  private status: SyncStatus
  private dispatch: React.Dispatch<AppAction>
  private syncTimer: NodeJS.Timeout | null = null
  private retryTimer: NodeJS.Timeout | null = null

  constructor(
    config: SyncConfig,
    dispatch: React.Dispatch<AppAction>
  ) {
    this.config = config
    this.dispatch = dispatch
    this.status = {
      isActive: false,
      lastSync: null,
      nextSync: null,
      errors: [],
      retryCount: 0,
    }
  }

  /**
   * 启动数据同步
   */
  start(): void {
    if (this.status.isActive) {
      return
    }

    this.status.isActive = true
    this.scheduleNextSync()

    if (this.config.enableNotifications) {
      enhancedToast.info('数据同步已启动')
    }
  }

  /**
   * 停止数据同步
   */
  stop(): void {
    if (!this.status.isActive) {
      return
    }

    this.status.isActive = false
    this.clearTimers()

    if (this.config.enableNotifications) {
      enhancedToast.info('数据同步已停止')
    }
  }

  /**
   * 手动同步所有数据
   */
  async syncAll(): Promise<boolean> {
    try {
      this.status.errors = []
      
      // 同步代理数据
      const proxiesResult = await this.syncProxies()
      
      // 同步代理统计
      const statsResult = await this.syncProxyStats()
      
      // 同步标签数据
      const tagsResult = await this.syncTags()

      const success = proxiesResult && statsResult && tagsResult
      
      if (success) {
        this.status.lastSync = Date.now()
        this.status.retryCount = 0
        
        if (this.config.enableNotifications) {
          enhancedToast.success('数据同步完成')
        }
      } else {
        this.handleSyncError('部分数据同步失败')
      }

      return success
    } catch (error) {
      this.handleSyncError(error instanceof Error ? error.message : '数据同步失败')
      return false
    }
  }

  /**
   * 同步代理数据
   */
  async syncProxies(): Promise<boolean> {
    try {
      this.dispatch({ type: 'SET_PROXIES_LOADING', payload: true })
      
      const proxies = await ProxyService.getProxies(false) // 强制刷新，不使用缓存
      
      // 验证数据
      const validation = this.validateProxies(proxies)
      if (!validation.valid) {
        this.status.errors.push(...validation.errors)
        return false
      }

      this.dispatch({ type: 'SET_PROXIES', payload: proxies })
      return true
    } catch (error) {
      this.status.errors.push(`代理数据同步失败: ${error}`)
      return false
    } finally {
      this.dispatch({ type: 'SET_PROXIES_LOADING', payload: false })
    }
  }

  /**
   * 同步代理统计
   */
  async syncProxyStats(): Promise<boolean> {
    try {
      const stats = await ProxyService.getProxyStats()
      
      // 验证统计数据
      const validation = this.validateProxyStats(stats)
      if (!validation.valid) {
        this.status.errors.push(...validation.errors)
        return false
      }

      this.dispatch({ type: 'SET_PROXY_STATS', payload: stats })
      return true
    } catch (error) {
      this.status.errors.push(`代理统计同步失败: ${error}`)
      return false
    }
  }

  /**
   * 同步标签数据
   */
  async syncTags(): Promise<boolean> {
    try {
      this.dispatch({ type: 'SET_TAGS_LOADING', payload: true })
      
      const tags = await TagService.getTags(false) // 强制刷新，不使用缓存
      
      // 验证数据
      const validation = this.validateTags(tags)
      if (!validation.valid) {
        this.status.errors.push(...validation.errors)
        return false
      }

      this.dispatch({ type: 'SET_TAGS', payload: tags })
      return true
    } catch (error) {
      this.status.errors.push(`标签数据同步失败: ${error}`)
      return false
    } finally {
      this.dispatch({ type: 'SET_TAGS_LOADING', payload: false })
    }
  }

  /**
   * 验证代理数据
   */
  private validateProxies(proxies: Proxy[]): ValidationResult {
    const errors: string[] = []
    const warnings: string[] = []

    if (!Array.isArray(proxies)) {
      errors.push('代理数据格式错误：不是数组')
      return { valid: false, errors, warnings }
    }

    proxies.forEach((proxy, index) => {
      // 验证必需字段
      if (!proxy.id) {
        errors.push(`代理 ${index + 1}: 缺少ID`)
      }
      if (!proxy.host) {
        errors.push(`代理 ${index + 1}: 缺少主机地址`)
      }
      if (!proxy.port || proxy.port <= 0 || proxy.port > 65535) {
        errors.push(`代理 ${index + 1}: 端口号无效`)
      }
      if (!['http', 'https', 'socks5'].includes(proxy.type)) {
        errors.push(`代理 ${index + 1}: 代理类型无效`)
      }

      // 验证状态
      if (!['active', 'inactive', 'failed'].includes(proxy.status)) {
        warnings.push(`代理 ${index + 1}: 状态值可能无效`)
      }

      // 验证质量评分
      if (proxy.quality_score !== undefined && (proxy.quality_score < 0 || proxy.quality_score > 100)) {
        warnings.push(`代理 ${index + 1}: 质量评分超出范围`)
      }
    })

    return {
      valid: errors.length === 0,
      errors,
      warnings,
    }
  }

  /**
   * 验证代理统计数据
   */
  private validateProxyStats(stats: ProxyStats | null): ValidationResult {
    const errors: string[] = []
    const warnings: string[] = []

    if (!stats) {
      warnings.push('代理统计数据为空')
      return { valid: true, errors, warnings }
    }

    // 验证统计数据的一致性
    if (stats.total !== stats.active + stats.inactive + stats.failed) {
      warnings.push('代理统计数据不一致：总数与各状态数量之和不匹配')
    }

    if (stats.total < 0 || stats.active < 0 || stats.inactive < 0 || stats.failed < 0) {
      errors.push('代理统计数据包含负数')
    }

    return {
      valid: errors.length === 0,
      errors,
      warnings,
    }
  }

  /**
   * 验证标签数据
   */
  private validateTags(tags: ProxyTag[]): ValidationResult {
    const errors: string[] = []
    const warnings: string[] = []

    if (!Array.isArray(tags)) {
      errors.push('标签数据格式错误：不是数组')
      return { valid: false, errors, warnings }
    }

    const nameSet = new Set<string>()
    
    tags.forEach((tag, index) => {
      // 验证必需字段
      if (!tag.id) {
        errors.push(`标签 ${index + 1}: 缺少ID`)
      }
      if (!tag.name) {
        errors.push(`标签 ${index + 1}: 缺少名称`)
      } else {
        // 检查重复名称
        if (nameSet.has(tag.name)) {
          warnings.push(`标签 ${index + 1}: 名称重复 "${tag.name}"`)
        }
        nameSet.add(tag.name)
      }

      // 验证颜色格式
      if (tag.color && !/^#[0-9A-Fa-f]{6}$/.test(tag.color)) {
        warnings.push(`标签 ${index + 1}: 颜色格式无效`)
      }

      // 验证日期格式
      if (tag.created_at && isNaN(new Date(tag.created_at).getTime())) {
        warnings.push(`标签 ${index + 1}: 创建时间格式无效`)
      }
    })

    return {
      valid: errors.length === 0,
      errors,
      warnings,
    }
  }

  /**
   * 处理同步错误
   */
  private handleSyncError(error: string): void {
    this.status.errors.push(error)
    this.status.retryCount++

    if (this.config.enableNotifications) {
      enhancedToast.error(`数据同步失败: ${error}`)
    }

    // 如果还有重试次数，安排重试
    if (this.status.retryCount < this.config.retryAttempts) {
      this.scheduleRetry()
    } else {
      // 重试次数用完，停止自动同步
      this.stop()
      if (this.config.enableNotifications) {
        enhancedToast.error('数据同步重试次数已用完，已停止自动同步')
      }
    }
  }

  /**
   * 安排下次同步
   */
  private scheduleNextSync(): void {
    if (!this.config.autoSync || !this.status.isActive) {
      return
    }

    this.clearTimers()
    
    this.status.nextSync = Date.now() + this.config.syncInterval
    
    this.syncTimer = setTimeout(async () => {
      await this.syncAll()
      this.scheduleNextSync() // 安排下次同步
    }, this.config.syncInterval)
  }

  /**
   * 安排重试
   */
  private scheduleRetry(): void {
    this.clearRetryTimer()
    
    this.retryTimer = setTimeout(async () => {
      await this.syncAll()
    }, this.config.retryDelay)
  }

  /**
   * 清除定时器
   */
  private clearTimers(): void {
    this.clearSyncTimer()
    this.clearRetryTimer()
  }

  private clearSyncTimer(): void {
    if (this.syncTimer) {
      clearTimeout(this.syncTimer)
      this.syncTimer = null
    }
  }

  private clearRetryTimer(): void {
    if (this.retryTimer) {
      clearTimeout(this.retryTimer)
      this.retryTimer = null
    }
  }

  /**
   * 更新配置
   */
  updateConfig(newConfig: Partial<SyncConfig>): void {
    this.config = { ...this.config, ...newConfig }
    
    // 如果同步间隔改变，重新安排同步
    if (newConfig.syncInterval && this.status.isActive) {
      this.scheduleNextSync()
    }
  }

  /**
   * 获取同步状态
   */
  getStatus(): SyncStatus {
    return { ...this.status }
  }

  /**
   * 获取配置
   */
  getConfig(): SyncConfig {
    return { ...this.config }
  }

  /**
   * 清除错误
   */
  clearErrors(): void {
    this.status.errors = []
  }

  /**
   * 重置重试计数
   */
  resetRetryCount(): void {
    this.status.retryCount = 0
  }

  /**
   * 强制清除缓存并重新同步
   */
  async forceRefresh(): Promise<boolean> {
    clearApiCache()
    return await this.syncAll()
  }

  /**
   * 销毁同步管理器
   */
  destroy(): void {
    this.stop()
    this.clearTimers()
  }
}
