import React, { useState, useEffect } from 'react'
import { websocketService } from '../../services/websocketService'

const WebSocketTest: React.FC = () => {
  const [connectionState, setConnectionState] = useState('disconnected')
  const [messages, setMessages] = useState<string[]>([])

  useEffect(() => {
    // 监听连接状态变化
    const handleConnected = () => {
      setConnectionState('connected')
      setMessages(prev => [...prev, 'WebSocket connected'])
    }

    const handleDisconnected = () => {
      setConnectionState('disconnected')
      setMessages(prev => [...prev, 'WebSocket disconnected'])
    }

    const handleError = (error: any) => {
      setMessages(prev => [...prev, `WebSocket error: ${error}`])
    }

    // 添加事件监听器
    websocketService.on('connected', handleConnected)
    websocketService.on('disconnected', handleDisconnected)
    websocketService.on('error', handleError)

    // 清理函数
    return () => {
      websocketService.off('connected', handleConnected)
      websocketService.off('disconnected', handleDisconnected)
      websocketService.off('error', handleError)
    }
  }, [])

  const handleConnect = () => {
    websocketService.connect()
  }

  const handleDisconnect = () => {
    websocketService.disconnect()
  }

  const clearMessages = () => {
    setMessages([])
  }

  return (
    <div className="p-6 max-w-2xl mx-auto">
      <h2 className="text-2xl font-bold mb-4">WebSocket 测试</h2>
      
      <div className="mb-4">
        <p className="mb-2">连接状态: 
          <span className={`ml-2 px-2 py-1 rounded text-sm ${
            connectionState === 'connected' 
              ? 'bg-green-100 text-green-800' 
              : 'bg-red-100 text-red-800'
          }`}>
            {connectionState}
          </span>
        </p>
        
        <div className="space-x-2">
          <button
            onClick={handleConnect}
            className="px-4 py-2 bg-blue-500 text-white rounded hover:bg-blue-600"
            disabled={connectionState === 'connected'}
          >
            连接
          </button>
          <button
            onClick={handleDisconnect}
            className="px-4 py-2 bg-red-500 text-white rounded hover:bg-red-600"
            disabled={connectionState === 'disconnected'}
          >
            断开
          </button>
          <button
            onClick={clearMessages}
            className="px-4 py-2 bg-gray-500 text-white rounded hover:bg-gray-600"
          >
            清除消息
          </button>
        </div>
      </div>

      <div className="border rounded-lg p-4 bg-gray-50 max-h-96 overflow-y-auto">
        <h3 className="font-semibold mb-2">消息日志:</h3>
        {messages.length === 0 ? (
          <p className="text-gray-500">暂无消息</p>
        ) : (
          <div className="space-y-1">
            {messages.map((message, index) => (
              <div key={index} className="text-sm font-mono">
                <span className="text-gray-500">[{new Date().toLocaleTimeString()}]</span> {message}
              </div>
            ))}
          </div>
        )}
      </div>
    </div>
  )
}

export default WebSocketTest
