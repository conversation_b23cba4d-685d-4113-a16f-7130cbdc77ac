import React, { useState, useEffect, useMemo } from 'react'
import { Search, MapPin, Globe, X, ChevronDown } from 'lucide-react'
import type { LocationFilter, ProxyLocationStats,  } from '../types'

interface LocationFilterProps {
  filter: LocationFilter
  onFilterChange: (filter: LocationFilter) => void
  locationStats?: ProxyLocationStats
  className?: string
}

const LocationFilterComponent: React.FC<LocationFilterProps> = ({
  filter,
  onFilterChange,
  locationStats,
  className = ''
}) => {
  const [isCountryDropdownOpen, setIsCountryDropdownOpen] = useState(false)
  const [isCityDropdownOpen, setIsCityDropdownOpen] = useState(false)
  const [countrySearchTerm, setCountrySearchTerm] = useState('')
  const [citySearchTerm, setCitySearchTerm] = useState('')

  // 过滤后的国家列表
  const filteredCountries = useMemo(() => {
    if (!locationStats?.countries) return []
    return locationStats.countries.filter(country =>
      country.location.toLowerCase().includes(countrySearchTerm.toLowerCase()) ||
      country.country_code.toLowerCase().includes(countrySearchTerm.toLowerCase())
    )
  }, [locationStats?.countries, countrySearchTerm])

  // 过滤后的城市列表
  const filteredCities = useMemo(() => {
    if (!locationStats?.cities) return []
    return locationStats.cities.filter(city =>
      city.city_name.toLowerCase().includes(citySearchTerm.toLowerCase()) ||
      city.country_code.toLowerCase().includes(citySearchTerm.toLowerCase())
    )
  }, [locationStats?.cities, citySearchTerm])

  // 处理国家选择
  const handleCountryToggle = (countryCode: string) => {
    const newCountries = filter.countries.includes(countryCode)
      ? filter.countries.filter(c => c !== countryCode)
      : [...filter.countries, countryCode]
    
    onFilterChange({
      ...filter,
      countries: newCountries
    })
  }

  // 处理城市选择
  const handleCityToggle = (cityKey: string) => {
    const newCities = filter.cities.includes(cityKey)
      ? filter.cities.filter(c => c !== cityKey)
      : [...filter.cities, cityKey]
    
    onFilterChange({
      ...filter,
      cities: newCities
    })
  }

  // 清除所有筛选
  const clearAllFilters = () => {
    onFilterChange({
      countries: [],
      cities: [],
      searchTerm: ''
    })
  }

  // 清除国家筛选
  const clearCountryFilters = () => {
    onFilterChange({
      ...filter,
      countries: []
    })
  }

  // 清除城市筛选
  const clearCityFilters = () => {
    onFilterChange({
      ...filter,
      cities: []
    })
  }

  // 点击外部关闭下拉框
  useEffect(() => {
    const handleClickOutside = (event: MouseEvent) => {
      const target = event.target as Element
      if (!target.closest('.location-filter-dropdown')) {
        setIsCountryDropdownOpen(false)
        setIsCityDropdownOpen(false)
      }
    }

    document.addEventListener('mousedown', handleClickOutside)
    return () => document.removeEventListener('mousedown', handleClickOutside)
  }, [])

  const hasActiveFilters = filter.countries.length > 0 || filter.cities.length > 0 || filter.searchTerm

  return (
    <div className={`space-y-4 ${className}`} style={{ position: 'relative', zIndex: 1, overflow:'visible' }}>
      {/* 搜索框 */}
      <div className="relative">
        <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-gray-400" />
        <input
          type="text"
          placeholder="搜索代理地址、国家或城市..."
          value={filter.searchTerm}
          onChange={(e) => onFilterChange({ ...filter, searchTerm: e.target.value })}
          className="w-full pl-10 pr-4 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent"
        />
      </div>

      {/* 筛选器 */}
      <div className="flex flex-wrap gap-3">
        {/* 国家筛选 */}
        <div className="relative location-filter-dropdown" style={{ zIndex: isCountryDropdownOpen ? 1000 : 1 }}>
          <button
            onClick={() => setIsCountryDropdownOpen(!isCountryDropdownOpen)}
            className={`flex items-center gap-2 px-3 py-2 border rounded-lg text-sm font-medium transition-colors ${
              filter.countries.length > 0
                ? 'bg-blue-50 border-blue-200 text-blue-700'
                : 'bg-white border-gray-300 text-gray-700 hover:bg-gray-50'
            }`}
          >
            <Globe className="h-4 w-4" />
            <span>
              国家 {filter.countries.length > 0 && `(${filter.countries.length})`}
            </span>
            <ChevronDown className={`h-4 w-4 transition-transform ${isCountryDropdownOpen ? 'rotate-180' : ''}`} />
          </button>

          {isCountryDropdownOpen && (
            <div className="absolute top-full left-0 mt-1 w-64 bg-white border border-gray-200 rounded-lg shadow-xl z-[9999]" style={{ boxShadow: '0 20px 25px -5px rgba(0, 0, 0, 0.1), 0 10px 10px -5px rgba(0, 0, 0, 0.04)' }}>
              <div className="p-3 border-b border-gray-200">
                <div className="relative">
                  <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-gray-400" />
                  <input
                    type="text"
                    placeholder="搜索国家..."
                    value={countrySearchTerm}
                    onChange={(e) => setCountrySearchTerm(e.target.value)}
                    className="w-full pl-10 pr-4 py-2 text-sm border border-gray-300 rounded focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                  />
                </div>
              </div>
              <div className="max-h-48 overflow-y-auto">
                {filteredCountries.map((country) => (
                  <label
                    key={country.country_code}
                    className="flex items-center gap-3 px-3 py-2 hover:bg-gray-50 cursor-pointer"
                  >
                    <input
                      type="checkbox"
                      checked={filter.countries.includes(country.country_code)}
                      onChange={() => handleCountryToggle(country.country_code)}
                      className="rounded border-gray-300 text-blue-600 focus:ring-blue-500"
                    />
                    <div className="flex-1 min-w-0">
                      <div className="text-sm font-medium text-gray-900">
                        {country.country_code}
                      </div>
                      <div className="text-xs text-gray-500">
                        {country.total} 个代理 · {country.active} 活跃
                      </div>
                    </div>
                  </label>
                ))}
                {filteredCountries.length === 0 && (
                  <div className="px-3 py-4 text-sm text-gray-500 text-center">
                    没有找到匹配的国家
                  </div>
                )}
              </div>
              {filter.countries.length > 0 && (
                <div className="p-3 border-t border-gray-200">
                  <button
                    onClick={clearCountryFilters}
                    className="text-sm text-blue-600 hover:text-blue-800"
                  >
                    清除国家筛选
                  </button>
                </div>
              )}
            </div>
          )}
        </div>

        {/* 城市筛选 */}
        <div className="relative location-filter-dropdown" style={{ zIndex: isCityDropdownOpen ? 1000 : 1 }}>
          <button
            onClick={() => setIsCityDropdownOpen(!isCityDropdownOpen)}
            className={`flex items-center gap-2 px-3 py-2 border rounded-lg text-sm font-medium transition-colors ${
              filter.cities.length > 0
                ? 'bg-green-50 border-green-200 text-green-700'
                : 'bg-white border-gray-300 text-gray-700 hover:bg-gray-50'
            }`}
          >
            <MapPin className="h-4 w-4" />
            <span>
              城市 {filter.cities.length > 0 && `(${filter.cities.length})`}
            </span>
            <ChevronDown className={`h-4 w-4 transition-transform ${isCityDropdownOpen ? 'rotate-180' : ''}`} />
          </button>

          {isCityDropdownOpen && (
            <div className="absolute top-full left-0 mt-1 w-64 bg-white border border-gray-200 rounded-lg shadow-xl z-[9999]" style={{ boxShadow: '0 20px 25px -5px rgba(0, 0, 0, 0.1), 0 10px 10px -5px rgba(0, 0, 0, 0.04)' }}>
              <div className="p-3 border-b border-gray-200">
                <div className="relative">
                  <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-gray-400" />
                  <input
                    type="text"
                    placeholder="搜索城市..."
                    value={citySearchTerm}
                    onChange={(e) => setCitySearchTerm(e.target.value)}
                    className="w-full pl-10 pr-4 py-2 text-sm border border-gray-300 rounded focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                  />
                </div>
              </div>
              <div className="max-h-48 overflow-y-auto">
                {filteredCities.map((city) => {
                  const cityKey = `${city.country_code}_${city.city_name}`
                  return (
                    <label
                      key={cityKey}
                      className="flex items-center gap-3 px-3 py-2 hover:bg-gray-50 cursor-pointer"
                    >
                      <input
                        type="checkbox"
                        checked={filter.cities.includes(cityKey)}
                        onChange={() => handleCityToggle(cityKey)}
                        className="rounded border-gray-300 text-blue-600 focus:ring-blue-500"
                      />
                      <div className="flex-1 min-w-0">
                        <div className="text-sm font-medium text-gray-900">
                          {city.city_name}
                        </div>
                        <div className="text-xs text-gray-500">
                          {city.country_code} · {city.total} 个代理 · {city.active} 活跃
                        </div>
                      </div>
                    </label>
                  )
                })}
                {filteredCities.length === 0 && (
                  <div className="px-3 py-4 text-sm text-gray-500 text-center">
                    没有找到匹配的城市
                  </div>
                )}
              </div>
              {filter.cities.length > 0 && (
                <div className="p-3 border-t border-gray-200">
                  <button
                    onClick={clearCityFilters}
                    className="text-sm text-blue-600 hover:text-blue-800"
                  >
                    清除城市筛选
                  </button>
                </div>
              )}
            </div>
          )}
        </div>

        {/* 清除所有筛选 */}
        {hasActiveFilters && (
          <button
            onClick={clearAllFilters}
            className="flex items-center gap-2 px-3 py-2 text-sm text-gray-600 hover:text-gray-800 border border-gray-300 rounded-lg hover:bg-gray-50"
          >
            <X className="h-4 w-4" />
            清除筛选
          </button>
        )}
      </div>

      {/* 已选择的筛选标签 */}
      {hasActiveFilters && (
        <div className="flex flex-wrap gap-2">
          {filter.countries.map((countryCode) => {
            const country = locationStats?.countries.find(c => c.country_code === countryCode)
            return (
              <span
                key={countryCode}
                className="inline-flex items-center gap-1 px-2 py-1 bg-blue-100 text-blue-800 text-xs rounded-full"
              >
                <Globe className="h-3 w-3" />
                {country?.country_code || countryCode}
                <button
                  onClick={() => handleCountryToggle(countryCode)}
                  className="ml-1 hover:bg-blue-200 rounded-full p-0.5"
                >
                  <X className="h-3 w-3" />
                </button>
              </span>
            )
          })}
          {filter.cities.map((cityKey) => {
            const city = locationStats?.cities.find(c => `${c.country_code}_${c.city_name}` === cityKey)
            return (
              <span
                key={cityKey}
                className="inline-flex items-center gap-1 px-2 py-1 bg-green-100 text-green-800 text-xs rounded-full"
              >
                <MapPin className="h-3 w-3" />
                {city?.city_name || cityKey.split('_')[1]} ({city?.country_code || cityKey.split('_')[0]})
                <button
                  onClick={() => handleCityToggle(cityKey)}
                  className="ml-1 hover:bg-green-200 rounded-full p-0.5"
                >
                  <X className="h-3 w-3" />
                </button>
              </span>
            )
          })}
        </div>
      )}
    </div>
  )
}

export default LocationFilterComponent
