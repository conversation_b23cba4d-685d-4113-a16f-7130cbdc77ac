import React from 'react'
import Modal from '../common/Modal'
import StatusBadge from '../common/StatusBadge'
import { formatDate } from '../../utils/helpers'
import { 
  Clock, 
  Globe, 
  Settings, 
  AlertCircle, 
  CheckCircle,
  Copy
} from 'lucide-react'
import { copyToClipboard } from '../../utils/helpers'
import toast from 'react-hot-toast'
import type { Task } from '../../types'

interface TaskDetailModalProps {
  task: Task | null
  isOpen: boolean
  onClose: () => void
}

const TaskDetailModal: React.FC<TaskDetailModalProps> = ({ task, isOpen, onClose }) => {
  if (!task) return null

  const handleCopyUrl = async (): Promise<void> => {
    const success = await copyToClipboard(task.url)
    if (success) {
      toast.success('URL已复制到剪贴板')
    } else {
      toast.error('复制失败')
    }
  }

  const handleCopyResult = async (): Promise<void> => {
    if (task.result) {
      const success = await copyToClipboard(task.result)
      if (success) {
        toast.success('结果已复制到剪贴板')
      } else {
        toast.error('复制失败')
      }
    }
  }

  const getStatusIcon = () => {
    switch (task.status) {
      case 'completed':
        return <CheckCircle className="h-5 w-5 text-green-500" />
      case 'failed':
        return <AlertCircle className="h-5 w-5 text-red-500" />
      case 'running':
        return <Clock className="h-5 w-5 text-blue-500 animate-spin" />
      default:
        return <Clock className="h-5 w-5 text-gray-500" />
    }
  }

  return (
    <Modal
      isOpen={isOpen}
      onClose={onClose}
      title="任务详情"
      size="lg"
    >
      <div className="space-y-6">
        {/* 基本信息 */}
        <div className="bg-gray-50 rounded-lg p-4">
          <div className="flex items-center justify-between mb-4">
            <h3 className="text-lg font-medium text-gray-900 flex items-center">
              {getStatusIcon()}
              <span className="ml-2">{task.name}</span>
            </h3>
            <StatusBadge status={task.status} type="task" />
          </div>
          
          <div className="grid grid-cols-1 md:grid-cols-2 gap-4 text-sm">
            <div>
              <span className="font-medium text-gray-700">任务ID:</span>
              <span className="ml-2 text-gray-600">{task.id}</span>
            </div>
            <div>
              <span className="font-medium text-gray-700">优先级:</span>
              <span className="ml-2 text-gray-600">{task.priority}</span>
            </div>
            <div>
              <span className="font-medium text-gray-700">请求方法:</span>
              <span className="ml-2 text-gray-600">{task.method}</span>
            </div>
            <div>
              <span className="font-medium text-gray-700">代理策略:</span>
              <span className="ml-2 text-gray-600">{task.proxy_strategy}</span>
            </div>
          </div>
        </div>

        {/* URL信息 */}
        <div>
          <h4 className="text-md font-medium text-gray-900 mb-2 flex items-center">
            <Globe className="h-4 w-4 mr-2" />
            请求URL
          </h4>
          <div className="bg-gray-50 rounded-lg p-3 flex items-center justify-between">
            <code className="text-sm text-gray-800 break-all flex-1">{task.url}</code>
            <button
              onClick={handleCopyUrl}
              className="ml-2 p-1 text-gray-500 hover:text-gray-700"
              title="复制URL"
            >
              <Copy className="h-4 w-4" />
            </button>
          </div>
        </div>

        {/* 请求配置 */}
        <div>
          <h4 className="text-md font-medium text-gray-900 mb-2 flex items-center">
            <Settings className="h-4 w-4 mr-2" />
            请求配置
          </h4>
          <div className="bg-gray-50 rounded-lg p-3 space-y-2 text-sm">
            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
              <div>
                <span className="font-medium text-gray-700">最大重试:</span>
                <span className="ml-2 text-gray-600">{task.max_retries || 0} 次</span>
              </div>
              <div>
                <span className="font-medium text-gray-700">超时时间:</span>
                <span className="ml-2 text-gray-600">{task.timeout || 30} 秒</span>
              </div>
              <div>
                <span className="font-medium text-gray-700">重试次数:</span>
                <span className="ml-2 text-gray-600">{task.retry_count || 0} 次</span>
              </div>
            </div>
            
            {task.headers && Object.keys(task.headers).length > 0 && (
              <div>
                <span className="font-medium text-gray-700">请求头:</span>
                <pre className="mt-1 text-xs bg-white p-2 rounded border overflow-x-auto">
                  {JSON.stringify(task.headers, null, 2)}
                </pre>
              </div>
            )}
            
            {task.body && (
              <div>
                <span className="font-medium text-gray-700">请求体:</span>
                <pre className="mt-1 text-xs bg-white p-2 rounded border overflow-x-auto">
                  {task.body}
                </pre>
              </div>
            )}
          </div>
        </div>

        {/* 执行结果 */}
        {(task.result || task.error) && (
          <div>
            <h4 className="text-md font-medium text-gray-900 mb-2 flex items-center">
              {task.status === 'completed' ? (
                <CheckCircle className="h-4 w-4 mr-2 text-green-500" />
              ) : (
                <AlertCircle className="h-4 w-4 mr-2 text-red-500" />
              )}
              执行结果
            </h4>
            <div className="bg-gray-50 rounded-lg p-3">
              {task.result && (
                <div className="mb-3">
                  <div className="flex items-center justify-between mb-2">
                    <span className="font-medium text-gray-700">响应结果:</span>
                    <button
                      onClick={handleCopyResult}
                      className="p-1 text-gray-500 hover:text-gray-700"
                      title="复制结果"
                    >
                      <Copy className="h-4 w-4" />
                    </button>
                  </div>
                  <pre className="text-xs bg-white p-2 rounded border overflow-x-auto max-h-40">
                    {task.result}
                  </pre>
                </div>
              )}
              
              {task.error && (
                <div>
                  <span className="font-medium text-red-700">错误信息:</span>
                  <pre className="mt-1 text-xs bg-red-50 text-red-800 p-2 rounded border overflow-x-auto">
                    {task.error}
                  </pre>
                </div>
              )}
            </div>
          </div>
        )}

        {/* 时间信息 */}
        <div>
          <h4 className="text-md font-medium text-gray-900 mb-2 flex items-center">
            <Clock className="h-4 w-4 mr-2" />
            时间信息
          </h4>
          <div className="bg-gray-50 rounded-lg p-3 grid grid-cols-1 md:grid-cols-2 gap-4 text-sm">
            <div>
              <span className="font-medium text-gray-700">创建时间:</span>
              <span className="ml-2 text-gray-600">{formatDate(task.created_at)}</span>
            </div>
            {task.started_at && (
              <div>
                <span className="font-medium text-gray-700">开始时间:</span>
                <span className="ml-2 text-gray-600">{formatDate(task.started_at)}</span>
              </div>
            )}
            {task.completed_at && (
              <div>
                <span className="font-medium text-gray-700">完成时间:</span>
                <span className="ml-2 text-gray-600">{formatDate(task.completed_at)}</span>
              </div>
            )}
            <div>
              <span className="font-medium text-gray-700">更新时间:</span>
              <span className="ml-2 text-gray-600">{formatDate(task.updated_at)}</span>
            </div>
          </div>
        </div>
      </div>
    </Modal>
  )
}

export default TaskDetailModal
