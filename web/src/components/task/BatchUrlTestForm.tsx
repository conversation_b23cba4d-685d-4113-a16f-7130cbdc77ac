import React, { useState, useRef } from 'react'
import { 
  Upload, 
  Download, 
  Plus, 
  X, 
  FileText, 
  AlertCircle,
  CheckCircle,
  Globe,
  Settings,
  Play,
  Layers
} from 'lucide-react'
import { taskAPI } from '../../services/api'
import { 
  HTTP_METHOD_OPTIONS, 
  PROXY_STRATEGY_OPTIONS, 
  TASK_PRIORITY_OPTIONS 
} from '../../utils/constants'
import { validateUrl } from '../../utils/helpers'
import toast from 'react-hot-toast'
import type { HttpMethod, ProxyStrategy, TaskPriority } from '../../types'

interface BatchUrlTestFormProps {
  onSuccess: () => void
  onCancel: () => void
  initialData?: any
  isEditing?: boolean
}

interface UrlItem {
  id: string
  url: string
  name?: string
  valid: boolean
  error?: string
}

interface BatchFormData {
  name: string
  urls: UrlItem[]
  method: HttpMethod
  headers: string
  body: string
  proxy_strategy: ProxyStrategy
  priority: TaskPriority
  max_retries: number
  timeout: number
  execution_mode: 'sequential' | 'parallel'
  batch_size: number
  delay_between_batches: number
}

const BatchUrlTestForm: React.FC<BatchUrlTestFormProps> = ({
  onSuccess,
  onCancel,
  initialData,
  isEditing = false
}) => {
  const fileInputRef = useRef<HTMLInputElement>(null)
  
  const [formData, setFormData] = useState<BatchFormData>({
    name: '',
    urls: [],
    method: 'GET',
    headers: '{}',
    body: '',
    proxy_strategy: 'round_robin',
    priority: 2,
    max_retries: 3,
    timeout: 30,
    execution_mode: 'parallel',
    batch_size: 10,
    delay_between_batches: 1000
  })
  
  const [loading, setLoading] = useState<boolean>(false)
  const [urlInput, setUrlInput] = useState<string>('')
  const [nameInput, setNameInput] = useState<string>('')
  const [showAdvanced, setShowAdvanced] = useState<boolean>(false)

  // 生成唯一ID
  const generateId = () => Math.random().toString(36).substr(2, 9)

  // 验证URL
  const validateUrlItem = (url: string): { valid: boolean; error?: string } => {
    if (!url.trim()) {
      return { valid: false, error: 'URL不能为空' }
    }
    
    if (!validateUrl(url)) {
      return { valid: false, error: 'URL格式无效' }
    }
    
    return { valid: true }
  }

  // 添加单个URL
  const addUrl = () => {
    if (!urlInput.trim()) return
    
    const validation = validateUrlItem(urlInput)
    const newUrl: UrlItem = {
      id: generateId(),
      url: urlInput.trim(),
      name: nameInput.trim() || undefined,
      valid: validation.valid,
      error: validation.error
    }
    
    setFormData(prev => ({
      ...prev,
      urls: [...prev.urls, newUrl]
    }))
    
    setUrlInput('')
    setNameInput('')
  }

  // 删除URL
  const removeUrl = (id: string) => {
    setFormData(prev => ({
      ...prev,
      urls: prev.urls.filter(url => url.id !== id)
    }))
  }

  // 批量添加URL（从文本）
  const addUrlsFromText = (text: string) => {
    const lines = text.split('\n').filter(line => line.trim())
    const newUrls: UrlItem[] = []
    
    lines.forEach(line => {
      const trimmedLine = line.trim()
      if (trimmedLine) {
        const validation = validateUrlItem(trimmedLine)
        newUrls.push({
          id: generateId(),
          url: trimmedLine,
          valid: validation.valid,
          error: validation.error
        })
      }
    })
    
    setFormData(prev => ({
      ...prev,
      urls: [...prev.urls, ...newUrls]
    }))
  }

  // 从文件导入URL
  const handleFileImport = (event: React.ChangeEvent<HTMLInputElement>) => {
    const file = event.target.files?.[0]
    if (!file) return
    
    const reader = new FileReader()
    reader.onload = (e) => {
      const text = e.target?.result as string
      addUrlsFromText(text)
      toast.success(`已导入 ${text.split('\n').filter(line => line.trim()).length} 个URL`)
    }
    reader.readAsText(file)
    
    // 重置文件输入
    if (fileInputRef.current) {
      fileInputRef.current.value = ''
    }
  }

  // 导出URL列表
  const exportUrls = () => {
    const urlText = formData.urls.map(item => 
      item.name ? `${item.url} # ${item.name}` : item.url
    ).join('\n')
    
    const blob = new Blob([urlText], { type: 'text/plain' })
    const url = URL.createObjectURL(blob)
    const a = document.createElement('a')
    a.href = url
    a.download = 'urls.txt'
    a.click()
    URL.revokeObjectURL(url)
  }

  // 验证所有URL
  const validateAllUrls = () => {
    setFormData(prev => ({
      ...prev,
      urls: prev.urls.map(url => {
        const validation = validateUrlItem(url.url)
        return {
          ...url,
          valid: validation.valid,
          error: validation.error
        }
      })
    }))
  }

  // 表单提交
  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault()
    
    if (!formData.name.trim()) {
      toast.error('请输入任务名称')
      return
    }
    
    if (formData.urls.length === 0) {
      toast.error('请至少添加一个URL')
      return
    }
    
    const invalidUrls = formData.urls.filter(url => !url.valid)
    if (invalidUrls.length > 0) {
      toast.error(`有 ${invalidUrls.length} 个无效URL，请检查后重试`)
      return
    }
    
    try {
      setLoading(true)
      
      // 构建任务数据
      const taskData = {
        name: formData.name,
        type: 'batch_url',
        urls: formData.urls.map(url => ({
          url: url.url,
          name: url.name
        })),
        method: formData.method,
        headers: formData.headers ? JSON.parse(formData.headers) : {},
        body: formData.body || undefined,
        proxy_strategy: formData.proxy_strategy,
        priority: formData.priority,
        max_retries: formData.max_retries,
        timeout: formData.timeout,
        execution_mode: formData.execution_mode,
        batch_size: formData.batch_size,
        delay_between_batches: formData.delay_between_batches
      }
      
      // 调用批量任务API
      await taskAPI.createBatchUrlTask(taskData)

      toast.success('批量URL测试任务创建成功')
      onSuccess()
    } catch (error) {
      toast.error('创建任务失败')
    } finally {
      setLoading(false)
    }
  }

  const validUrls = formData.urls.filter(url => url.valid)
  const invalidUrls = formData.urls.filter(url => !url.valid)

  return (
    <form onSubmit={handleSubmit} className="space-y-6">
      {/* 任务基本信息 */}
      <div className="space-y-4">
        <div>
          <label className="block text-sm font-medium text-gray-700 mb-2">
            任务名称 *
          </label>
          <input
            type="text"
            required
            className="input w-full"
            placeholder="请输入批量URL测试任务名称"
            value={formData.name}
            onChange={(e) => setFormData(prev => ({ ...prev, name: e.target.value }))}
          />
        </div>
      </div>

      {/* URL管理区域 */}
      <div className="space-y-4">
        <div className="flex items-center justify-between">
          <h3 className="text-lg font-medium text-gray-900 flex items-center">
            <Globe className="h-5 w-5 mr-2" />
            URL列表管理
          </h3>
          <div className="flex items-center space-x-2">
            <span className="text-sm text-gray-500">
              {validUrls.length} 有效 / {formData.urls.length} 总计
            </span>
          </div>
        </div>

        {/* URL输入区域 */}
        <div className="bg-gray-50 rounded-lg p-4 space-y-3">
          <div className="grid grid-cols-1 md:grid-cols-3 gap-3">
            <div className="md:col-span-2">
              <input
                type="url"
                className="input w-full"
                placeholder="输入URL地址"
                value={urlInput}
                onChange={(e) => setUrlInput(e.target.value)}
                onKeyPress={(e) => e.key === 'Enter' && (e.preventDefault(), addUrl())}
              />
            </div>
            <div>
              <input
                type="text"
                className="input w-full"
                placeholder="URL名称（可选）"
                value={nameInput}
                onChange={(e) => setNameInput(e.target.value)}
                onKeyPress={(e) => e.key === 'Enter' && (e.preventDefault(), addUrl())}
              />
            </div>
          </div>
          
          <div className="flex items-center justify-between">
            <button
              type="button"
              onClick={addUrl}
              disabled={!urlInput.trim()}
              className="btn-primary"
            >
              <Plus className="h-4 w-4 mr-2" />
              添加URL
            </button>
            
            <div className="flex items-center space-x-2">
              <input
                ref={fileInputRef}
                type="file"
                accept=".txt,.csv"
                onChange={handleFileImport}
                className="hidden"
              />
              <button
                type="button"
                onClick={() => fileInputRef.current?.click()}
                className="btn-outline text-sm"
              >
                <Upload className="h-4 w-4 mr-1" />
                导入文件
              </button>
              
              {formData.urls.length > 0 && (
                <>
                  <button
                    type="button"
                    onClick={exportUrls}
                    className="btn-outline text-sm"
                  >
                    <Download className="h-4 w-4 mr-1" />
                    导出列表
                  </button>
                  
                  <button
                    type="button"
                    onClick={validateAllUrls}
                    className="btn-outline text-sm"
                  >
                    <CheckCircle className="h-4 w-4 mr-1" />
                    验证全部
                  </button>
                </>
              )}
            </div>
          </div>
        </div>

        {/* URL列表显示 */}
        {formData.urls.length > 0 && (
          <div className="border border-gray-200 rounded-lg">
            <div className="px-4 py-3 bg-gray-50 border-b border-gray-200">
              <div className="flex items-center justify-between">
                <h4 className="font-medium text-gray-900">URL列表</h4>
                <div className="flex items-center space-x-4 text-sm">
                  {validUrls.length > 0 && (
                    <span className="text-green-600 flex items-center">
                      <CheckCircle className="h-4 w-4 mr-1" />
                      {validUrls.length} 有效
                    </span>
                  )}
                  {invalidUrls.length > 0 && (
                    <span className="text-red-600 flex items-center">
                      <AlertCircle className="h-4 w-4 mr-1" />
                      {invalidUrls.length} 无效
                    </span>
                  )}
                </div>
              </div>
            </div>
            
            <div className="max-h-60 overflow-y-auto">
              {formData.urls.map((url, index) => (
                <div
                  key={url.id}
                  className={`px-4 py-3 border-b border-gray-100 last:border-b-0 ${
                    !url.valid ? 'bg-red-50' : ''
                  }`}
                >
                  <div className="flex items-center justify-between">
                    <div className="flex-1 min-w-0">
                      <div className="flex items-center space-x-2">
                        <span className="text-sm text-gray-500">#{index + 1}</span>
                        {url.valid ? (
                          <CheckCircle className="h-4 w-4 text-green-500" />
                        ) : (
                          <AlertCircle className="h-4 w-4 text-red-500" />
                        )}
                        <div className="flex-1 min-w-0">
                          <p className="text-sm font-medium text-gray-900 truncate">
                            {url.name || url.url}
                          </p>
                          {url.name && (
                            <p className="text-xs text-gray-500 truncate">{url.url}</p>
                          )}
                          {url.error && (
                            <p className="text-xs text-red-600">{url.error}</p>
                          )}
                        </div>
                      </div>
                    </div>
                    
                    <button
                      type="button"
                      onClick={() => removeUrl(url.id)}
                      className="ml-2 text-gray-400 hover:text-red-600"
                    >
                      <X className="h-4 w-4" />
                    </button>
                  </div>
                </div>
              ))}
            </div>
          </div>
        )}
      </div>

      {/* 执行配置 */}
      <div className="space-y-4">
        <div className="flex items-center justify-between">
          <h3 className="text-lg font-medium text-gray-900 flex items-center">
            <Settings className="h-5 w-5 mr-2" />
            执行配置
          </h3>
          <button
            type="button"
            onClick={() => setShowAdvanced(!showAdvanced)}
            className="text-sm text-blue-600 hover:text-blue-800"
          >
            {showAdvanced ? '隐藏高级选项' : '显示高级选项'}
          </button>
        </div>

        <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
          <div>
            <label className="block text-sm font-medium text-gray-700 mb-2">
              HTTP方法
            </label>
            <select
              className="input w-full"
              value={formData.method}
              onChange={(e) => setFormData(prev => ({ ...prev, method: e.target.value as HttpMethod }))}
            >
              {HTTP_METHOD_OPTIONS.map(option => (
                <option key={option.value} value={option.value}>
                  {option.label}
                </option>
              ))}
            </select>
          </div>

          <div>
            <label className="block text-sm font-medium text-gray-700 mb-2">
              代理策略
            </label>
            <select
              className="input w-full"
              value={formData.proxy_strategy}
              onChange={(e) => setFormData(prev => ({ ...prev, proxy_strategy: e.target.value as ProxyStrategy }))}
            >
              {PROXY_STRATEGY_OPTIONS.map(option => (
                <option key={option.value} value={option.value}>
                  {option.label}
                </option>
              ))}
            </select>
          </div>

          <div>
            <label className="block text-sm font-medium text-gray-700 mb-2">
              执行模式
            </label>
            <select
              className="input w-full"
              value={formData.execution_mode}
              onChange={(e) => setFormData(prev => ({ ...prev, execution_mode: e.target.value as 'sequential' | 'parallel' }))}
            >
              <option value="parallel">并行执行（推荐）</option>
              <option value="sequential">串行执行</option>
            </select>
            <p className="text-xs text-gray-500 mt-1">
              并行执行速度更快，串行执行对服务器压力更小
            </p>
          </div>

          <div>
            <label className="block text-sm font-medium text-gray-700 mb-2">
              任务优先级
            </label>
            <select
              className="input w-full"
              value={formData.priority}
              onChange={(e) => setFormData(prev => ({ ...prev, priority: parseInt(e.target.value) as TaskPriority }))}
            >
              {TASK_PRIORITY_OPTIONS.map(option => (
                <option key={option.value} value={option.value}>
                  {option.label}
                </option>
              ))}
            </select>
          </div>
        </div>

        {/* 高级配置 */}
        {showAdvanced && (
          <div className="bg-gray-50 rounded-lg p-4 space-y-4">
            <h4 className="font-medium text-gray-900">高级配置</h4>

            <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-2">
                  批次大小
                </label>
                <input
                  type="number"
                  min="1"
                  max="100"
                  className="input w-full"
                  value={formData.batch_size}
                  onChange={(e) => setFormData(prev => ({ ...prev, batch_size: parseInt(e.target.value) || 10 }))}
                />
                <p className="text-xs text-gray-500 mt-1">
                  每批次处理的URL数量
                </p>
              </div>

              <div>
                <label className="block text-sm font-medium text-gray-700 mb-2">
                  批次间延迟 (ms)
                </label>
                <input
                  type="number"
                  min="0"
                  max="10000"
                  step="100"
                  className="input w-full"
                  value={formData.delay_between_batches}
                  onChange={(e) => setFormData(prev => ({ ...prev, delay_between_batches: parseInt(e.target.value) || 1000 }))}
                />
                <p className="text-xs text-gray-500 mt-1">
                  批次之间的等待时间
                </p>
              </div>

              <div>
                <label className="block text-sm font-medium text-gray-700 mb-2">
                  超时时间 (秒)
                </label>
                <input
                  type="number"
                  min="5"
                  max="300"
                  className="input w-full"
                  value={formData.timeout}
                  onChange={(e) => setFormData(prev => ({ ...prev, timeout: parseInt(e.target.value) || 30 }))}
                />
              </div>

              <div>
                <label className="block text-sm font-medium text-gray-700 mb-2">
                  最大重试次数
                </label>
                <input
                  type="number"
                  min="0"
                  max="10"
                  className="input w-full"
                  value={formData.max_retries}
                  onChange={(e) => setFormData(prev => ({ ...prev, max_retries: parseInt(e.target.value) || 3 }))}
                />
              </div>
            </div>

            {/* 请求头配置 */}
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-2">
                请求头 (JSON格式)
              </label>
              <textarea
                rows={3}
                className="input w-full font-mono text-sm"
                placeholder='{"User-Agent": "Mozilla/5.0...", "Accept": "application/json"}'
                value={formData.headers}
                onChange={(e) => setFormData(prev => ({ ...prev, headers: e.target.value }))}
              />
            </div>

            {/* 请求体配置 */}
            {(formData.method === 'POST' || formData.method === 'PUT' || formData.method === 'PATCH') && (
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-2">
                  请求体
                </label>
                <textarea
                  rows={4}
                  className="input w-full font-mono text-sm"
                  placeholder="请求体内容"
                  value={formData.body}
                  onChange={(e) => setFormData(prev => ({ ...prev, body: e.target.value }))}
                />
              </div>
            )}
          </div>
        )}
      </div>

      {/* 任务预览 */}
      {formData.urls.length > 0 && (
        <div className="bg-blue-50 rounded-lg p-4">
          <h4 className="font-medium text-blue-900 mb-2 flex items-center">
            <Play className="h-4 w-4 mr-2" />
            任务预览
          </h4>
          <div className="text-sm text-blue-800 space-y-1">
            <p>• 将测试 <strong>{validUrls.length}</strong> 个有效URL</p>
            <p>• 使用 <strong>{formData.method}</strong> 方法和 <strong>{PROXY_STRATEGY_OPTIONS.find(opt => opt.value === formData.proxy_strategy)?.label}</strong> 代理策略</p>
            <p>• 采用 <strong>{formData.execution_mode === 'parallel' ? '并行' : '串行'}</strong> 执行模式</p>
            {formData.execution_mode === 'parallel' && (
              <p>• 每批次处理 <strong>{formData.batch_size}</strong> 个URL，批次间延迟 <strong>{formData.delay_between_batches}ms</strong></p>
            )}
            <p>• 预计执行时间：<strong>{Math.ceil(validUrls.length / formData.batch_size) * (formData.timeout + formData.delay_between_batches / 1000)} 秒</strong></p>
          </div>
        </div>
      )}

      {/* 表单操作按钮 */}
      <div className="flex justify-end space-x-3 pt-4 border-t border-gray-200">
        <button
          type="button"
          onClick={onCancel}
          className="btn-outline"
        >
          取消
        </button>
        <button
          type="submit"
          disabled={loading || formData.urls.length === 0 || validUrls.length === 0}
          className="btn-primary"
        >
          {loading ? (
            <>
              <Layers className="h-4 w-4 mr-2 animate-pulse" />
              创建中...
            </>
          ) : (
            <>
              <Play className="h-4 w-4 mr-2" />
              创建批量测试任务
            </>
          )}
        </button>
      </div>
    </form>
  )
}

export default BatchUrlTestForm
