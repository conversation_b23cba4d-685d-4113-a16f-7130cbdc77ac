import React, { useState, useEffect } from 'react'
import { 
  <PERSON><PERSON><PERSON>, 
  Save, 
  Co<PERSON>, 
  <PERSON>, 
  Clock, 
  Globe,
  BarChart3,
  Target,
  Layers,
  Calendar,
  Play,
  Pause,
  RefreshCw
} from 'lucide-react'
import { taskAPI } from '../../services/api'
import { 
  HTTP_METHOD_OPTIONS, 
  PROXY_STRATEGY_OPTIONS, 
  TASK_PRIORITY_OPTIONS 
} from '../../utils/constants'
import toast from 'react-hot-toast'
import type { HttpMethod, ProxyStrategy, TaskPriority } from '../../types'

interface TaskTemplateFormProps {
  onSuccess: () => void
  onCancel: () => void
  templateId?: string // 如果提供，则为编辑模式
}

interface TaskTemplate {
  id?: string
  name: string
  description: string
  category: 'web_testing' | 'api_testing' | 'monitoring' | 'performance' | 'custom'
  task_type: 'single_url' | 'batch_url' | 'proxy_comparison' | 'scheduled'
  config: {
    method: HttpMethod
    headers: Record<string, string>
    body?: string
    proxy_strategy: ProxyStrategy
    priority: TaskPriority
    max_retries: number
    timeout: number
    // 特定类型的配置
    batch_config?: {
      execution_mode: 'sequential' | 'parallel'
      batch_size: number
      delay_between_batches: number
    }
    comparison_config?: {
      test_count: number
      concurrent: boolean
      include_metrics: {
        response_time: boolean
        success_rate: boolean
        geographic_info: boolean
        quality_score: boolean
      }
    }
    schedule_config?: {
      enabled: boolean
      cron_expression: string
      timezone: string
      max_runs?: number
      end_date?: string
    }
  }
  is_favorite: boolean
  usage_count: number
  created_at?: string
  updated_at?: string
}

// 预定义模板
const PREDEFINED_TEMPLATES: Partial<TaskTemplate>[] = [
  {
    name: '网站可用性检测',
    description: '检测网站是否可正常访问，适用于监控网站状态',
    category: 'web_testing',
    task_type: 'single_url',
    config: {
      method: 'GET',
      headers: { 'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36' },
      proxy_strategy: 'round_robin',
      priority: 2,
      max_retries: 3,
      timeout: 30
    }
  },
  {
    name: 'API接口测试',
    description: '测试API接口的响应性能和可用性',
    category: 'api_testing',
    task_type: 'single_url',
    config: {
      method: 'GET',
      headers: { 'Accept': 'application/json', 'Content-Type': 'application/json' },
      proxy_strategy: 'least_used',
      priority: 3,
      max_retries: 2,
      timeout: 15
    }
  },
  {
    name: '批量网站检测',
    description: '批量检测多个网站的可访问性',
    category: 'web_testing',
    task_type: 'batch_url',
    config: {
      method: 'GET',
      headers: { 'User-Agent': 'Mozilla/5.0 (compatible; WebChecker/1.0)' },
      proxy_strategy: 'random',
      priority: 2,
      max_retries: 2,
      timeout: 20,
      batch_config: {
        execution_mode: 'parallel',
        batch_size: 10,
        delay_between_batches: 1000
      }
    }
  },
  {
    name: '代理性能评估',
    description: '全面评估代理的性能指标',
    category: 'performance',
    task_type: 'proxy_comparison',
    config: {
      method: 'GET',
      headers: { 'User-Agent': 'ProxyTester/1.0' },
      proxy_strategy: 'round_robin',
      priority: 2,
      max_retries: 1,
      timeout: 30,
      comparison_config: {
        test_count: 5,
        concurrent: true,
        include_metrics: {
          response_time: true,
          success_rate: true,
          geographic_info: true,
          quality_score: true
        }
      }
    }
  },
  {
    name: '定时监控任务',
    description: '定期执行的监控任务，适用于持续监控',
    category: 'monitoring',
    task_type: 'scheduled',
    config: {
      method: 'GET',
      headers: { 'User-Agent': 'Monitor/1.0' },
      proxy_strategy: 'round_robin',
      priority: 2,
      max_retries: 3,
      timeout: 30,
      schedule_config: {
        enabled: true,
        cron_expression: '0 */30 * * * *', // 每30分钟
        timezone: 'Asia/Shanghai'
      }
    }
  }
]

const CATEGORY_CONFIG = {
  web_testing: { label: '网站测试', icon: Globe, color: 'blue' },
  api_testing: { label: 'API测试', icon: Target, color: 'green' },
  monitoring: { label: '监控任务', icon: BarChart3, color: 'purple' },
  performance: { label: '性能测试', icon: RefreshCw, color: 'orange' },
  custom: { label: '自定义', icon: Settings, color: 'gray' }
}

const TaskTemplateForm: React.FC<TaskTemplateFormProps> = ({ onSuccess, onCancel, templateId }) => {
  const [template, setTemplate] = useState<TaskTemplate>({
    name: '',
    description: '',
    category: 'custom',
    task_type: 'single_url',
    config: {
      method: 'GET',
      headers: {},
      proxy_strategy: 'round_robin',
      priority: 2,
      max_retries: 3,
      timeout: 30
    },
    is_favorite: false,
    usage_count: 0
  })

  const [loading, setLoading] = useState<boolean>(false)
  const [showPredefined, setShowPredefined] = useState<boolean>(!templateId)
  const [selectedPredefined, setSelectedPredefined] = useState<number | null>(null)

  // 如果是编辑模式，加载模板数据
  useEffect(() => {
    if (templateId) {
      loadTemplate(templateId)
    }
  }, [templateId])

  const loadTemplate = async (id: string) => {
    try {
      setLoading(true)
      // const response = await taskAPI.getTemplate(id)
      // setTemplate(response.data.data)
    } catch (error) {
      toast.error('加载模板失败')
    } finally {
      setLoading(false)
    }
  }

  // 应用预定义模板
  const applyPredefinedTemplate = (index: number) => {
    const predefined = PREDEFINED_TEMPLATES[index]
    setTemplate(prev => ({
      ...prev,
      ...predefined,
      config: {
        ...prev.config,
        ...predefined.config
      }
    }))
    setSelectedPredefined(index)
    setShowPredefined(false)
    toast.success('已应用预定义模板')
  }

  // 表单提交
  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault()
    
    if (!template.name.trim()) {
      toast.error('请输入模板名称')
      return
    }
    
    if (!template.description.trim()) {
      toast.error('请输入模板描述')
      return
    }
    
    try {
      setLoading(true)
      
      if (templateId) {
        // 更新模板
        await taskAPI.updateTemplate(templateId, template)
        toast.success('模板更新成功')
      } else {
        // 创建模板
        await taskAPI.createTemplate(template)
        toast.success('模板创建成功')
      }
      
      onSuccess()
    } catch (error) {
      toast.error(templateId ? '更新模板失败' : '创建模板失败')
    } finally {
      setLoading(false)
    }
  }

  return (
    <div className="space-y-6">
      {/* 预定义模板选择 */}
      {showPredefined && !templateId && (
        <div className="space-y-4">
          <div className="flex items-center justify-between">
            <h3 className="text-lg font-medium text-gray-900">选择预定义模板</h3>
            <button
              type="button"
              onClick={() => setShowPredefined(false)}
              className="text-sm text-blue-600 hover:text-blue-800"
            >
              自定义创建
            </button>
          </div>
          
          <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
            {PREDEFINED_TEMPLATES.map((template, index) => {
              const categoryConfig = CATEGORY_CONFIG[template.category!]
              const Icon = categoryConfig.icon
              
              return (
                <div
                  key={index}
                  onClick={() => applyPredefinedTemplate(index)}
                  className={`border-2 rounded-lg p-4 cursor-pointer transition-all ${
                    selectedPredefined === index
                      ? 'border-blue-500 bg-blue-50'
                      : 'border-gray-200 hover:border-blue-300 hover:bg-gray-50'
                  }`}
                >
                  <div className="flex items-start space-x-3">
                    <div className={`p-2 rounded-lg bg-${categoryConfig.color}-100`}>
                      <Icon className={`h-5 w-5 text-${categoryConfig.color}-600`} />
                    </div>
                    <div className="flex-1">
                      <h4 className="font-medium text-gray-900">{template.name}</h4>
                      <p className="text-sm text-gray-600 mt-1">{template.description}</p>
                      <div className="flex items-center space-x-2 mt-2">
                        <span className={`text-xs px-2 py-1 rounded-full bg-${categoryConfig.color}-100 text-${categoryConfig.color}-800`}>
                          {categoryConfig.label}
                        </span>
                        <span className="text-xs text-gray-500">
                          {template.config?.method} • {template.config?.timeout}s
                        </span>
                      </div>
                    </div>
                  </div>
                </div>
              )
            })}
          </div>
        </div>
      )}

      {/* 模板配置表单 */}
      {(!showPredefined || templateId) && (
        <form onSubmit={handleSubmit} className="space-y-6">
          {/* 基本信息 */}
          <div className="space-y-4">
            <h3 className="text-lg font-medium text-gray-900 flex items-center">
              <Settings className="h-5 w-5 mr-2" />
              模板信息
            </h3>
            
            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-2">
                  模板名称 *
                </label>
                <input
                  type="text"
                  required
                  className="input w-full"
                  placeholder="请输入模板名称"
                  value={template.name}
                  onChange={(e) => setTemplate(prev => ({ ...prev, name: e.target.value }))}
                />
              </div>
              
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-2">
                  模板分类
                </label>
                <select
                  className="input w-full"
                  value={template.category}
                  onChange={(e) => setTemplate(prev => ({ ...prev, category: e.target.value as any }))}
                >
                  {Object.entries(CATEGORY_CONFIG).map(([key, config]) => (
                    <option key={key} value={key}>{config.label}</option>
                  ))}
                </select>
              </div>
            </div>
            
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-2">
                模板描述 *
              </label>
              <textarea
                required
                rows={3}
                className="input w-full"
                placeholder="请描述此模板的用途和特点"
                value={template.description}
                onChange={(e) => setTemplate(prev => ({ ...prev, description: e.target.value }))}
              />
            </div>
          </div>

          {/* 任务类型 */}
          <div className="space-y-4">
            <h3 className="text-lg font-medium text-gray-900">任务类型</h3>
            
            <div className="grid grid-cols-2 md:grid-cols-4 gap-3">
              {[
                { value: 'single_url', label: '单URL测试', icon: Target },
                { value: 'batch_url', label: '批量URL测试', icon: Layers },
                { value: 'proxy_comparison', label: '代理对比', icon: BarChart3 },
                { value: 'scheduled', label: '定时任务', icon: Clock }
              ].map(({ value, label, icon: Icon }) => (
                <label
                  key={value}
                  className={`flex items-center space-x-2 p-3 border rounded-lg cursor-pointer ${
                    template.task_type === value
                      ? 'border-blue-500 bg-blue-50'
                      : 'border-gray-200 hover:border-gray-300'
                  }`}
                >
                  <input
                    type="radio"
                    name="task_type"
                    value={value}
                    checked={template.task_type === value}
                    onChange={(e) => setTemplate(prev => ({ ...prev, task_type: e.target.value as any }))}
                    className="sr-only"
                  />
                  <Icon className="h-5 w-5 text-gray-600" />
                  <span className="text-sm font-medium">{label}</span>
                </label>
              ))}
            </div>
          </div>

          {/* 基础配置 */}
          <div className="space-y-4">
            <h3 className="text-lg font-medium text-gray-900">基础配置</h3>
            
            <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-2">
                  HTTP方法
                </label>
                <select
                  className="input w-full"
                  value={template.config.method}
                  onChange={(e) => setTemplate(prev => ({
                    ...prev,
                    config: { ...prev.config, method: e.target.value as HttpMethod }
                  }))}
                >
                  {HTTP_METHOD_OPTIONS.map(option => (
                    <option key={option.value} value={option.value}>
                      {option.label}
                    </option>
                  ))}
                </select>
              </div>
              
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-2">
                  代理策略
                </label>
                <select
                  className="input w-full"
                  value={template.config.proxy_strategy}
                  onChange={(e) => setTemplate(prev => ({
                    ...prev,
                    config: { ...prev.config, proxy_strategy: e.target.value as ProxyStrategy }
                  }))}
                >
                  {PROXY_STRATEGY_OPTIONS.map(option => (
                    <option key={option.value} value={option.value}>
                      {option.label}
                    </option>
                  ))}
                </select>
              </div>
              
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-2">
                  优先级
                </label>
                <select
                  className="input w-full"
                  value={template.config.priority}
                  onChange={(e) => setTemplate(prev => ({
                    ...prev,
                    config: { ...prev.config, priority: parseInt(e.target.value) as TaskPriority }
                  }))}
                >
                  {TASK_PRIORITY_OPTIONS.map(option => (
                    <option key={option.value} value={option.value}>
                      {option.label}
                    </option>
                  ))}
                </select>
              </div>
              
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-2">
                  超时时间 (秒)
                </label>
                <input
                  type="number"
                  min="5"
                  max="300"
                  className="input w-full"
                  value={template.config.timeout}
                  onChange={(e) => setTemplate(prev => ({
                    ...prev,
                    config: { ...prev.config, timeout: parseInt(e.target.value) || 30 }
                  }))}
                />
              </div>
              
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-2">
                  最大重试次数
                </label>
                <input
                  type="number"
                  min="0"
                  max="10"
                  className="input w-full"
                  value={template.config.max_retries}
                  onChange={(e) => setTemplate(prev => ({
                    ...prev,
                    config: { ...prev.config, max_retries: parseInt(e.target.value) || 3 }
                  }))}
                />
              </div>
            </div>
            
            {/* 请求头配置 */}
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-2">
                默认请求头 (JSON格式)
              </label>
              <textarea
                rows={3}
                className="input w-full font-mono text-sm"
                placeholder='{"User-Agent": "Mozilla/5.0...", "Accept": "application/json"}'
                value={JSON.stringify(template.config.headers, null, 2)}
                onChange={(e) => {
                  try {
                    const headers = JSON.parse(e.target.value || '{}')
                    setTemplate(prev => ({
                      ...prev,
                      config: { ...prev.config, headers }
                    }))
                  } catch {
                    // 忽略JSON解析错误，用户输入时可能不完整
                  }
                }}
              />
            </div>
          </div>

          {/* 操作按钮 */}
          <div className="flex items-center justify-between pt-4 border-t border-gray-200">
            <div className="flex items-center space-x-2">
              <label className="flex items-center space-x-2">
                <input
                  type="checkbox"
                  checked={template.is_favorite}
                  onChange={(e) => setTemplate(prev => ({ ...prev, is_favorite: e.target.checked }))}
                  className="rounded border-gray-300 text-yellow-600 focus:ring-yellow-500"
                />
                <Star className={`h-4 w-4 ${template.is_favorite ? 'text-yellow-500 fill-current' : 'text-gray-400'}`} />
                <span className="text-sm text-gray-700">设为收藏</span>
              </label>
            </div>
            
            <div className="flex space-x-3">
              <button
                type="button"
                onClick={onCancel}
                className="btn-outline"
              >
                取消
              </button>
              <button
                type="submit"
                disabled={loading}
                className="btn-primary"
              >
                {loading ? (
                  <>
                    <RefreshCw className="h-4 w-4 mr-2 animate-spin" />
                    {templateId ? '更新中...' : '创建中...'}
                  </>
                ) : (
                  <>
                    <Save className="h-4 w-4 mr-2" />
                    {templateId ? '更新模板' : '创建模板'}
                  </>
                )}
              </button>
            </div>
          </div>
        </form>
      )}
    </div>
  )
}

export default TaskTemplateForm
