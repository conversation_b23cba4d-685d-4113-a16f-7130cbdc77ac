import React, { useState, useEffect } from 'react'
import { 
  BarChart3, 
  Target, 
  Clock, 
  Globe, 
  Zap,
  CheckCircle,
  AlertCircle,
  Search,
  Filter,
  Settings,
  Play,
  RefreshCw
} from 'lucide-react'
import { proxyAPI, taskAPI } from '../../services/api'
import LoadingSpinner from '../common/LoadingSpinner'
import { formatDate } from '../../utils/helpers'
import { TASK_PRIORITY_OPTIONS } from '../../utils/constants'
import toast from 'react-hot-toast'
import type { Proxy, TaskPriority } from '../../types'

interface ProxyComparisonFormProps {
  onSuccess: () => void
  onCancel: () => void
  initialData?: any
  isEditing?: boolean
}

interface ComparisonFormData {
  name: string
  target_url: string
  selected_proxies: string[]
  test_count: number
  concurrent: boolean
  priority: TaskPriority
  timeout: number
  include_metrics: {
    response_time: boolean
    success_rate: boolean
    geographic_info: boolean
    quality_score: boolean
  }
}

const ProxyComparisonForm: React.FC<ProxyComparisonFormProps> = ({
  onSuccess,
  onCancel,
  initialData,
  isEditing = false
}) => {
  const [formData, setFormData] = useState<ComparisonFormData>({
    name: '',
    target_url: '',
    selected_proxies: [],
    test_count: 5,
    concurrent: true,
    priority: 2,
    timeout: 30,
    include_metrics: {
      response_time: true,
      success_rate: true,
      geographic_info: true,
      quality_score: true
    }
  })

  const [proxies, setProxies] = useState<Proxy[]>([])
  const [loading, setLoading] = useState<boolean>(false)
  const [proxiesLoading, setProxiesLoading] = useState<boolean>(true)
  const [searchTerm, setSearchTerm] = useState<string>('')
  const [statusFilter, setStatusFilter] = useState<'all' | 'active' | 'inactive'>('active')
  const [locationFilter, setLocationFilter] = useState<string>('all')

  // 获取代理列表
  const fetchProxies = async () => {
    try {
      setProxiesLoading(true)
      const response = await proxyAPI.getProxies()
      setProxies(response.data.data || [])
    } catch (error) {
      console.error('Failed to fetch proxies:', error)
      toast.error('获取代理列表失败')
    } finally {
      setProxiesLoading(false)
    }
  }

  useEffect(() => {
    fetchProxies()
  }, [])

  // 过滤代理
  const filteredProxies = proxies.filter(proxy => {
    const matchesSearch = 
      proxy.host.toLowerCase().includes(searchTerm.toLowerCase()) ||
      proxy.location?.country?.toLowerCase().includes(searchTerm.toLowerCase()) ||
      proxy.location?.city?.toLowerCase().includes(searchTerm.toLowerCase())
    
    const matchesStatus = statusFilter === 'all' || proxy.status === statusFilter
    const matchesLocation = locationFilter === 'all' || proxy.location?.country === locationFilter
    
    return matchesSearch && matchesStatus && matchesLocation
  })

  // 获取唯一的国家列表
  const countries = Array.from(new Set(
    proxies
      .map(proxy => proxy.location?.country)
      .filter(Boolean)
  )).sort()

  // 处理代理选择
  const handleProxySelect = (proxyId: string, selected: boolean) => {
    setFormData(prev => ({
      ...prev,
      selected_proxies: selected
        ? [...prev.selected_proxies, proxyId]
        : prev.selected_proxies.filter(id => id !== proxyId)
    }))
  }

  // 全选/取消全选
  const handleSelectAll = (selected: boolean) => {
    setFormData(prev => ({
      ...prev,
      selected_proxies: selected ? filteredProxies.map(proxy => proxy.id) : []
    }))
  }

  // 智能选择代理
  const handleSmartSelect = (type: 'quality' | 'geographic' | 'random') => {
    let selectedIds: string[] = []
    
    switch (type) {
      case 'quality':
        // 选择质量评分最高的代理
        selectedIds = filteredProxies
          .sort((a, b) => (b.quality_score || 0) - (a.quality_score || 0))
          .slice(0, 10)
          .map(proxy => proxy.id)
        break
      
      case 'geographic':
        // 选择不同地理位置的代理
        const locationGroups = new Map<string, Proxy[]>()
        filteredProxies.forEach(proxy => {
          const location = proxy.location?.country || 'Unknown'
          if (!locationGroups.has(location)) {
            locationGroups.set(location, [])
          }
          locationGroups.get(location)!.push(proxy)
        })
        
        selectedIds = Array.from(locationGroups.values())
          .map(group => group[0]) // 每个地区选一个
          .slice(0, 15)
          .map(proxy => proxy.id)
        break
      
      case 'random':
        // 随机选择
        const shuffled = [...filteredProxies].sort(() => Math.random() - 0.5)
        selectedIds = shuffled.slice(0, 8).map(proxy => proxy.id)
        break
    }
    
    setFormData(prev => ({ ...prev, selected_proxies: selectedIds }))
    toast.success(`已选择 ${selectedIds.length} 个代理`)
  }

  // 表单提交
  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault()
    
    if (!formData.name.trim()) {
      toast.error('请输入任务名称')
      return
    }
    
    if (!formData.target_url.trim()) {
      toast.error('请输入目标URL')
      return
    }
    
    if (formData.selected_proxies.length === 0) {
      toast.error('请至少选择一个代理')
      return
    }
    
    if (formData.selected_proxies.length < 2) {
      toast.error('代理对比至少需要选择2个代理')
      return
    }
    
    try {
      setLoading(true)
      
      const taskData = {
        name: formData.name,
        type: 'proxy_comparison',
        target_url: formData.target_url,
        proxy_ids: formData.selected_proxies,
        test_count: formData.test_count,
        concurrent: formData.concurrent,
        priority: formData.priority,
        timeout: formData.timeout,
        include_metrics: formData.include_metrics
      }
      
      // 调用代理对比任务API
      await taskAPI.createProxyComparisonTask(taskData)

      toast.success('代理性能对比任务创建成功')
      onSuccess()
    } catch (error) {
      toast.error('创建任务失败')
    } finally {
      setLoading(false)
    }
  }

  return (
    <form onSubmit={handleSubmit} className="space-y-6">
      {/* 基本信息 */}
      <div className="space-y-4">
        <div>
          <label className="block text-sm font-medium text-gray-700 mb-2">
            任务名称 *
          </label>
          <input
            type="text"
            required
            className="input w-full"
            placeholder="请输入代理性能对比任务名称"
            value={formData.name}
            onChange={(e) => setFormData(prev => ({ ...prev, name: e.target.value }))}
          />
        </div>

        <div>
          <label className="block text-sm font-medium text-gray-700 mb-2">
            目标URL *
          </label>
          <input
            type="url"
            required
            className="input w-full"
            placeholder="https://example.com"
            value={formData.target_url}
            onChange={(e) => setFormData(prev => ({ ...prev, target_url: e.target.value }))}
          />
          <p className="text-xs text-gray-500 mt-1">
            所有选中的代理将访问此URL进行性能对比
          </p>
        </div>
      </div>

      {/* 测试配置 */}
      <div className="space-y-4">
        <h3 className="text-lg font-medium text-gray-900 flex items-center">
          <Settings className="h-5 w-5 mr-2" />
          测试配置
        </h3>

        <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
          <div>
            <label className="block text-sm font-medium text-gray-700 mb-2">
              每个代理测试次数
            </label>
            <input
              type="number"
              min="1"
              max="20"
              className="input w-full"
              value={formData.test_count}
              onChange={(e) => setFormData(prev => ({ ...prev, test_count: parseInt(e.target.value) || 5 }))}
            />
          </div>

          <div>
            <label className="block text-sm font-medium text-gray-700 mb-2">
              超时时间 (秒)
            </label>
            <input
              type="number"
              min="5"
              max="120"
              className="input w-full"
              value={formData.timeout}
              onChange={(e) => setFormData(prev => ({ ...prev, timeout: parseInt(e.target.value) || 30 }))}
            />
          </div>

          <div>
            <label className="block text-sm font-medium text-gray-700 mb-2">
              任务优先级
            </label>
            <select
              className="input w-full"
              value={formData.priority}
              onChange={(e) => setFormData(prev => ({ ...prev, priority: parseInt(e.target.value) as TaskPriority }))}
            >
              {TASK_PRIORITY_OPTIONS.map(option => (
                <option key={option.value} value={option.value}>
                  {option.label}
                </option>
              ))}
            </select>
          </div>
        </div>

        <div className="flex items-center space-x-4">
          <label className="flex items-center space-x-2">
            <input
              type="checkbox"
              checked={formData.concurrent}
              onChange={(e) => setFormData(prev => ({ ...prev, concurrent: e.target.checked }))}
              className="rounded border-gray-300 text-blue-600 focus:ring-blue-500"
            />
            <span className="text-sm text-gray-700">并发测试（推荐）</span>
          </label>
        </div>
      </div>

      {/* 指标选择 */}
      <div className="space-y-4">
        <h3 className="text-lg font-medium text-gray-900 flex items-center">
          <BarChart3 className="h-5 w-5 mr-2" />
          对比指标
        </h3>

        <div className="grid grid-cols-2 md:grid-cols-4 gap-4">
          <label className="flex items-center space-x-2">
            <input
              type="checkbox"
              checked={formData.include_metrics.response_time}
              onChange={(e) => setFormData(prev => ({
                ...prev,
                include_metrics: { ...prev.include_metrics, response_time: e.target.checked }
              }))}
              className="rounded border-gray-300 text-blue-600 focus:ring-blue-500"
            />
            <span className="text-sm text-gray-700">响应时间</span>
          </label>

          <label className="flex items-center space-x-2">
            <input
              type="checkbox"
              checked={formData.include_metrics.success_rate}
              onChange={(e) => setFormData(prev => ({
                ...prev,
                include_metrics: { ...prev.include_metrics, success_rate: e.target.checked }
              }))}
              className="rounded border-gray-300 text-blue-600 focus:ring-blue-500"
            />
            <span className="text-sm text-gray-700">成功率</span>
          </label>

          <label className="flex items-center space-x-2">
            <input
              type="checkbox"
              checked={formData.include_metrics.geographic_info}
              onChange={(e) => setFormData(prev => ({
                ...prev,
                include_metrics: { ...prev.include_metrics, geographic_info: e.target.checked }
              }))}
              className="rounded border-gray-300 text-blue-600 focus:ring-blue-500"
            />
            <span className="text-sm text-gray-700">地理信息</span>
          </label>

          <label className="flex items-center space-x-2">
            <input
              type="checkbox"
              checked={formData.include_metrics.quality_score}
              onChange={(e) => setFormData(prev => ({
                ...prev,
                include_metrics: { ...prev.include_metrics, quality_score: e.target.checked }
              }))}
              className="rounded border-gray-300 text-blue-600 focus:ring-blue-500"
            />
            <span className="text-sm text-gray-700">质量评分</span>
          </label>
        </div>
      </div>

      {/* 代理选择区域 */}
      <div className="space-y-4">
        <div className="flex items-center justify-between">
          <h3 className="text-lg font-medium text-gray-900 flex items-center">
            <Target className="h-5 w-5 mr-2" />
            代理选择
          </h3>
          <span className="text-sm text-gray-500">
            已选择 {formData.selected_proxies.length} 个代理
          </span>
        </div>

        {/* 智能选择按钮 */}
        <div className="flex flex-wrap gap-2">
          <button
            type="button"
            onClick={() => handleSmartSelect('quality')}
            className="btn-outline text-sm"
          >
            <Zap className="h-4 w-4 mr-1" />
            选择高质量代理
          </button>
          <button
            type="button"
            onClick={() => handleSmartSelect('geographic')}
            className="btn-outline text-sm"
          >
            <Globe className="h-4 w-4 mr-1" />
            选择不同地区代理
          </button>
          <button
            type="button"
            onClick={() => handleSmartSelect('random')}
            className="btn-outline text-sm"
          >
            <RefreshCw className="h-4 w-4 mr-1" />
            随机选择
          </button>
        </div>

        {/* 代理筛选 */}
        <div className="bg-gray-50 rounded-lg p-4 space-y-3">
          <div className="grid grid-cols-1 md:grid-cols-3 gap-3">
            <div className="relative">
              <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-gray-400" />
              <input
                type="text"
                className="pl-10 input w-full"
                placeholder="搜索代理..."
                value={searchTerm}
                onChange={(e) => setSearchTerm(e.target.value)}
              />
            </div>

            <select
              className="input w-full"
              value={statusFilter}
              onChange={(e) => setStatusFilter(e.target.value as 'all' | 'active' | 'inactive')}
            >
              <option value="all">所有状态</option>
              <option value="active">正常</option>
              <option value="inactive">未激活</option>
            </select>

            <select
              className="input w-full"
              value={locationFilter}
              onChange={(e) => setLocationFilter(e.target.value)}
            >
              <option value="all">所有地区</option>
              {countries.map(country => (
                <option key={country} value={country}>{country}</option>
              ))}
            </select>
          </div>

          <div className="flex items-center justify-between">
            <label className="flex items-center space-x-2 text-sm">
              <input
                type="checkbox"
                checked={formData.selected_proxies.length === filteredProxies.length && filteredProxies.length > 0}
                onChange={(e) => handleSelectAll(e.target.checked)}
                className="rounded border-gray-300 text-blue-600 focus:ring-blue-500"
              />
              <span>全选当前筛选结果</span>
            </label>
            
            <span className="text-sm text-gray-500">
              显示 {filteredProxies.length} / {proxies.length} 个代理
            </span>
          </div>
        </div>

        {/* 代理列表 */}
        {proxiesLoading ? (
          <div className="flex justify-center py-8">
            <LoadingSpinner size="md" text="加载代理列表..." />
          </div>
        ) : (
          <div className="border border-gray-200 rounded-lg max-h-80 overflow-y-auto">
            {filteredProxies.length === 0 ? (
              <div className="text-center py-8 text-gray-500">
                <Target className="h-8 w-8 mx-auto mb-2 text-gray-400" />
                <p>没有找到匹配的代理</p>
              </div>
            ) : (
              <div className="divide-y divide-gray-200">
                {filteredProxies.map(proxy => (
                  <div key={proxy.id} className="p-4 hover:bg-gray-50">
                    <div className="flex items-center justify-between">
                      <div className="flex items-center space-x-3">
                        <input
                          type="checkbox"
                          checked={formData.selected_proxies.includes(proxy.id)}
                          onChange={(e) => handleProxySelect(proxy.id, e.target.checked)}
                          className="rounded border-gray-300 text-blue-600 focus:ring-blue-500"
                        />
                        <div>
                          <div className="flex items-center space-x-2">
                            <span className="font-medium text-gray-900">
                              {proxy.host}:{proxy.port}
                            </span>
                            {proxy.status === 'active' ? (
                              <CheckCircle className="h-4 w-4 text-green-500" />
                            ) : (
                              <AlertCircle className="h-4 w-4 text-red-500" />
                            )}
                          </div>
                          <div className="text-sm text-gray-500 space-x-4">
                            <span>{proxy.type.toUpperCase()}</span>
                            {proxy.location && (
                              <span>{proxy.location.city}, {proxy.location.country}</span>
                            )}
                            {proxy.quality_score && (
                              <span>质量: {(proxy.quality_score * 100).toFixed(1)}%</span>
                            )}
                            {proxy.response_time && (
                              <span>响应: {proxy.response_time}ms</span>
                            )}
                          </div>
                        </div>
                      </div>
                    </div>
                  </div>
                ))}
              </div>
            )}
          </div>
        )}
      </div>

      {/* 任务预览 */}
      {formData.selected_proxies.length > 0 && (
        <div className="bg-blue-50 rounded-lg p-4">
          <h4 className="font-medium text-blue-900 mb-2 flex items-center">
            <Play className="h-4 w-4 mr-2" />
            任务预览
          </h4>
          <div className="text-sm text-blue-800 space-y-1">
            <p>• 将对比 <strong>{formData.selected_proxies.length}</strong> 个代理的性能</p>
            <p>• 每个代理测试 <strong>{formData.test_count}</strong> 次，目标URL: <strong>{formData.target_url || '未设置'}</strong></p>
            <p>• 采用 <strong>{formData.concurrent ? '并发' : '串行'}</strong> 测试模式</p>
            <p>• 预计总测试次数: <strong>{formData.selected_proxies.length * formData.test_count}</strong></p>
            <p>• 预计执行时间: <strong>{Math.ceil(formData.selected_proxies.length * formData.test_count * formData.timeout / (formData.concurrent ? 10 : 1))} 秒</strong></p>
          </div>
        </div>
      )}

      {/* 表单操作按钮 */}
      <div className="flex justify-end space-x-3 pt-4 border-t border-gray-200">
        <button
          type="button"
          onClick={onCancel}
          className="btn-outline"
        >
          取消
        </button>
        <button
          type="submit"
          disabled={loading || formData.selected_proxies.length < 2}
          className="btn-primary"
        >
          {loading ? (
            <>
              <BarChart3 className="h-4 w-4 mr-2 animate-pulse" />
              创建中...
            </>
          ) : (
            <>
              <Play className="h-4 w-4 mr-2" />
              创建对比任务
            </>
          )}
        </button>
      </div>
    </form>
  )
}

export default ProxyComparisonForm
