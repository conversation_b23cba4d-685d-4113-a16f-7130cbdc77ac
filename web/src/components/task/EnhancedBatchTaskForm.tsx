import React, { useState, useEffect } from 'react'
import { Plus, Trash2, Upload, Download, Settings } from 'lucide-react'
import { taskAPI, proxyAPI } from '../../services/api'
import { 
  HTTP_METHOD_OPTIONS, 
  PROXY_STRATEGY_OPTIONS, 
  TASK_PRIORITY_OPTIONS 
} from '../../utils/constants'
import { validateUrl } from '../../utils/helpers'
import toast from 'react-hot-toast'
import type { Proxy, HttpMethod, ProxyStrategy, TaskPriority } from '../../types'

interface BatchTaskConfig {
  name: string
  urls: string[]
  method: HttpMethod
  headers: string
  body: string
  priority: TaskPriority
  max_retries: number
  timeout: number
  proxy_selection: 'all' | 'selected' | 'strategy'
  selected_proxies: string[]
  proxy_strategy: ProxyStrategy
}

interface EnhancedBatchTaskFormProps {
  onSuccess: () => void
  onCancel: () => void
}

const EnhancedBatchTaskForm: React.FC<EnhancedBatchTaskFormProps> = ({
  onSuccess,
  onCancel
}) => {
  const [config, setConfig] = useState<BatchTaskConfig>({
    name: '',
    urls: [''],
    method: 'GET',
    headers: '{}',
    body: '',
    priority: 2,
    max_retries: 3,
    timeout: 30,
    proxy_selection: 'strategy',
    selected_proxies: [],
    proxy_strategy: 'round_robin'
  })
  
  const [proxies, setProxies] = useState<Proxy[]>([])
  const [loading, setLoading] = useState<boolean>(false)
  const [loadingProxies, setLoadingProxies] = useState<boolean>(true)
  const [urlInput, setUrlInput] = useState<string>('')

  // 逐个创建任务的辅助函数
  const createTasksIndividually = async (tasksData: any[]): Promise<void> => {
    const batchSize = 10 // 每批处理10个任务
    const totalTasks = tasksData.length
    let completedTasks = 0
    let failedTasks = 0

    // 显示进度提示
    if (totalTasks > batchSize) {
      toast.loading(`正在创建 ${totalTasks} 个任务...`, { id: 'batch-create' })
    }

    // 分批执行任务创建
    for (let i = 0; i < tasksData.length; i += batchSize) {
      const batch = tasksData.slice(i, i + batchSize)
      const batchPromises = batch.map(taskData => taskAPI.createTask(taskData))

      try {
        const results = await Promise.allSettled(batchPromises)

        results.forEach(result => {
          if (result.status === 'fulfilled') {
            completedTasks++
          } else {
            failedTasks++
            console.error('Task creation failed:', result.reason)
          }
        })

        // 更新进度
        if (totalTasks > batchSize) {
          const progress = Math.round((completedTasks + failedTasks) / totalTasks * 100)
          toast.loading(`创建进度: ${progress}% (${completedTasks + failedTasks}/${totalTasks})`, { id: 'batch-create' })
        }

        // 在批次之间添加短暂延迟，避免服务器过载
        if (i + batchSize < tasksData.length) {
          await new Promise(resolve => setTimeout(resolve, 100))
        }
      } catch (error) {
        console.error('Batch creation error:', error)
        failedTasks += batch.length
      }
    }

    // 关闭进度提示
    if (totalTasks > batchSize) {
      toast.dismiss('batch-create')
    }

    // 显示结果
    if (failedTasks === 0) {
      toast.success(`成功创建 ${completedTasks} 个任务`)
    } else if (completedTasks > 0) {
      toast.success(`成功创建 ${completedTasks} 个任务，${failedTasks} 个失败`)
    } else {
      toast.error(`任务创建失败，请检查网络连接和服务器状态`)
      throw new Error('All tasks failed to create')
    }
  }

  // 获取代理列表
  useEffect(() => {
    const fetchProxies = async () => {
      try {
        const response = await proxyAPI.getProxies()
        setProxies(response.data.data || [])
      } catch (error) {
        console.error('Failed to fetch proxies:', error)
        toast.error('获取代理列表失败')
      } finally {
        setLoadingProxies(false)
      }
    }
    fetchProxies()
  }, [])

  // 添加URL
  const addUrl = () => {
    setConfig(prev => ({
      ...prev,
      urls: [...prev.urls, '']
    }))
  }

  // 删除URL
  const removeUrl = (index: number) => {
    setConfig(prev => ({
      ...prev,
      urls: prev.urls.filter((_, i) => i !== index)
    }))
  }

  // 更新URL
  const updateUrl = (index: number, value: string) => {
    setConfig(prev => ({
      ...prev,
      urls: prev.urls.map((url, i) => i === index ? value : url)
    }))
  }

  // 批量添加URL
  const batchAddUrls = () => {
    if (!urlInput.trim()) return
    
    const newUrls = urlInput
      .split('\n')
      .map(url => url.trim())
      .filter(url => url && validateUrl(url))
    
    if (newUrls.length === 0) {
      toast.error('请输入有效的URL')
      return
    }

    setConfig(prev => ({
      ...prev,
      urls: [...prev.urls.filter(url => url.trim()), ...newUrls]
    }))
    setUrlInput('')
    toast.success(`已添加 ${newUrls.length} 个URL`)
  }

  // 处理代理选择
  const handleProxySelection = (proxyId: string, selected: boolean) => {
    setConfig(prev => ({
      ...prev,
      selected_proxies: selected
        ? [...prev.selected_proxies, proxyId]
        : prev.selected_proxies.filter(id => id !== proxyId)
    }))
  }

  // 全选/取消全选代理
  const handleSelectAllProxies = (selected: boolean) => {
    setConfig(prev => ({
      ...prev,
      selected_proxies: selected ? proxies.map(p => p.id) : []
    }))
  }

  // 验证表单
  const validateForm = (): boolean => {
    if (!config.name.trim()) {
      toast.error('请输入任务名称')
      return false
    }

    const validUrls = config.urls.filter(url => url.trim() && validateUrl(url))
    if (validUrls.length === 0) {
      toast.error('请至少添加一个有效的URL')
      return false
    }

    if (config.proxy_selection === 'selected' && config.selected_proxies.length === 0) {
      toast.error('请选择至少一个代理')
      return false
    }

    try {
      JSON.parse(config.headers)
    } catch {
      toast.error('请输入有效的JSON格式请求头')
      return false
    }

    return true
  }

  // 提交表单
  const handleSubmit = async () => {
    if (!validateForm()) return

    try {
      setLoading(true)
      
      const validUrls = config.urls.filter(url => url.trim() && validateUrl(url))
      const headers = JSON.parse(config.headers)

      // 根据代理选择策略准备任务数据
      let tasksData: any[] = []

      if (config.proxy_selection === 'all') {
        // 为所有代理创建任务
        tasksData = proxies.flatMap(proxy =>
          validUrls.map(url => ({
            name: `${config.name} - ${proxy.host}:${proxy.port} - ${url}`,
            url,
            method: config.method,
            headers,
            body: config.body,
            proxy_strategy: config.proxy_strategy,
            priority: config.priority,
            max_retries: config.max_retries,
            timeout: config.timeout
          }))
        )
      } else if (config.proxy_selection === 'selected') {
        // 为选定的代理创建任务
        const selectedProxies = proxies.filter(p => config.selected_proxies.includes(p.id))
        tasksData = selectedProxies.flatMap(proxy =>
          validUrls.map(url => ({
            name: `${config.name} - ${proxy.host}:${proxy.port} - ${url}`,
            url,
            method: config.method,
            headers,
            body: config.body,
            proxy_strategy: config.proxy_strategy,
            priority: config.priority,
            max_retries: config.max_retries,
            timeout: config.timeout
          }))
        )
      } else {
        // 使用策略选择代理
        tasksData = validUrls.map(url => ({
          name: `${config.name} - ${url}`,
          url,
          method: config.method,
          headers,
          body: config.body,
          proxy_strategy: config.proxy_strategy,
          priority: config.priority,
          max_retries: config.max_retries,
          timeout: config.timeout
        }))
      }

      const totalTasks = tasksData.length

      // 优化批量创建：使用专用的批量API或分批处理
      if (totalTasks <= 50) {
        // 小批量：使用批量API（如果后端支持）
        try {
          const result = await taskAPI.createBatchTasks(tasksData)
          const { created, failed, errors } = result.data.data || { created: 0, failed: totalTasks }

          if (failed === 0) {
            toast.success(`成功创建 ${created} 个任务`)
          } else if (created > 0) {
            toast.success(`成功创建 ${created} 个任务，${failed} 个失败`)
            if (errors && errors.length > 0) {
              console.error('Batch creation errors:', errors)
            }
          } else {
            toast.error('批量任务创建失败')
            return
          }
        } catch (error) {
          console.warn('批量API不可用，回退到逐个创建:', error)
          // 回退到逐个创建
          await createTasksIndividually(tasksData)
        }
      } else {
        // 大批量：分批处理
        await createTasksIndividually(tasksData)
      }

      onSuccess()
    } catch (error: any) {
      console.error('Failed to create batch tasks:', error)
      const errorMessage = error?.response?.data?.message || error?.message || '创建批量任务失败'
      toast.error(errorMessage)
    } finally {
      setLoading(false)
    }
  }

  return (
    <div className="space-y-6">
      {/* 基本配置 */}
      <div className="space-y-4">
        <div>
          <label className="block text-sm font-medium text-gray-700 mb-1">
            任务名称 *
          </label>
          <input
            type="text"
            className="input w-full"
            value={config.name}
            onChange={(e) => setConfig(prev => ({ ...prev, name: e.target.value }))}
            placeholder="输入任务名称"
          />
        </div>

        <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
          <div>
            <label className="block text-sm font-medium text-gray-700 mb-1">
              请求方法
            </label>
            <select
              className="input w-full"
              value={config.method}
              onChange={(e) => setConfig(prev => ({ ...prev, method: e.target.value as HttpMethod }))}
            >
              {HTTP_METHOD_OPTIONS.map(option => (
                <option key={option.value} value={option.value}>
                  {option.label}
                </option>
              ))}
            </select>
          </div>

          <div>
            <label className="block text-sm font-medium text-gray-700 mb-1">
              优先级
            </label>
            <select
              className="input w-full"
              value={config.priority}
              onChange={(e) => setConfig(prev => ({ ...prev, priority: Number(e.target.value) as TaskPriority }))}
            >
              {TASK_PRIORITY_OPTIONS.map(option => (
                <option key={option.value} value={option.value}>
                  {option.label}
                </option>
              ))}
            </select>
          </div>
        </div>

        <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
          <div>
            <label className="block text-sm font-medium text-gray-700 mb-1">
              最大重试次数
            </label>
            <input
              type="number"
              min="0"
              max="10"
              className="input w-full"
              value={config.max_retries}
              onChange={(e) => setConfig(prev => ({ ...prev, max_retries: Number(e.target.value) }))}
            />
          </div>

          <div>
            <label className="block text-sm font-medium text-gray-700 mb-1">
              超时时间 (秒)
            </label>
            <input
              type="number"
              min="1"
              max="300"
              className="input w-full"
              value={config.timeout}
              onChange={(e) => setConfig(prev => ({ ...prev, timeout: Number(e.target.value) }))}
            />
          </div>
        </div>
      </div>

      {/* URL配置 */}
      <div className="space-y-4">
        <div className="flex items-center justify-between">
          <h3 className="text-lg font-medium text-gray-900">URL配置</h3>
          <button
            type="button"
            onClick={addUrl}
            className="btn-sm btn-outline"
          >
            <Plus className="h-4 w-4 mr-1" />
            添加URL
          </button>
        </div>

        {/* URL列表 */}
        <div className="space-y-2">
          {config.urls.map((url, index) => (
            <div key={index} className="flex items-center space-x-2">
              <input
                type="url"
                className="input flex-1"
                value={url}
                onChange={(e) => updateUrl(index, e.target.value)}
                placeholder="输入URL"
              />
              {config.urls.length > 1 && (
                <button
                  type="button"
                  onClick={() => removeUrl(index)}
                  className="p-2 text-red-600 hover:text-red-800 hover:bg-red-50 rounded"
                >
                  <Trash2 className="h-4 w-4" />
                </button>
              )}
            </div>
          ))}
        </div>

        {/* 批量添加URL */}
        <div className="border-t pt-4">
          <label className="block text-sm font-medium text-gray-700 mb-2">
            批量添加URL (每行一个)
          </label>
          <textarea
            className="input w-full h-24"
            value={urlInput}
            onChange={(e) => setUrlInput(e.target.value)}
            placeholder="https://example1.com&#10;https://example2.com&#10;https://example3.com"
          />
          <button
            type="button"
            onClick={batchAddUrls}
            className="mt-2 btn-sm btn-outline"
          >
            <Upload className="h-4 w-4 mr-1" />
            批量添加
          </button>
        </div>
      </div>

      {/* 代理选择 */}
      <div className="space-y-4">
        <h3 className="text-lg font-medium text-gray-900">代理选择</h3>
        
        <div className="space-y-3">
          <label className="flex items-center">
            <input
              type="radio"
              name="proxy_selection"
              value="strategy"
              checked={config.proxy_selection === 'strategy'}
              onChange={(e) => setConfig(prev => ({ ...prev, proxy_selection: e.target.value as any }))}
              className="mr-2"
            />
            <span>使用策略自动选择代理</span>
          </label>

          {config.proxy_selection === 'strategy' && (
            <div className="ml-6">
              <select
                className="input w-full max-w-xs"
                value={config.proxy_strategy}
                onChange={(e) => setConfig(prev => ({ ...prev, proxy_strategy: e.target.value as ProxyStrategy }))}
              >
                {PROXY_STRATEGY_OPTIONS.map(option => (
                  <option key={option.value} value={option.value}>
                    {option.label}
                  </option>
                ))}
              </select>
            </div>
          )}

          <label className="flex items-center">
            <input
              type="radio"
              name="proxy_selection"
              value="selected"
              checked={config.proxy_selection === 'selected'}
              onChange={(e) => setConfig(prev => ({ ...prev, proxy_selection: e.target.value as any }))}
              className="mr-2"
            />
            <span>手动选择代理</span>
          </label>

          <label className="flex items-center">
            <input
              type="radio"
              name="proxy_selection"
              value="all"
              checked={config.proxy_selection === 'all'}
              onChange={(e) => setConfig(prev => ({ ...prev, proxy_selection: e.target.value as any }))}
              className="mr-2"
            />
            <span>使用所有可用代理</span>
          </label>
        </div>

        {/* 代理列表 */}
        {config.proxy_selection === 'selected' && (
          <div className="border rounded-lg p-4 max-h-64 overflow-y-auto">
            {loadingProxies ? (
              <div className="text-center py-4 text-gray-500">加载代理列表...</div>
            ) : proxies.length === 0 ? (
              <div className="text-center py-4 text-gray-500">暂无可用代理</div>
            ) : (
              <div className="space-y-2">
                <div className="flex items-center justify-between border-b pb-2">
                  <span className="text-sm font-medium">选择代理</span>
                  <button
                    type="button"
                    onClick={() => handleSelectAllProxies(config.selected_proxies.length !== proxies.length)}
                    className="text-sm text-blue-600 hover:text-blue-800"
                  >
                    {config.selected_proxies.length === proxies.length ? '取消全选' : '全选'}
                  </button>
                </div>
                {proxies.map(proxy => (
                  <label key={proxy.id} className="flex items-center">
                    <input
                      type="checkbox"
                      checked={config.selected_proxies.includes(proxy.id)}
                      onChange={(e) => handleProxySelection(proxy.id, e.target.checked)}
                      className="mr-2"
                    />
                    <span className="text-sm">
                      {proxy.host}:{proxy.port} 
                      {proxy.location && (
                        <span className="text-gray-500 ml-2">
                          ({proxy.location.country_name})
                        </span>
                      )}
                    </span>
                  </label>
                ))}
              </div>
            )}
          </div>
        )}
      </div>

      {/* 高级配置 */}
      <div className="space-y-4">
        <h3 className="text-lg font-medium text-gray-900">高级配置</h3>
        
        <div>
          <label className="block text-sm font-medium text-gray-700 mb-1">
            请求头 (JSON格式)
          </label>
          <textarea
            className="input w-full h-24 font-mono text-sm"
            value={config.headers}
            onChange={(e) => setConfig(prev => ({ ...prev, headers: e.target.value }))}
            placeholder='{"Content-Type": "application/json"}'
          />
        </div>

        {(config.method === 'POST' || config.method === 'PUT' || config.method === 'PATCH') && (
          <div>
            <label className="block text-sm font-medium text-gray-700 mb-1">
              请求体
            </label>
            <textarea
              className="input w-full h-24"
              value={config.body}
              onChange={(e) => setConfig(prev => ({ ...prev, body: e.target.value }))}
              placeholder="输入请求体内容"
            />
          </div>
        )}
      </div>

      {/* 操作按钮 */}
      <div className="flex justify-end space-x-3 pt-6 border-t">
        <button
          type="button"
          onClick={onCancel}
          className="btn-outline"
          disabled={loading}
        >
          取消
        </button>
        <button
          type="button"
          onClick={handleSubmit}
          className="btn-primary"
          disabled={loading}
        >
          {loading ? '创建中...' : '创建批量任务'}
        </button>
      </div>
    </div>
  )
}

export default EnhancedBatchTaskForm
