import React from 'react'
import { Globe, MapPin, Activity, TrendingUp, Users, Zap } from 'lucide-react'
import type { ProxyLocationStats } from '../types'

interface LocationOverviewProps {
  locationStats: ProxyLocationStats
  className?: string
}

const LocationOverview: React.FC<LocationOverviewProps> = ({
  locationStats,
  className = ''
}) => {
  // 计算总体统计
  const totalCountries = locationStats.countries.length
  const totalCities = locationStats.cities.length
  const totalProxies = locationStats.countries.reduce((sum, country) => sum + country.total, 0) + locationStats.unknown.total
  const totalActive = locationStats.countries.reduce((sum, country) => sum + country.active, 0) + locationStats.unknown.active
  const overallSuccessRate = totalProxies > 0 ? (totalActive / totalProxies) * 100 : 0

  // 获取最佳表现的国家和城市
  const topCountry = locationStats.countries.reduce((best, current) => 
    current.success_rate > best.success_rate ? current : best, 
    locationStats.countries[0] || { success_rate: 0, country_code: 'N/A', total: 0, active: 0 }
  )

  const topCity = locationStats.cities.reduce((best, current) => 
    current.success_rate > best.success_rate ? current : best,
    locationStats.cities[0] || { success_rate: 0, city_name: 'N/A', country_code: 'N/A', total: 0, active: 0 }
  )

  // 计算平均响应时间
  const avgResponseTime = locationStats.countries.length > 0 
    ? locationStats.countries.reduce((sum, country) => sum + country.avg_response, 0) / locationStats.countries.length
    : 0

  return (
    <div className={`bg-white rounded-lg border border-gray-200 ${className}`}>
      <div className="p-6">
        <div className="flex items-center justify-between mb-6">
          <h3 className="text-lg font-semibold text-gray-900">地理位置概览</h3>
          <div className="flex items-center text-sm text-gray-500">
            <Activity className="h-4 w-4 mr-1" />
            实时数据
          </div>
        </div>

        {/* 总体统计卡片 */}
        <div className="grid grid-cols-2 md:grid-cols-4 gap-4 mb-6">
          <div className="bg-blue-50 rounded-lg p-4">
            <div className="flex items-center">
              <Globe className="h-8 w-8 text-blue-500" />
              <div className="ml-3">
                <p className="text-sm font-medium text-blue-600">覆盖国家</p>
                <p className="text-2xl font-bold text-blue-900">{totalCountries}</p>
              </div>
            </div>
          </div>

          <div className="bg-green-50 rounded-lg p-4">
            <div className="flex items-center">
              <MapPin className="h-8 w-8 text-green-500" />
              <div className="ml-3">
                <p className="text-sm font-medium text-green-600">覆盖城市</p>
                <p className="text-2xl font-bold text-green-900">{totalCities}</p>
              </div>
            </div>
          </div>

          <div className="bg-purple-50 rounded-lg p-4">
            <div className="flex items-center">
              <Users className="h-8 w-8 text-purple-500" />
              <div className="ml-3">
                <p className="text-sm font-medium text-purple-600">总代理数</p>
                <p className="text-2xl font-bold text-purple-900">{totalProxies}</p>
              </div>
            </div>
          </div>

          <div className="bg-yellow-50 rounded-lg p-4">
            <div className="flex items-center">
              <TrendingUp className="h-8 w-8 text-yellow-500" />
              <div className="ml-3">
                <p className="text-sm font-medium text-yellow-600">总体成功率</p>
                <p className="text-2xl font-bold text-yellow-900">{overallSuccessRate.toFixed(1)}%</p>
              </div>
            </div>
          </div>
        </div>

        {/* 性能指标 */}
        <div className="grid grid-cols-1 md:grid-cols-2 gap-6 mb-6">
          {/* 最佳表现国家 */}
          <div className="border border-gray-200 rounded-lg p-4">
            <h4 className="text-sm font-medium text-gray-900 mb-3 flex items-center">
              <Globe className="h-4 w-4 mr-2 text-blue-500" />
              最佳表现国家
            </h4>
            {topCountry.country_code !== 'N/A' ? (
              <div>
                <div className="flex items-center justify-between mb-2">
                  <span className="text-lg font-semibold text-gray-900">{topCountry.country_code}</span>
                  <span className="text-sm font-medium text-green-600">{topCountry.success_rate.toFixed(1)}%</span>
                </div>
                <div className="text-sm text-gray-500">
                  {topCountry.active}/{topCountry.total} 个代理活跃
                </div>
                <div className="w-full bg-gray-200 rounded-full h-2 mt-2">
                  <div 
                    className="bg-green-500 h-2 rounded-full" 
                    style={{ width: `${topCountry.success_rate}%` }}
                  />
                </div>
              </div>
            ) : (
              <div className="text-gray-500 text-sm">暂无数据</div>
            )}
          </div>

          {/* 最佳表现城市 */}
          <div className="border border-gray-200 rounded-lg p-4">
            <h4 className="text-sm font-medium text-gray-900 mb-3 flex items-center">
              <MapPin className="h-4 w-4 mr-2 text-green-500" />
              最佳表现城市
            </h4>
            {topCity.city_name !== 'N/A' ? (
              <div>
                <div className="flex items-center justify-between mb-2">
                  <span className="text-lg font-semibold text-gray-900">
                    {topCity.city_name} ({topCity.country_code})
                  </span>
                  <span className="text-sm font-medium text-green-600">{topCity.success_rate.toFixed(1)}%</span>
                </div>
                <div className="text-sm text-gray-500">
                  {topCity.active}/{topCity.total} 个代理活跃
                </div>
                <div className="w-full bg-gray-200 rounded-full h-2 mt-2">
                  <div 
                    className="bg-green-500 h-2 rounded-full" 
                    style={{ width: `${topCity.success_rate}%` }}
                  />
                </div>
              </div>
            ) : (
              <div className="text-gray-500 text-sm">暂无数据</div>
            )}
          </div>
        </div>

        {/* 性能统计 */}
        <div className="border-t border-gray-200 pt-4">
          <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
            <div className="text-center">
              <div className="flex items-center justify-center mb-2">
                <Zap className="h-5 w-5 text-orange-500 mr-2" />
                <span className="text-sm font-medium text-gray-600">平均响应时间</span>
              </div>
              <div className="text-xl font-bold text-gray-900">
                {avgResponseTime > 0 ? `${avgResponseTime.toFixed(0)}ms` : 'N/A'}
              </div>
            </div>

            <div className="text-center">
              <div className="flex items-center justify-center mb-2">
                <Activity className="h-5 w-5 text-green-500 mr-2" />
                <span className="text-sm font-medium text-gray-600">活跃代理</span>
              </div>
              <div className="text-xl font-bold text-green-600">{totalActive}</div>
            </div>

            <div className="text-center">
              <div className="flex items-center justify-center mb-2">
                <TrendingUp className="h-5 w-5 text-blue-500 mr-2" />
                <span className="text-sm font-medium text-gray-600">可用率</span>
              </div>
              <div className="text-xl font-bold text-blue-600">{overallSuccessRate.toFixed(1)}%</div>
            </div>
          </div>
        </div>

        {/* 未知位置统计 */}
        {locationStats.unknown.total > 0 && (
          <div className="border-t border-gray-200 pt-4 mt-4">
            <div className="bg-gray-50 rounded-lg p-4">
              <h4 className="text-sm font-medium text-gray-900 mb-2">未知位置代理</h4>
              <div className="flex items-center justify-between">
                <span className="text-sm text-gray-600">
                  {locationStats.unknown.total} 个代理，{locationStats.unknown.active} 个活跃
                </span>
                <span className="text-sm font-medium text-gray-900">
                  {locationStats.unknown.success_rate.toFixed(1)}% 成功率
                </span>
              </div>
            </div>
          </div>
        )}
      </div>
    </div>
  )
}

export default LocationOverview
