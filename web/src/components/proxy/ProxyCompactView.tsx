import React from 'react'
import {
  Activity,
  BarChart3,
  <PERSON>rash2,
  MapPin,
  Zap,
  CheckCircle,
  XCircle,
  AlertCircle,
  Star
} from 'lucide-react'
import { formatResponseTime } from '../../utils/helpers'
import type { Proxy } from '../../types'

interface ProxyCompactViewProps {
  proxies: Proxy[]
  selectedProxies: string[]
  operatingIds: Set<string>
  onSelectProxy: (proxyId: string) => void
  onSelectAll: () => void
  onHealthCheck: (proxyId: string) => void
  onQualityAssess: (proxyId: string) => void
  onDelete: (proxyId: string) => void
}

const ProxyCompactView: React.FC<ProxyCompactViewProps> = ({
  proxies,
  selectedProxies,
  operatingIds,
  onSelectProxy,
  onSelectAll,
  onHealthCheck,
  onQualityAssess,
  onDelete
}) => {
  const getStatusIcon = (status: string) => {
    switch (status) {
      case 'active':
        return <CheckCircle className="h-4 w-4 text-green-500" />
      case 'inactive':
        return <AlertCircle className="h-4 w-4 text-yellow-500" />
      case 'failed':
        return <XCircle className="h-4 w-4 text-red-500" />
      default:
        return <AlertCircle className="h-4 w-4 text-gray-500" />
    }
  }

  const getQualityColor = (score?: number): string => {
    if (!score) return 'text-gray-400'
    if (score >= 0.8) return 'text-green-500'
    if (score >= 0.6) return 'text-yellow-500'
    if (score >= 0.4) return 'text-orange-500'
    return 'text-red-500'
  }

  const formatScore = (score?: number): string => {
    if (!score) return '--'
    return (score * 100).toFixed(0)
  }

  if (proxies.length === 0) {
    return (
      <div className="text-center py-12">
        <div className="text-gray-400 mb-4">
          <Activity className="h-12 w-12 mx-auto" />
        </div>
        <h3 className="text-lg font-medium text-gray-900 mb-2">暂无代理</h3>
        <p className="text-gray-500">没有找到符合条件的代理</p>
      </div>
    )
  }

  return (
    <div className="card">
      <div className="card-body p-0">
        {/* 表头 */}
        <div className="border-b border-gray-200 bg-gray-50 px-4 py-3">
          <div className="grid grid-cols-12 gap-4 items-center text-sm font-medium text-gray-700">
            <div className="col-span-1">
              <input
                type="checkbox"
                checked={selectedProxies.length === proxies.length && proxies.length > 0}
                onChange={onSelectAll}
                className="rounded border-gray-300 text-blue-600 focus:ring-blue-500"
              />
            </div>
            <div className="col-span-1">状态</div>
            <div className="col-span-3">地址</div>
            <div className="col-span-1">类型</div>
            <div className="col-span-2">位置</div>
            <div className="col-span-1">质量</div>
            <div className="col-span-1">响应</div>
            <div className="col-span-1">使用</div>
            <div className="col-span-1">操作</div>
          </div>
        </div>

        {/* 代理列表 */}
        <div className="divide-y divide-gray-200">
          {proxies.map(proxy => (
            <div
              key={proxy.id}
              className={`px-4 py-3 hover:bg-gray-50 transition-colors ${
                selectedProxies.includes(proxy.id) ? 'bg-blue-50' : ''
              }`}
            >
              <div className="grid grid-cols-12 gap-4 items-center text-sm">
                {/* 选择框 */}
                <div className="col-span-1">
                  <input
                    type="checkbox"
                    checked={selectedProxies.includes(proxy.id)}
                    onChange={() => onSelectProxy(proxy.id)}
                    className="rounded border-gray-300 text-blue-600 focus:ring-blue-500"
                  />
                </div>

                {/* 状态 */}
                <div className="col-span-1">
                  <div className="flex items-center">
                    {getStatusIcon(proxy.status)}
                  </div>
                </div>

                {/* 地址 */}
                <div className="col-span-3">
                  <div className="font-mono text-gray-900">
                    {proxy.host}:{proxy.port}
                  </div>
                  {proxy.username && (
                    <div className="text-xs text-gray-500">需要认证</div>
                  )}
                </div>

                {/* 类型 */}
                <div className="col-span-1">
                  <span className="uppercase font-medium text-gray-700">
                    {proxy.type}
                  </span>
                </div>

                {/* 位置 */}
                <div className="col-span-2">
                  {proxy.country_code ? (
                    <div className="flex items-center text-gray-700">
                      <MapPin className="h-3 w-3 mr-1 text-gray-400" />
                      <span className="truncate">
                        {proxy.country_code}
                        {proxy.city_name && ` - ${proxy.city_name}`}
                      </span>
                    </div>
                  ) : (
                    <span className="text-gray-400">--</span>
                  )}
                </div>

                {/* 质量评分 */}
                <div className="col-span-1">
                  <div className="flex items-center space-x-1">
                    <Star className="h-3 w-3 text-yellow-500" />
                    <span className={`font-medium ${getQualityColor(proxy.quality_score)}`}>
                      {formatScore(proxy.quality_score)}
                    </span>
                  </div>
                  {proxy.anonymity_level && (
                    <div className="text-xs text-gray-500 capitalize">
                      {proxy.anonymity_level === 'elite' ? '高匿' : 
                       proxy.anonymity_level === 'anonymous' ? '普匿' : 
                       proxy.anonymity_level === 'transparent' ? '透明' : '未知'}
                    </div>
                  )}
                </div>

                {/* 响应时间 */}
                <div className="col-span-1">
                  {proxy.response_time ? (
                    <div className="flex items-center text-gray-700">
                      <Zap className="h-3 w-3 mr-1 text-gray-400" />
                      <span className="text-xs">
                        {formatResponseTime(proxy.response_time)}
                      </span>
                    </div>
                  ) : (
                    <span className="text-gray-400">--</span>
                  )}
                </div>

                {/* 使用次数 */}
                <div className="col-span-1">
                  <div className="text-gray-700">
                    {proxy.use_count || 0}
                  </div>
                  {proxy.failures !== undefined && proxy.failures > 0 && (
                    <div className="text-xs text-red-500">
                      失败: {proxy.failures}
                    </div>
                  )}
                </div>

                {/* 操作按钮 */}
                <div className="col-span-1">
                  <div className="flex items-center space-x-1">
                    <button
                      onClick={() => onHealthCheck(proxy.id)}
                      disabled={operatingIds.has(proxy.id)}
                      className="p-1 text-gray-400 hover:text-blue-600 disabled:opacity-50"
                      title="健康检查"
                    >
                      {operatingIds.has(proxy.id) ? (
                        <div className="animate-spin rounded-full h-3 w-3 border-b-2 border-blue-600" />
                      ) : (
                        <Activity className="h-3 w-3" />
                      )}
                    </button>
                    <button
                      onClick={() => onQualityAssess(proxy.id)}
                      disabled={operatingIds.has(proxy.id)}
                      className="p-1 text-gray-400 hover:text-purple-600 disabled:opacity-50"
                      title="质量评估"
                    >
                      {operatingIds.has(proxy.id) ? (
                        <div className="animate-spin rounded-full h-3 w-3 border-b-2 border-purple-600" />
                      ) : (
                        <BarChart3 className="h-3 w-3" />
                      )}
                    </button>
                    <button
                      onClick={() => onDelete(proxy.id)}
                      disabled={operatingIds.has(proxy.id)}
                      className="p-1 text-gray-400 hover:text-red-600 disabled:opacity-50"
                      title="删除代理"
                    >
                      <Trash2 className="h-3 w-3" />
                    </button>
                  </div>
                </div>
              </div>

              {/* 标签行 */}
              {proxy.tags && proxy.tags.length > 0 && (
                <div className="mt-2 ml-8">
                  <div className="flex flex-wrap gap-1">
                    {proxy.tags.map(tag => (
                      <span
                        key={tag.id}
                        className="px-2 py-0.5 text-xs rounded border"
                        style={{
                          backgroundColor: tag.color + '20',
                          borderColor: tag.color,
                          color: tag.color
                        }}
                      >
                        {tag.name}
                      </span>
                    ))}
                  </div>
                </div>
              )}
            </div>
          ))}
        </div>
      </div>
    </div>
  )
}

export default ProxyCompactView
