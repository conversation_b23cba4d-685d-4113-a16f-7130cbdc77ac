import React, { useState } from 'react'
import { proxyAPI } from '../../services/api'
import { PROXY_TYPE_OPTIONS } from '../../utils/constants'
import { validateIP, validatePort } from '../../utils/helpers'
import toast from 'react-hot-toast'
import {  FileText, AlertCircle, CheckCircle, X, Globe,  } from 'lucide-react'
import type {  ProxyType } from '../../types'

interface BatchAddProxyFormProps {
  onSuccess: () => void
  onCancel: () => void
}

interface ParsedProxy {
  host: string
  port: number
  type: ProxyType
  username?: string
  password?: string
  weight?: number
  // 地理位置字段
  country_code?: string
  city_name?: string
  asn_name?: string
  asn_number?: number
  high_country_confidence?: boolean
  valid: boolean
  error?: string
  line: number
}

const BatchAddProxyForm: React.FC<BatchAddProxyFormProps> = ({ onSuccess, onCancel }) => {
  const [proxyText, setProxyText] = useState<string>('')
  const [defaultType, setDefaultType] = useState<ProxyType>('http')
  const [defaultWeight, setDefaultWeight] = useState<number>(1)
  const [parsedProxies, setParsedProxies] = useState<ParsedProxy[]>([])
  const [loading, setLoading] = useState<boolean>(false)
  const [showPreview, setShowPreview] = useState<boolean>(false)

  // 地理位置默认设置
  const [defaultCountryCode, setDefaultCountryCode] = useState<string>('')
  const [defaultCityName, setDefaultCityName] = useState<string>('')
  const [defaultASNName, setDefaultASNName] = useState<string>('')
  const [defaultASNNumber, setDefaultASNNumber] = useState<string>('')
  const [defaultHighConfidence, setDefaultHighConfidence] = useState<boolean>(false)

  // 解析代理文本
  const parseProxies = (text: string): ParsedProxy[] => {
    const lines = text.split('\n').filter(line => line.trim())
    const proxies: ParsedProxy[] = []

    lines.forEach((line, index) => {
      const trimmedLine = line.trim()
      if (!trimmedLine) return

      const proxy: ParsedProxy = {
        host: '',
        port: 0,
        type: defaultType,
        weight: defaultWeight,
        // 应用默认地理位置设置
        country_code: defaultCountryCode || undefined,
        city_name: defaultCityName || undefined,
        asn_name: defaultASNName || undefined,
        asn_number: defaultASNNumber ? parseInt(defaultASNNumber) : undefined,
        high_country_confidence: defaultHighConfidence,
        valid: false,
        line: index + 1
      }

      try {
        // 支持多种格式：
        // 1. protocol://username:password@host:port
        // 2. protocol://host:port
        // 3. host:port
        // 4. username:password@host:port

        let match: RegExpMatchArray | null

        // 格式1: protocol://username:password@host:port
        match = trimmedLine.match(/^(https?|socks[45]?):\/\/([^:]+):([^@]+)@([^:]+):(\d+)$/)
        if (match) {
          proxy.type = match[1] as ProxyType
          proxy.username = match[2]
          proxy.password = match[3]
          proxy.host = match[4]
          proxy.port = parseInt(match[5])
        } else {
          // 格式2: protocol://host:port
          match = trimmedLine.match(/^(https?|socks[45]?):\/\/([^:]+):(\d+)$/)
          if (match) {
            proxy.type = match[1] as ProxyType
            proxy.host = match[2]
            proxy.port = parseInt(match[3])
          } else {
            // 格式3: username:password@host:port
            match = trimmedLine.match(/^([^:]+):([^@]+)@([^:]+):(\d+)$/)
            if (match) {
              proxy.username = match[1]
              proxy.password = match[2]
              proxy.host = match[3]
              proxy.port = parseInt(match[4])
            } else {
              // 格式4: host:port
              match = trimmedLine.match(/^([^:]+):(\d+)$/)
              if (match) {
                proxy.host = match[1]
                proxy.port = parseInt(match[2])
              } else {
                proxy.error = '格式不正确'
              }
            }
          }
        }

        // 验证解析结果
        if (!proxy.error) {
          if (!proxy.host) {
            proxy.error = '主机地址为空'
          } else if (!validateIP(proxy.host) && !proxy.host.includes('.')) {
            proxy.error = '无效的主机地址'
          } else if (!validatePort(proxy.port.toString())) {
            proxy.error = '无效的端口号'
          } else {
            proxy.valid = true
          }
        }
      } catch (error) {
        proxy.error = '解析失败'
      }

      proxies.push(proxy)
    })

    return proxies
  }

  const handleTextChange = (e: React.ChangeEvent<HTMLTextAreaElement>) => {
    const text = e.target.value
    setProxyText(text)
    
    if (text.trim()) {
      const parsed = parseProxies(text)
      setParsedProxies(parsed)
      setShowPreview(true)
    } else {
      setParsedProxies([])
      setShowPreview(false)
    }
  }

  const handleSubmit = async () => {
    const validProxies = parsedProxies.filter(p => p.valid)
    
    if (validProxies.length === 0) {
      toast.error('没有有效的代理可以添加')
      return
    }

    try {
      setLoading(true)
      
      // 批量添加代理
      const promises = validProxies.map(proxy =>
        proxyAPI.addProxy({
          host: proxy.host,
          port: proxy.port,
          type: proxy.type,
          username: proxy.username || '',
          password: proxy.password || '',
          weight: proxy.weight || 1,
          // 地理位置字段
          country_code: proxy.country_code,
          city_name: proxy.city_name,
          asn_name: proxy.asn_name,
          asn_number: proxy.asn_number,
          high_country_confidence: proxy.high_country_confidence,
        })
      )

      await Promise.all(promises)
      
      toast.success(`成功添加 ${validProxies.length} 个代理`)
      onSuccess()
    } catch (error) {
      toast.error('批量添加代理失败')
    } finally {
      setLoading(false)
    }
  }

  const validCount = parsedProxies.filter(p => p.valid).length
  const invalidCount = parsedProxies.length - validCount

  return (
    <div className="space-y-6">
      {/* 默认设置 */}
      <div className="space-y-4">
        <h3 className="text-lg font-medium text-gray-900">默认设置</h3>

        <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
          <div>
            <label htmlFor="defaultType" className="block text-sm font-medium text-gray-700">
              默认代理类型
            </label>
            <select
              id="defaultType"
              value={defaultType}
              onChange={(e) => setDefaultType(e.target.value as ProxyType)}
              className="input"
            >
              {PROXY_TYPE_OPTIONS.map(option => (
                <option key={option.value} value={option.value}>
                  {option.label}
                </option>
              ))}
            </select>
          </div>

          <div>
            <label htmlFor="defaultWeight" className="block text-sm font-medium text-gray-700">
              默认权重
            </label>
            <input
              type="number"
              id="defaultWeight"
              min="1"
              max="100"
              value={defaultWeight}
              onChange={(e) => setDefaultWeight(parseInt(e.target.value) || 1)}
              className="input"
            />
          </div>
        </div>

        {/* 默认地理位置设置 */}
        <div className="border-t border-gray-200 pt-4">
          <div className="flex items-center gap-2 mb-4">
            <Globe className="h-5 w-5 text-blue-500" />
            <h4 className="text-sm font-medium text-gray-900">默认地理位置信息（可选）</h4>
          </div>

          <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
            <div>
              <label htmlFor="defaultCountryCode" className="block text-sm font-medium text-gray-700">
                默认国家代码
              </label>
              <input
                type="text"
                id="defaultCountryCode"
                value={defaultCountryCode}
                onChange={(e) => setDefaultCountryCode(e.target.value)}
                className="input"
                placeholder="US, CN, DE 等"
                maxLength={2}
              />
            </div>

            <div>
              <label htmlFor="defaultCityName" className="block text-sm font-medium text-gray-700">
                默认城市名称
              </label>
              <input
                type="text"
                id="defaultCityName"
                value={defaultCityName}
                onChange={(e) => setDefaultCityName(e.target.value)}
                className="input"
                placeholder="New York, Beijing 等"
              />
            </div>
          </div>

          <div className="grid grid-cols-1 md:grid-cols-2 gap-4 mt-4">
            <div>
              <label htmlFor="defaultASNName" className="block text-sm font-medium text-gray-700">
                默认ASN名称
              </label>
              <input
                type="text"
                id="defaultASNName"
                value={defaultASNName}
                onChange={(e) => setDefaultASNName(e.target.value)}
                className="input"
                placeholder="Amazon.com, Google LLC 等"
              />
            </div>

            <div>
              <label htmlFor="defaultASNNumber" className="block text-sm font-medium text-gray-700">
                默认ASN编号
              </label>
              <input
                type="number"
                id="defaultASNNumber"
                value={defaultASNNumber}
                onChange={(e) => setDefaultASNNumber(e.target.value)}
                className="input"
                placeholder="15169, 16509 等"
                min="1"
              />
            </div>
          </div>

          <div className="mt-4">
            <label className="flex items-center">
              <input
                type="checkbox"
                checked={defaultHighConfidence}
                onChange={(e) => setDefaultHighConfidence(e.target.checked)}
                className="rounded border-gray-300 text-blue-600 focus:ring-blue-500"
              />
              <span className="ml-2 text-sm text-gray-700">默认高置信度国家信息</span>
            </label>
          </div>
        </div>
      </div>

      {/* 格式说明 */}
      <div className="bg-blue-50 border border-blue-200 rounded-lg p-4">
        <div className="flex items-start">
          <FileText className="h-5 w-5 text-blue-600 mt-0.5 mr-3 flex-shrink-0" />
          <div>
            <h4 className="text-sm font-medium text-blue-900">支持的格式</h4>
            <div className="mt-2 text-sm text-blue-700 space-y-1">
              <div><code>protocol://username:password@host:port</code></div>
              <div><code>protocol://host:port</code></div>
              <div><code>username:password@host:port</code></div>
              <div><code>host:port</code></div>
            </div>
            <p className="mt-2 text-xs text-blue-600">
              每行一个代理，支持 http、https、socks4、socks5 协议。地理位置信息将使用上面设置的默认值。
            </p>
          </div>
        </div>
      </div>

      {/* 代理输入区域 */}
      <div>
        <label htmlFor="proxyText" className="block text-sm font-medium text-gray-700 mb-2">
          代理列表
        </label>
        <textarea
          id="proxyText"
          rows={10}
          className="input resize-none font-mono text-sm"
          placeholder="请输入代理列表，每行一个代理&#10;例如：&#10;*********************************&#10;https://proxy.example.com:3128&#10;***********:1080"
          value={proxyText}
          onChange={handleTextChange}
        />
      </div>

      {/* 解析预览 */}
      {showPreview && parsedProxies.length > 0 && (
        <div className="border border-gray-200 rounded-lg">
          <div className="px-4 py-3 border-b border-gray-200 bg-gray-50">
            <div className="flex items-center justify-between">
              <h4 className="text-sm font-medium text-gray-900">解析结果</h4>
              <div className="flex items-center space-x-4 text-sm">
                <span className="flex items-center text-green-600">
                  <CheckCircle className="h-4 w-4 mr-1" />
                  有效: {validCount}
                </span>
                {invalidCount > 0 && (
                  <span className="flex items-center text-red-600">
                    <AlertCircle className="h-4 w-4 mr-1" />
                    无效: {invalidCount}
                  </span>
                )}
              </div>
            </div>
          </div>
          
          <div className="max-h-64 overflow-y-auto">
            {parsedProxies.map((proxy, index) => (
              <div
                key={index}
                className={`px-4 py-3 border-b border-gray-100 last:border-b-0 ${
                  proxy.valid ? 'bg-white' : 'bg-red-50'
                }`}
              >
                <div className="flex items-center justify-between">
                  <div className="flex items-center space-x-3">
                    {proxy.valid ? (
                      <CheckCircle className="h-4 w-4 text-green-500" />
                    ) : (
                      <X className="h-4 w-4 text-red-500" />
                    )}
                    <span className="text-xs text-gray-500">第{proxy.line}行</span>
                    {proxy.valid ? (
                      <span className="text-sm font-mono">
                        {proxy.type}://{proxy.username && `${proxy.username}:***@`}{proxy.host}:{proxy.port}
                      </span>
                    ) : (
                      <span className="text-sm text-red-600">{proxy.error}</span>
                    )}
                  </div>
                </div>
              </div>
            ))}
          </div>
        </div>
      )}

      {/* 操作按钮 */}
      <div className="flex justify-end space-x-3 pt-4">
        <button
          type="button"
          onClick={onCancel}
          className="btn-outline"
        >
          取消
        </button>
        <button
          onClick={handleSubmit}
          disabled={loading || validCount === 0}
          className="btn-primary"
        >
          {loading ? '添加中...' : `批量添加 (${validCount})`}
        </button>
      </div>
    </div>
  )
}

export default BatchAddProxyForm
