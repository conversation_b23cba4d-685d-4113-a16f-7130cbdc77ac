import React, { useState } from 'react'
import {  Globe,  } from 'lucide-react'
import { proxyAPI } from '../../services/api'
import { PROXY_TYPE_OPTIONS } from '../../utils/constants'
import { validateIP, validatePort } from '../../utils/helpers'
import toast from 'react-hot-toast'
import type { ProxyFormData, FormErrors, ProxyType } from '../../types'

interface AddProxyFormProps {
  onSuccess: () => void
  onCancel: () => void
}

interface FormData extends Omit<ProxyFormData, 'port' | 'asn_number'> {
  port: string
  weight: number
  // 地理位置字段
  country_code: string
  city_name: string
  asn_name: string
  asn_number: string // 表单中使用字符串，提交时转换为数字
  high_country_confidence: boolean
}

const AddProxyForm: React.FC<AddProxyFormProps> = ({ onSuccess, onCancel }) => {
  const [formData, setFormData] = useState<FormData>({
    host: '',
    port: '',
    type: 'http' as ProxyType,
    username: '',
    password: '',
    weight: 1,
    // 地理位置字段
    country_code: '',
    city_name: '',
    asn_name: '',
    asn_number: '',
    high_country_confidence: false,
  })
  const [errors, setErrors] = useState<FormErrors<FormData>>({})
  const [loading, setLoading] = useState<boolean>(false)

  const handleChange = (e: React.ChangeEvent<HTMLInputElement | HTMLSelectElement>) => {
    const { name, value, type } = e.target
    const checked = (e.target as HTMLInputElement).checked
    const intProps = ['weight','port']

    setFormData(prev => ({
      ...prev,
      [name]: type === 'checkbox' ? checked :
              intProps.includes(name) ? parseInt(value) || 1 : value
    }))

    // 清除对应字段的错误
    if (errors[name as keyof FormData]) {
      setErrors(prev => ({
        ...prev,
        [name]: ''
      }))
    }
  }

  const validateForm = (): boolean => {
    const newErrors: FormErrors<FormData> = {}
    
    if (!formData.host.trim()) {
      newErrors.host = '请输入主机地址'
    } else if (!validateIP(formData.host) && !formData.host.includes('.')) {
      newErrors.host = '请输入有效的IP地址或域名'
    }
    
    if (!formData.port) {
      newErrors.port = '请输入端口号'
    } else if (!validatePort(formData.port)) {
      newErrors.port = '端口号必须在1-65535之间'
    }
    
    if (formData.weight && (formData.weight < 1 || formData.weight > 100)) {
      newErrors.weight = '权重必须在1-100之间'
    }
    
    setErrors(newErrors)
    return Object.keys(newErrors).length === 0
  }

  const handleSubmit = async (e: React.FormEvent<HTMLFormElement>) => {
    e.preventDefault()
    
    if (!validateForm()) return
    
    try {
      setLoading(true)
      await proxyAPI.addProxy({
        ...formData,
        port: parseInt(formData.port),
        weight: formData.weight || 1,
        asn_number: formData.asn_number ? parseInt(formData.asn_number) : undefined,
      })
      
      toast.success('代理添加成功')
      onSuccess()
    } catch (error) {
      toast.error('添加代理失败')
    } finally {
      setLoading(false)
    }
  }

  return (
    <form onSubmit={handleSubmit} className="space-y-4">
      <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
        <div>
          <label htmlFor="host" className="block text-sm font-medium text-gray-700">
            主机地址 *
          </label>
          <input
            type="text"
            id="host"
            name="host"
            required
            className={`input ${errors.host ? 'border-red-300 focus:border-red-500 focus:ring-red-500' : ''}`}
            placeholder="*********** 或 proxy.example.com"
            value={formData.host}
            onChange={handleChange}
          />
          {errors.host && (
            <p className="mt-1 text-sm text-red-600">{errors.host}</p>
          )}
        </div>

        <div>
          <label htmlFor="port" className="block text-sm font-medium text-gray-700">
            端口号 *
          </label>
          <input
            type="number"
            id="port"
            name="port"
            required
            min="1"
            max="65535"
            className={`input ${errors.port ? 'border-red-300 focus:border-red-500 focus:ring-red-500' : ''}`}
            placeholder="8080"
            value={formData.port}
            onChange={handleChange}
          />
          {errors.port && (
            <p className="mt-1 text-sm text-red-600">{errors.port}</p>
          )}
        </div>
      </div>

      <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
        <div>
          <label htmlFor="type" className="block text-sm font-medium text-gray-700">
            代理类型 *
          </label>
          <select
            id="type"
            name="type"
            required
            className="input"
            value={formData.type}
            onChange={handleChange}
          >
            {PROXY_TYPE_OPTIONS.map(option => (
              <option key={option.value} value={option.value}>
                {option.label}
              </option>
            ))}
          </select>
        </div>

        <div>
          <label htmlFor="weight" className="block text-sm font-medium text-gray-700">
            权重
          </label>
          <input
            type="number"
            id="weight"
            name="weight"
            min="1"
            max="100"
            className={`input ${errors.weight ? 'border-red-300 focus:border-red-500 focus:ring-red-500' : ''}`}
            placeholder="1"
            value={formData.weight}
            onChange={handleChange}
          />
          {errors.weight && (
            <p className="mt-1 text-sm text-red-600">{errors.weight}</p>
          )}
        </div>
      </div>

      <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
        <div>
          <label htmlFor="username" className="block text-sm font-medium text-gray-700">
            用户名
          </label>
          <input
            type="text"
            id="username"
            name="username"
            className="input"
            placeholder="可选"
            value={formData.username}
            onChange={handleChange}
          />
        </div>

        <div>
          <label htmlFor="password" className="block text-sm font-medium text-gray-700">
            密码
          </label>
          <input
            type="password"
            id="password"
            name="password"
            className="input"
            placeholder="可选"
            value={formData.password}
            onChange={handleChange}
          />
        </div>
      </div>

      {/* 地理位置信息 */}
      <div className="border-t border-gray-200 pt-4">
        <div className="flex items-center gap-2 mb-4">
          <Globe className="h-5 w-5 text-blue-500" />
          <h3 className="text-sm font-medium text-gray-900">地理位置信息（可选）</h3>
        </div>

        <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
          <div>
            <label htmlFor="country_code" className="block text-sm font-medium text-gray-700">
              国家代码
            </label>
            <input
              type="text"
              id="country_code"
              name="country_code"
              className="input"
              placeholder="US, CN, DE 等"
              value={formData.country_code}
              onChange={handleChange}
              maxLength={2}
            />
            <p className="mt-1 text-xs text-gray-500">ISO 3166-1 alpha-2 国家代码</p>
          </div>

          <div>
            <label htmlFor="city_name" className="block text-sm font-medium text-gray-700">
              城市名称
            </label>
            <input
              type="text"
              id="city_name"
              name="city_name"
              className="input"
              placeholder="New York, Beijing, Frankfurt 等"
              value={formData.city_name}
              onChange={handleChange}
            />
          </div>
        </div>

        <div className="grid grid-cols-1 md:grid-cols-2 gap-4 mt-4">
          <div>
            <label htmlFor="asn_name" className="block text-sm font-medium text-gray-700">
              ASN 名称
            </label>
            <input
              type="text"
              id="asn_name"
              name="asn_name"
              className="input"
              placeholder="Amazon.com, Google LLC 等"
              value={formData.asn_name}
              onChange={handleChange}
            />
          </div>

          <div>
            <label htmlFor="asn_number" className="block text-sm font-medium text-gray-700">
              ASN 编号
            </label>
            <input
              type="number"
              id="asn_number"
              name="asn_number"
              className="input"
              placeholder="15169, 16509 等"
              value={formData.asn_number}
              onChange={handleChange}
              min="1"
            />
          </div>
        </div>

        <div className="mt-4">
          <label className="flex items-center">
            <input
              type="checkbox"
              name="high_country_confidence"
              checked={formData.high_country_confidence}
              onChange={handleChange}
              className="rounded border-gray-300 text-blue-600 focus:ring-blue-500"
            />
            <span className="ml-2 text-sm text-gray-700">高置信度国家信息</span>
          </label>
          <p className="mt-1 text-xs text-gray-500">表示地理位置信息的准确性较高</p>
        </div>
      </div>

      <div className="flex justify-end space-x-3 pt-4">
        <button
          type="button"
          onClick={onCancel}
          className="btn-outline"
        >
          取消
        </button>
        <button
          type="submit"
          disabled={loading}
          className="btn-primary"
        >
          {loading ? '添加中...' : '添加代理'}
        </button>
      </div>
    </form>
  )
}

export default AddProxyForm
