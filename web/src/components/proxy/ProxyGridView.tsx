import React from 'react'
import {
  <PERSON><PERSON>,
  <PERSON><PERSON><PERSON>3,
  <PERSON>rash2,
  <PERSON><PERSON><PERSON>,
  <PERSON>,
  Zap,
  CheckCircle,
  XCircle,
  AlertCircle
} from 'lucide-react'
import QualityScoreDisplay from './QualityScoreDisplay'
import { formatDate, formatResponseTime } from '../../utils/helpers'
import type { Proxy } from '../../types'

interface ProxyGridViewProps {
  proxies: Proxy[]
  selectedProxies: string[]
  operatingIds: Set<string>
  onSelectProxy: (proxyId: string) => void
  onHealthCheck: (proxyId: string) => void
  onQualityAssess: (proxyId: string) => void
  onDelete: (proxyId: string) => void
}

const ProxyGridView: React.FC<ProxyGridViewProps> = ({
  proxies,
  selectedProxies,
  operatingIds,
  onSelectProxy,
  onHealthCheck,
  onQualityAssess,
  onDelete
}) => {
  const getStatusIcon = (status: string) => {
    switch (status) {
      case 'active':
        return <CheckCircle className="h-4 w-4 text-green-500" />
      case 'inactive':
        return <AlertCircle className="h-4 w-4 text-yellow-500" />
      case 'failed':
        return <XCircle className="h-4 w-4 text-red-500" />
      default:
        return <AlertCircle className="h-4 w-4 text-gray-500" />
    }
  }

  const getStatusColor = (status: string) => {
    switch (status) {
      case 'active':
        return 'bg-green-100 text-green-800 border-green-200'
      case 'inactive':
        return 'bg-yellow-100 text-yellow-800 border-yellow-200'
      case 'failed':
        return 'bg-red-100 text-red-800 border-red-200'
      default:
        return 'bg-gray-100 text-gray-800 border-gray-200'
    }
  }

  if (proxies.length === 0) {
    return (
      <div className="text-center py-12">
        <div className="text-gray-400 mb-4">
          <Activity className="h-12 w-12 mx-auto" />
        </div>
        <h3 className="text-lg font-medium text-gray-900 mb-2">暂无代理</h3>
        <p className="text-gray-500">没有找到符合条件的代理</p>
      </div>
    )
  }

  return (
    <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4 gap-4">
      {proxies.map(proxy => (
        <div
          key={proxy.id}
          className={`card hover:shadow-lg transition-all duration-200 ${
            selectedProxies.includes(proxy.id) 
              ? 'ring-2 ring-blue-500 bg-blue-50' 
              : ''
          }`}
        >
          {/* 卡片头部 */}
          <div className="card-header">
            <div className="flex items-center justify-between">
              <div className="flex items-center space-x-2">
                <input
                  type="checkbox"
                  checked={selectedProxies.includes(proxy.id)}
                  onChange={() => onSelectProxy(proxy.id)}
                  className="rounded border-gray-300 text-blue-600 focus:ring-blue-500"
                />
                {getStatusIcon(proxy.status)}
                <span className="font-medium text-gray-900 truncate">
                  {proxy.host}
                </span>
              </div>
              <span className={`px-2 py-1 text-xs rounded-full border ${getStatusColor(proxy.status)}`}>
                {proxy.status === 'active' ? '活跃' : proxy.status === 'inactive' ? '不活跃' : '失败'}
              </span>
            </div>
          </div>

          {/* 卡片内容 */}
          <div className="card-body space-y-3">
            {/* 基本信息 */}
            <div className="space-y-2">
              <div className="flex items-center justify-between text-sm">
                <span className="text-gray-600">地址:</span>
                <span className="font-mono text-gray-900">{proxy.host}:{proxy.port}</span>
              </div>
              <div className="flex items-center justify-between text-sm">
                <span className="text-gray-600">类型:</span>
                <span className="uppercase font-medium">{proxy.type}</span>
              </div>
              {proxy.country_code && (
                <div className="flex items-center justify-between text-sm">
                  <span className="text-gray-600 flex items-center">
                    <MapPin className="h-3 w-3 mr-1" />
                    位置:
                  </span>
                  <span>{proxy.country_code} {proxy.city_name && `- ${proxy.city_name}`}</span>
                </div>
              )}
            </div>

            {/* 质量评分 */}
            <div className="border-t pt-3">
              <QualityScoreDisplay proxy={proxy} />
            </div>

            {/* 性能指标 */}
            <div className="border-t pt-3 space-y-2">
              {proxy.response_time && (
                <div className="flex items-center justify-between text-sm">
                  <span className="text-gray-600 flex items-center">
                    <Zap className="h-3 w-3 mr-1" />
                    响应时间:
                  </span>
                  <span>{formatResponseTime(proxy.response_time)}</span>
                </div>
              )}
              <div className="flex items-center justify-between text-sm">
                <span className="text-gray-600">使用次数:</span>
                <span>{proxy.use_count || 0}</span>
              </div>
              {proxy.failures !== undefined && proxy.failures > 0 && (
                <div className="flex items-center justify-between text-sm">
                  <span className="text-red-600">失败次数:</span>
                  <span className="text-red-600">{proxy.failures}</span>
                </div>
              )}
            </div>

            {/* 标签 */}
            {proxy.tags && proxy.tags.length > 0 && (
              <div className="border-t pt-3">
                <div className="flex flex-wrap gap-1">
                  {proxy.tags.slice(0, 3).map(tag => (
                    <span
                      key={tag.id}
                      className="px-2 py-1 text-xs rounded border"
                      style={{
                        backgroundColor: tag.color + '20',
                        borderColor: tag.color,
                        color: tag.color
                      }}
                    >
                      {tag.name}
                    </span>
                  ))}
                  {proxy.tags.length > 3 && (
                    <span className="px-2 py-1 text-xs rounded border border-gray-300 text-gray-600">
                      +{proxy.tags.length - 3}
                    </span>
                  )}
                </div>
              </div>
            )}

            {/* 最后检查时间 */}
            {proxy.last_check && (
              <div className="text-xs text-gray-500 flex items-center">
                <Clock className="h-3 w-3 mr-1" />
                最后检查: {formatDate(proxy.last_check)}
              </div>
            )}
          </div>

          {/* 操作按钮 */}
          <div className="card-footer">
            <div className="flex items-center justify-between">
              <div className="flex space-x-1">
                <button
                  onClick={() => onHealthCheck(proxy.id)}
                  disabled={operatingIds.has(proxy.id)}
                  className="btn-xs btn-outline"
                  title="健康检查"
                >
                  {operatingIds.has(proxy.id) ? (
                    <div className="animate-spin rounded-full h-3 w-3 border-b-2 border-blue-600" />
                  ) : (
                    <Activity className="h-3 w-3" />
                  )}
                </button>
                <button
                  onClick={() => onQualityAssess(proxy.id)}
                  disabled={operatingIds.has(proxy.id)}
                  className="btn-xs btn-outline"
                  title="质量评估"
                >
                  {operatingIds.has(proxy.id) ? (
                    <div className="animate-spin rounded-full h-3 w-3 border-b-2 border-purple-600" />
                  ) : (
                    <BarChart3 className="h-3 w-3" />
                  )}
                </button>
              </div>
              <button
                onClick={() => onDelete(proxy.id)}
                disabled={operatingIds.has(proxy.id)}
                className="btn-xs btn-danger"
                title="删除代理"
              >
                <Trash2 className="h-3 w-3" />
              </button>
            </div>
          </div>
        </div>
      ))}
    </div>
  )
}

export default ProxyGridView
