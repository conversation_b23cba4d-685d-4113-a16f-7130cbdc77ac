import React from 'react'
import { Star, Shield, Zap, Activity, Eye } from 'lucide-react'
import type { Proxy } from '../../types'

interface QualityScoreDisplayProps {
  proxy: Proxy
  showDetails?: boolean
}

const QualityScoreDisplay: React.FC<QualityScoreDisplayProps> = ({ 
  proxy, 
  showDetails = false 
}) => {
  // 获取质量评分颜色
  const getScoreColor = (score?: number): string => {
    if (!score) return 'text-gray-400'
    if (score >= 0.8) return 'text-green-500'
    if (score >= 0.6) return 'text-yellow-500'
    if (score >= 0.4) return 'text-orange-500'
    return 'text-red-500'
  }

  // 获取匿名等级颜色和图标
  const getAnonymityInfo = (level?: string) => {
    switch (level) {
      case 'elite':
        return { color: 'text-green-500', label: '高匿', icon: Shield }
      case 'anonymous':
        return { color: 'text-yellow-500', label: '普匿', icon: Eye }
      case 'transparent':
        return { color: 'text-red-500', label: '透明', icon: Eye }
      default:
        return { color: 'text-gray-400', label: '未知', icon: Eye }
    }
  }

  // 格式化评分显示
  const formatScore = (score?: number): string => {
    if (!score) return '--'
    return (score * 100).toFixed(0)
  }

  const anonymityInfo = getAnonymityInfo(proxy.anonymity_level)
  const AnonymityIcon = anonymityInfo.icon

  if (!showDetails) {
    // 简化显示模式
    return (
      <div className="flex items-center space-x-2">
        <div className="flex items-center space-x-1">
          <Star className="h-4 w-4 text-yellow-500" />
          <span className={`text-sm font-medium ${getScoreColor(proxy.quality_score)}`}>
            {formatScore(proxy.quality_score)}
          </span>
        </div>
        {proxy.anonymity_level && (
          <div className="flex items-center space-x-1">
            <AnonymityIcon className={`h-4 w-4 ${anonymityInfo.color}`} />
            <span className={`text-xs ${anonymityInfo.color}`}>
              {anonymityInfo.label}
            </span>
          </div>
        )}
      </div>
    )
  }

  // 详细显示模式
  return (
    <div className="space-y-3">
      {/* 综合评分 */}
      <div className="flex items-center justify-between">
        <span className="text-sm font-medium text-gray-700">综合评分</span>
        <div className="flex items-center space-x-1">
          <Star className="h-4 w-4 text-yellow-500" />
          <span className={`text-sm font-bold ${getScoreColor(proxy.quality_score)}`}>
            {formatScore(proxy.quality_score)}
          </span>
        </div>
      </div>

      {/* 详细评分 */}
      <div className="grid grid-cols-2 gap-3">
        {/* 速度评分 */}
        <div className="flex items-center justify-between">
          <div className="flex items-center space-x-1">
            <Zap className="h-3 w-3 text-blue-500" />
            <span className="text-xs text-gray-600">速度</span>
          </div>
          <span className={`text-xs font-medium ${getScoreColor(proxy.speed_score)}`}>
            {formatScore(proxy.speed_score)}
          </span>
        </div>

        {/* 稳定性评分 */}
        <div className="flex items-center justify-between">
          <div className="flex items-center space-x-1">
            <Activity className="h-3 w-3 text-green-500" />
            <span className="text-xs text-gray-600">稳定</span>
          </div>
          <span className={`text-xs font-medium ${getScoreColor(proxy.stability_score)}`}>
            {formatScore(proxy.stability_score)}
          </span>
        </div>

        {/* 可靠性评分 */}
        <div className="flex items-center justify-between">
          <div className="flex items-center space-x-1">
            <Shield className="h-3 w-3 text-purple-500" />
            <span className="text-xs text-gray-600">可靠</span>
          </div>
          <span className={`text-xs font-medium ${getScoreColor(proxy.reliability_score)}`}>
            {formatScore(proxy.reliability_score)}
          </span>
        </div>

        {/* 匿名等级 */}
        <div className="flex items-center justify-between">
          <div className="flex items-center space-x-1">
            <AnonymityIcon className={`h-3 w-3 ${anonymityInfo.color}`} />
            <span className="text-xs text-gray-600">匿名</span>
          </div>
          <span className={`text-xs font-medium ${anonymityInfo.color}`}>
            {anonymityInfo.label}
          </span>
        </div>
      </div>

      {/* 最后评估时间 */}
      {proxy.last_quality_check && (
        <div className="text-xs text-gray-500 border-t pt-2">
          最后评估: {new Date(proxy.last_quality_check).toLocaleString()}
        </div>
      )}
    </div>
  )
}

export default QualityScoreDisplay
