import React, { useState, useEffect } from 'react';
import {
  Key,
  Plus,
  Copy,
  Eye,
  EyeOff,
  Trash2,
  Edit,
  MoreVertical,
  Calendar,
  Activity,
  Shield,
  AlertTriangle
} from 'lucide-react';
import { APIKeyInfo, APIKeyResponse, CreateAPIKeyRequest } from '../types';
import { apiKeyAPI } from '../services/api';
import LoadingSpinner from './common/LoadingSpinner';
import Modal from './common/Modal';
import { enhancedToast } from '../utils/toastConfig';

interface EnhancedAPIKeyManagerProps {
  className?: string;
}

export const EnhancedAPIKeyManager: React.FC<EnhancedAPIKeyManagerProps> = ({ className = '' }) => {
  const [apiKeys, setApiKeys] = useState<APIKeyInfo[]>([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [actionLoading, setActionLoading] = useState(false);
  
  // 模态框状态
  const [showCreateModal, setShowCreateModal] = useState(false);
  const [showDeleteModal, setShowDeleteModal] = useState(false);
  const [showEditModal, setShowEditModal] = useState(false);
  const [selectedKey, setSelectedKey] = useState<APIKeyInfo | null>(null);
  
  // 表单状态
  const [createForm, setCreateForm] = useState<CreateAPIKeyRequest>({
    name: '',
    expires_in_days: undefined
  });
  const [editForm, setEditForm] = useState({ name: '', is_active: true });
  
  // 显示完整密钥的状态
  const [visibleKeys, setVisibleKeys] = useState<Set<string>>(new Set());
  const [fullKeys, setFullKeys] = useState<Map<string, string>>(new Map());

  useEffect(() => {
    loadAPIKeys();
  }, []);

  const loadAPIKeys = async () => {
    setLoading(true);
    setError(null);

    try {
      const response = await apiKeyAPI.getAPIKeys();
      setApiKeys(response.data.data || []);
    } catch (error: any) {
      console.error('Failed to load API keys:', error);
      // 如果新API不存在，尝试使用旧API
      try {
        const oldResponse = await apiKeyAPI.getAPIKey();
        if (oldResponse.data.data) {
          setApiKeys([{
            id: 'legacy',
            name: 'Legacy API Key',
            ...oldResponse.data.data,
            is_active: true
          }]);
        } else {
          setApiKeys([]);
        }
      } catch (oldError) {
        setApiKeys([]);
        setError('获取API密钥失败');
      }
    } finally {
      setLoading(false);
    }
  };

  const handleCreateAPIKey = async () => {
    if (!createForm.name.trim()) {
      enhancedToast.error('请输入API密钥名称');
      return;
    }

    setActionLoading(true);
    try {
      const response = await apiKeyAPI.createAPIKey(createForm);
      if (response.data.data) {
        const newKey = response.data.data;
        setApiKeys(prev => [...prev, newKey]);
        setFullKeys(prev => new Map(prev).set(newKey.id, newKey.api_key));
        // 自动显示新创建的密钥
        setVisibleKeys(prev => new Set(prev).add(newKey.id));
        setShowCreateModal(false);
        setCreateForm({ name: '', expires_in_days: undefined });
        enhancedToast.success('API密钥创建成功！请妥善保管您的密钥，这是唯一一次显示完整密钥的机会。');
      }
    } catch (error: any) {
      console.error('Failed to create API key:', error);
      enhancedToast.error('创建API密钥失败');
    } finally {
      setActionLoading(false);
    }
  };

  const handleUpdateAPIKey = async () => {
    if (!selectedKey) return;

    setActionLoading(true);
    try {
      const response = await apiKeyAPI.updateAPIKey(selectedKey.id, editForm);
      if (response.data.data) {
        setApiKeys(prev => prev.map(key => 
          key.id === selectedKey.id ? response.data.data! : key
        ));
        setShowEditModal(false);
        setSelectedKey(null);
        enhancedToast.success('API密钥更新成功');
      }
    } catch (error: any) {
      console.error('Failed to update API key:', error);
      enhancedToast.error('更新API密钥失败');
    } finally {
      setActionLoading(false);
    }
  };

  const handleDeleteAPIKey = async () => {
    if (!selectedKey) return;

    setActionLoading(true);
    try {
      await apiKeyAPI.deleteAPIKey(selectedKey.id);
      setApiKeys(prev => prev.filter(key => key.id !== selectedKey.id));
      setShowDeleteModal(false);
      setSelectedKey(null);
      enhancedToast.success('API密钥删除成功');
    } catch (error: any) {
      console.error('Failed to delete API key:', error);
      enhancedToast.error('删除API密钥失败');
    } finally {
      setActionLoading(false);
    }
  };

  const toggleKeyVisibility = (keyId: string) => {
    const newVisible = new Set(visibleKeys);
    if (newVisible.has(keyId)) {
      newVisible.delete(keyId);
    } else {
      newVisible.add(keyId);
    }
    setVisibleKeys(newVisible);
  };

  const copyToClipboard = async (text: string) => {
    try {
      await navigator.clipboard.writeText(text);
      enhancedToast.success('已复制到剪贴板');
    } catch (error) {
      enhancedToast.error('复制失败');
    }
  };

  const formatDate = (dateString: string): string => {
    if (!dateString || dateString === '0001-01-01T00:00:00Z') {
      return '从未使用';
    }
    return new Date(dateString).toLocaleString('zh-CN');
  };

  const isExpired = (expiresAt?: string): boolean => {
    if (!expiresAt) return false;
    return new Date(expiresAt) < new Date();
  };

  if (loading) {
    return (
      <div className={`bg-white rounded-lg shadow-sm border p-6 ${className}`}>
        <div className="flex items-center justify-center py-8">
          <LoadingSpinner size="md" text="加载API密钥..." />
        </div>
      </div>
    );
  }

  return (
    <div className={`bg-white rounded-lg shadow-sm border ${className}`}>
      {/* 头部 */}
      <div className="p-6 border-b border-gray-200">
        <div className="flex items-center justify-between">
          <div>
            <h2 className="text-lg font-semibold text-gray-900">API密钥管理</h2>
            <p className="text-sm text-gray-600 mt-1">
              管理您的API密钥以进行程序化访问
            </p>
          </div>
          <button
            onClick={() => setShowCreateModal(true)}
            className="btn-primary"
          >
            <Plus className="h-4 w-4 mr-2" />
            创建密钥
          </button>
        </div>
      </div>

      {/* 错误提示 */}
      {error && (
        <div className="p-4 bg-red-50 border-l-4 border-red-400">
          <div className="flex">
            <AlertTriangle className="h-5 w-5 text-red-400" />
            <div className="ml-3">
              <p className="text-sm text-red-700">{error}</p>
            </div>
          </div>
        </div>
      )}

      {/* API密钥列表 */}
      <div className="p-6">
        {apiKeys.length === 0 ? (
          <div className="text-center py-12">
            <div className="bg-gray-50 rounded-full w-20 h-20 flex items-center justify-center mx-auto mb-6">
              <Key className="h-10 w-10 text-gray-400" />
            </div>
            <h3 className="text-xl font-semibold text-gray-900 mb-3">暂无API密钥</h3>
            <p className="text-gray-600 mb-8 max-w-md mx-auto leading-relaxed">
              您还没有API密钥。创建一个API密钥来开始使用程序化接口访问系统功能。
            </p>
            <button
              onClick={() => setShowCreateModal(true)}
              className="btn-primary"
            >
              <Plus className="h-5 w-5 mr-2" />
              创建第一个API密钥
            </button>
          </div>
        ) : (
          <div className="space-y-4">
            {apiKeys.map((apiKey) => (
              <div
                key={apiKey.id}
                className={`border rounded-lg p-4 ${
                  !apiKey.is_active ? 'bg-gray-50 border-gray-200' : 
                  isExpired(apiKey.expires_at) ? 'bg-red-50 border-red-200' : 'border-gray-200'
                }`}
              >
                <div className="flex items-center justify-between">
                  <div className="flex-1">
                    <div className="flex items-center space-x-3">
                      <h4 className="font-medium text-gray-900">{apiKey.name}</h4>
                      {!apiKey.is_active && (
                        <span className="px-2 py-1 text-xs bg-gray-100 text-gray-600 rounded">
                          已禁用
                        </span>
                      )}
                      {isExpired(apiKey.expires_at) && (
                        <span className="px-2 py-1 text-xs bg-red-100 text-red-600 rounded">
                          已过期
                        </span>
                      )}
                    </div>
                    
                    <div className="mt-2">
                      <div className="flex items-center space-x-2">
                        <code className={`text-sm px-2 py-1 rounded font-mono ${
                          visibleKeys.has(apiKey.id) && fullKeys.has(apiKey.id)
                            ? 'bg-yellow-50 border border-yellow-200 text-yellow-800'
                            : 'bg-gray-100'
                        }`}>
                          {visibleKeys.has(apiKey.id) && fullKeys.has(apiKey.id)
                            ? fullKeys.get(apiKey.id)
                            : apiKey.masked_api_key
                          }
                        </code>
                      <button
                        onClick={() => toggleKeyVisibility(apiKey.id)}
                        className="p-1 text-gray-500 hover:text-gray-700"
                        title={visibleKeys.has(apiKey.id) ? '隐藏密钥' : '显示密钥'}
                      >
                        {visibleKeys.has(apiKey.id) ? (
                          <EyeOff className="h-4 w-4" />
                        ) : (
                          <Eye className="h-4 w-4" />
                        )}
                      </button>
                      <button
                        onClick={() => copyToClipboard(
                          visibleKeys.has(apiKey.id) && fullKeys.has(apiKey.id)
                            ? fullKeys.get(apiKey.id)!
                            : apiKey.masked_api_key
                        )}
                        className="p-1 text-gray-500 hover:text-gray-700"
                        title="复制密钥"
                      >
                        <Copy className="h-4 w-4" />
                      </button>
                      </div>
                      {visibleKeys.has(apiKey.id) && fullKeys.has(apiKey.id) && (
                        <div className="mt-2 text-xs text-yellow-600 bg-yellow-50 px-2 py-1 rounded border border-yellow-200">
                          ⚠️ 完整密钥已显示，请妥善保管并避免泄露
                        </div>
                      )}
                    </div>

                    <div className="mt-3 grid grid-cols-2 md:grid-cols-4 gap-4 text-sm text-gray-600">
                      <div className="flex items-center">
                        <Calendar className="h-4 w-4 mr-1" />
                        <span>创建: {formatDate(apiKey.api_key_created)}</span>
                      </div>
                      <div className="flex items-center">
                        <Activity className="h-4 w-4 mr-1" />
                        <span>最后使用: {formatDate(apiKey.api_key_last_used)}</span>
                      </div>
                      <div className="flex items-center">
                        <Shield className="h-4 w-4 mr-1" />
                        <span>使用次数: {apiKey.api_key_usage_count}</span>
                      </div>
                      {apiKey.expires_at && (
                        <div className="flex items-center">
                          <Calendar className="h-4 w-4 mr-1" />
                          <span>过期: {formatDate(apiKey.expires_at)}</span>
                        </div>
                      )}
                    </div>
                  </div>

                  <div className="flex items-center space-x-2">
                    <button
                      onClick={() => {
                        setSelectedKey(apiKey);
                        setEditForm({ name: apiKey.name, is_active: apiKey.is_active });
                        setShowEditModal(true);
                      }}
                      className="p-2 text-gray-500 hover:text-gray-700 hover:bg-gray-100 rounded"
                      title="编辑"
                    >
                      <Edit className="h-4 w-4" />
                    </button>
                    <button
                      onClick={() => {
                        setSelectedKey(apiKey);
                        setShowDeleteModal(true);
                      }}
                      className="p-2 text-red-500 hover:text-red-700 hover:bg-red-100 rounded"
                      title="删除"
                    >
                      <Trash2 className="h-4 w-4" />
                    </button>
                  </div>
                </div>
              </div>
            ))}
          </div>
        )}
      </div>

      {/* 创建API密钥模态框 */}
      <Modal
        isOpen={showCreateModal}
        onClose={() => setShowCreateModal(false)}
        title="创建API密钥"
        size="md"
      >
        <div className="space-y-4">
          <div>
            <label className="block text-sm font-medium text-gray-700 mb-1">
              密钥名称 *
            </label>
            <input
              type="text"
              className="input w-full"
              value={createForm.name}
              onChange={(e) => setCreateForm(prev => ({ ...prev, name: e.target.value }))}
              placeholder="输入API密钥名称"
            />
          </div>

          <div>
            <label className="block text-sm font-medium text-gray-700 mb-1">
              过期时间 (可选)
            </label>
            <select
              className="input w-full"
              value={createForm.expires_in_days || ''}
              onChange={(e) => setCreateForm(prev => ({ 
                ...prev, 
                expires_in_days: e.target.value ? Number(e.target.value) : undefined 
              }))}
            >
              <option value="">永不过期</option>
              <option value="30">30天</option>
              <option value="90">90天</option>
              <option value="180">180天</option>
              <option value="365">1年</option>
            </select>
          </div>

          <div className="flex justify-end space-x-3 pt-4">
            <button
              onClick={() => setShowCreateModal(false)}
              className="btn-outline"
              disabled={actionLoading}
            >
              取消
            </button>
            <button
              onClick={handleCreateAPIKey}
              className="btn-primary"
              disabled={actionLoading}
            >
              {actionLoading ? '创建中...' : '创建'}
            </button>
          </div>
        </div>
      </Modal>

      {/* 编辑API密钥模态框 */}
      <Modal
        isOpen={showEditModal}
        onClose={() => setShowEditModal(false)}
        title="编辑API密钥"
        size="md"
      >
        <div className="space-y-4">
          <div>
            <label className="block text-sm font-medium text-gray-700 mb-1">
              密钥名称
            </label>
            <input
              type="text"
              className="input w-full"
              value={editForm.name}
              onChange={(e) => setEditForm(prev => ({ ...prev, name: e.target.value }))}
            />
          </div>

          <div>
            <label className="flex items-center">
              <input
                type="checkbox"
                checked={editForm.is_active}
                onChange={(e) => setEditForm(prev => ({ ...prev, is_active: e.target.checked }))}
                className="mr-2"
              />
              <span className="text-sm text-gray-700">启用此API密钥</span>
            </label>
          </div>

          <div className="flex justify-end space-x-3 pt-4">
            <button
              onClick={() => setShowEditModal(false)}
              className="btn-outline"
              disabled={actionLoading}
            >
              取消
            </button>
            <button
              onClick={handleUpdateAPIKey}
              className="btn-primary"
              disabled={actionLoading}
            >
              {actionLoading ? '更新中...' : '更新'}
            </button>
          </div>
        </div>
      </Modal>

      {/* 删除确认模态框 */}
      <Modal
        isOpen={showDeleteModal}
        onClose={() => setShowDeleteModal(false)}
        title="删除API密钥"
        size="md"
      >
        <div className="space-y-4">
          <p className="text-gray-700">
            确定要删除API密钥 "<strong>{selectedKey?.name}</strong>" 吗？
          </p>
          <p className="text-sm text-red-600">
            此操作无法撤销，使用此密钥的应用程序将无法继续访问API。
          </p>

          <div className="flex justify-end space-x-3 pt-4">
            <button
              onClick={() => setShowDeleteModal(false)}
              className="btn-outline"
              disabled={actionLoading}
            >
              取消
            </button>
            <button
              onClick={handleDeleteAPIKey}
              className="btn-danger"
              disabled={actionLoading}
            >
              {actionLoading ? '删除中...' : '删除'}
            </button>
          </div>
        </div>
      </Modal>
    </div>
  );
};

export default EnhancedAPIKeyManager;
