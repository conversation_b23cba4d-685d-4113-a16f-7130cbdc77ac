import React, { useState } from 'react'
import { <PERSON>u, Bell, User, LogOut, Settings } from 'lucide-react'
import { useAuth } from '../../contexts/AuthContext'
import { useNavigate } from 'react-router-dom'

interface HeaderProps {
  setSidebarOpen: (open: boolean) => void
}

const Header: React.FC<HeaderProps> = ({ setSidebarOpen }) => {
  const { user, logout } = useAuth()
  const navigate = useNavigate()
  const [dropdownOpen, setDropdownOpen] = useState<boolean>(false)

  const handleLogout = (): void => {
    logout()
    setDropdownOpen(false)
  }

  const handleProfileClick = (): void => {
    navigate('/settings?tab=profile')
    setDropdownOpen(false)
  }

  const handleSettingsClick = (): void => {
    navigate('/settings')
    setDropdownOpen(false)
  }

  return (
    <div className="relative z-10 flex-shrink-0 flex h-16 bg-white shadow">
      {/* 移动端菜单按钮 */}
      <button
        className="px-4 border-r border-gray-200 text-gray-500 focus:outline-none focus:ring-2 focus:ring-inset focus:ring-primary-500 md:hidden"
        onClick={() => setSidebarOpen(true)}
      >
        <Menu className="h-6 w-6" />
      </button>

      <div className="flex-1 px-4 flex justify-between container-max-width">
        {/* 左侧内容 */}
        <div className="flex-1 flex items-center">
          <div className="w-full flex md:ml-0">
            {/* 可以在这里添加搜索框或其他内容 */}
          </div>
        </div>

        {/* 右侧内容 */}
        <div className="ml-4 flex items-center md:ml-6 space-x-4">
          {/* 通知按钮 */}
          {/* <button className="bg-white p-1 rounded-full text-gray-400 hover:text-gray-500 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-primary-500">
            <Bell className="h-6 w-6" />
          </button> */}

          {/* 用户菜单 */}
          <div className="ml-3 relative">
            <div>
              <button
                className="max-w-xs bg-white flex items-center text-sm rounded-full focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-primary-500 hover:bg-gray-50 transition-colors p-1"
                onClick={() => setDropdownOpen(!dropdownOpen)}
                title="用户菜单"
              >
                <div className="h-8 w-8 bg-primary-100 rounded-full flex items-center justify-center">
                  <span className="text-sm font-medium text-primary-600">
                    {user?.username?.charAt(0).toUpperCase()}
                  </span>
                </div>
                <span className="ml-2 text-sm font-medium text-gray-700 hidden sm:block max-w-32 truncate">
                  {user?.username}
                </span>
              </button>
            </div>

            {/* 下拉菜单 */}
            {dropdownOpen && (
              <div className="origin-top-right absolute right-0 mt-2 w-48 rounded-md shadow-lg py-1 bg-white ring-1 ring-black ring-opacity-5 focus:outline-none z-50">
                <div className="px-4 py-2 text-sm text-gray-700 border-b border-gray-100">
                  <p className="font-medium">{user?.username}</p>
                  <p className="text-xs text-gray-500">{user?.email}</p>
                </div>
                
                <button
                  className="flex items-center w-full px-4 py-2 text-sm text-gray-700 hover:bg-gray-100"
                  onClick={handleProfileClick}
                >
                  <User className="mr-3 h-4 w-4" />
                  个人资料
                </button>

                <button
                  className="flex items-center w-full px-4 py-2 text-sm text-gray-700 hover:bg-gray-100"
                  onClick={handleSettingsClick}
                >
                  <Settings className="mr-3 h-4 w-4" />
                  设置
                </button>
                
                <button
                  className="flex items-center w-full px-4 py-2 text-sm text-gray-700 hover:bg-gray-100"
                  onClick={handleLogout}
                >
                  <LogOut className="mr-3 h-4 w-4" />
                  退出登录
                </button>
              </div>
            )}
          </div>
        </div>
      </div>

      {/* 点击外部关闭下拉菜单 */}
      {dropdownOpen && (
        <div
          className="fixed inset-0 z-40"
          onClick={() => setDropdownOpen(false)}
        />
      )}
    </div>
  )
}

export default Header
