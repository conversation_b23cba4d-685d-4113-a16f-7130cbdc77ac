import React, { useState } from 'react'
import { Outlet } from 'react-router-dom'
import Sidebar from './Sidebar'
import Header from './Header'
import Footer from './Footer'

const Layout: React.FC = () => {
  const [sidebarOpen, setSidebarOpen] = useState<boolean>(false)

  return (
    <div className="h-screen flex overflow-hidden bg-gray-100">
      {/* 移动端侧边栏遮罩 */}
      {sidebarOpen && (
        <div 
          className="fixed inset-0 flex z-40 md:hidden"
          onClick={() => setSidebarOpen(false)}
        >
          <div className="fixed inset-0 bg-gray-600 bg-opacity-75" />
        </div>
      )}

      {/* 侧边栏 */}
      <Sidebar 
        sidebarOpen={sidebarOpen} 
        setSidebarOpen={setSidebarOpen} 
      />

      {/* 主内容区域 */}
      <div className="flex flex-col w-0 flex-1 overflow-hidden">
        {/* 顶部导航 */}
        <Header setSidebarOpen={setSidebarOpen} />
        
        {/* 主内容 */}
        <main className="flex-1 relative overflow-y-auto focus:outline-none flex flex-col">
          <div className="py-6 flex-1">
            <div className="container-max-width px-4 sm:px-6 md:px-8">
              <Outlet />
            </div>
          </div>
          <Footer />
        </main>
      </div>
    </div>
  )
}

export default Layout
