import React from 'react'
import { Github, ExternalLink } from 'lucide-react'

const Footer: React.FC = () => {
  // 可以通过环境变量配置GitHub链接
  const githubUrl = import.meta.env.VITE_GITHUB_URL || 'https://github.com/your-username/proxyflow'
  const currentYear = new Date().getFullYear()

  return (
    <footer className="bg-white border-t border-gray-200 mt-auto">
      <div className="container-max-width px-4 sm:px-6 md:px-8 py-6">
        <div className="flex flex-col md:flex-row md:items-center md:justify-between">
          {/* 左侧信息 */}
          <div className="flex flex-col md:flex-row md:items-center space-y-2 md:space-y-0 md:space-x-6">
            <div className="flex items-center">
              <img 
                src="/logo.svg" 
                alt="ProxyFlow Logo" 
                className="h-6 w-6 mr-2"
              />
              <span className="text-sm font-medium text-gray-900">ProxyFlow</span>
            </div>
            <p className="text-sm text-gray-500">
              高性能代理服务管理系统
            </p>
          </div>

          {/* 右侧链接 */}
          <div className="flex items-center space-x-4 mt-4 md:mt-0">
            <span className="text-sm text-gray-500">
              © {currentYear} ProxyFlow
            </span>
            <a
              href={githubUrl}
              target="_blank"
              rel="noopener noreferrer"
              className="flex items-center text-sm text-gray-600 hover:text-gray-900 transition-colors"
              title="查看源码"
            >
              <Github className="h-4 w-4 mr-1" />
              源码
              <ExternalLink className="h-3 w-3 ml-1" />
            </a>
          </div>
        </div>
      </div>
    </footer>
  )
}

export default Footer
