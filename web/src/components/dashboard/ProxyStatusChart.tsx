import React from 'react'
import { <PERSON><PERSON><PERSON>, Pie, Cell, Responsive<PERSON><PERSON><PERSON>, Legend, Tooltip } from 'recharts'
import type { ProxyStats } from '../../types'

interface ProxyStatusChartProps {
  data: ProxyStats
}

interface ChartDataItem {
  name: string
  value: number
  color: string
}

const ProxyStatusChart: React.FC<ProxyStatusChartProps> = ({ data }) => {
  const chartData: ChartDataItem[] = [
    { name: '正常', value: data.active || 0, color: '#22c55e' },
    { name: '未激活', value: data.inactive || 0, color: '#f59e0b' },
    { name: '失败', value: data.failed || 0, color: '#ef4444' },
  ].filter(item => item.value > 0)

  if (chartData.length === 0) {
    return (
      <div className="flex items-center justify-center h-64">
        <p className="text-gray-500">暂无数据</p>
      </div>
    )
  }

  const CustomTooltip = ({ active, payload }: any) => {
    if (active && payload && payload.length) {
      const data = payload[0]
      return (
        <div className="bg-white p-3 border border-gray-200 rounded-lg shadow-lg">
          <p className="text-sm font-medium">{data.name}</p>
          <p className="text-sm text-gray-600">
            数量: {data.value}
          </p>
        </div>
      )
    }
    return null
  }

  return (
    <div className="h-64">
      <ResponsiveContainer width="100%" height="100%">
        <PieChart>
          <Pie
            data={chartData}
            cx="50%"
            cy="50%"
            innerRadius={40}
            outerRadius={80}
            paddingAngle={5}
            dataKey="value"
          >
            {chartData.map((entry, index) => (
              <Cell key={`cell-${index}`} fill={entry.color} />
            ))}
          </Pie>
          <Tooltip content={<CustomTooltip />} />
          <Legend 
            verticalAlign="bottom" 
            height={36}
            formatter={(value: string, entry: any) => (
              <span style={{ color: entry.color }}>{value}</span>
            )}
          />
        </PieChart>
      </ResponsiveContainer>
    </div>
  )
}

export default ProxyStatusChart
