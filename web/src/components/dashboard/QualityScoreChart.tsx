import React from 'react'
import { <PERSON><PERSON>hart, Bar, XAxis, YAxis, CartesianGrid, Tooltip, ResponsiveContainer, Cell } from 'recharts'
import type { Proxy } from '../../types'

interface QualityScoreChartProps {
  proxies: Proxy[]
}

interface QualityDistribution {
  range: string
  count: number
  color: string
}

const QualityScoreChart: React.FC<QualityScoreChartProps> = ({ proxies }) => {
  // 计算质量评分分布
  const calculateQualityDistribution = (): QualityDistribution[] => {
    const distribution = {
      excellent: 0, // 0.8-1.0
      good: 0,      // 0.6-0.8
      fair: 0,      // 0.4-0.6
      poor: 0,      // 0.2-0.4
      bad: 0,       // 0.0-0.2
      unknown: 0    // 未评估
    }

    proxies.forEach(proxy => {
      const score = proxy.quality_score
      if (score === undefined || score === null) {
        distribution.unknown++
      } else if (score >= 0.8) {
        distribution.excellent++
      } else if (score >= 0.6) {
        distribution.good++
      } else if (score >= 0.4) {
        distribution.fair++
      } else if (score >= 0.2) {
        distribution.poor++
      } else {
        distribution.bad++
      }
    })

    return [
      { range: '优秀 (80-100)', count: distribution.excellent, color: '#22c55e' },
      { range: '良好 (60-80)', count: distribution.good, color: '#84cc16' },
      { range: '一般 (40-60)', count: distribution.fair, color: '#f59e0b' },
      { range: '较差 (20-40)', count: distribution.poor, color: '#f97316' },
      { range: '很差 (0-20)', count: distribution.bad, color: '#ef4444' },
      { range: '未评估', count: distribution.unknown, color: '#6b7280' },
    ].filter(item => item.count > 0)
  }

  const data = calculateQualityDistribution()

  // 自定义Tooltip
  const CustomTooltip = ({ active, payload, label }: any) => {
    if (active && payload && payload.length) {
      const data = payload[0].payload
      return (
        <div className="bg-white p-3 border border-gray-200 rounded-lg shadow-lg">
          <p className="font-medium text-gray-900">{label}</p>
          <p className="text-sm text-gray-600">
            代理数量: <span className="font-medium">{data.count}</span>
          </p>
          <p className="text-sm text-gray-600">
            占比: <span className="font-medium">
              {((data.count / proxies.length) * 100).toFixed(1)}%
            </span>
          </p>
        </div>
      )
    }
    return null
  }

  if (data.length === 0) {
    return (
      <div className="flex items-center justify-center h-64">
        <p className="text-gray-500">暂无质量评分数据</p>
      </div>
    )
  }

  return (
    <div className="h-64">
      <ResponsiveContainer width="100%" height="100%">
        <BarChart
          data={data}
          margin={{
            top: 20,
            right: 30,
            left: 20,
            bottom: 5,
          }}
        >
          <CartesianGrid strokeDasharray="3 3" />
          <XAxis 
            dataKey="range" 
            tick={{ fontSize: 12 }}
            angle={-45}
            textAnchor="end"
            height={80}
          />
          <YAxis tick={{ fontSize: 12 }} />
          <Tooltip content={<CustomTooltip />} />
          <Bar dataKey="count" radius={[4, 4, 0, 0]}>
            {data.map((entry, index) => (
              <Cell key={`cell-${index}`} fill={entry.color} />
            ))}
          </Bar>
        </BarChart>
      </ResponsiveContainer>
    </div>
  )
}

export default QualityScoreChart
