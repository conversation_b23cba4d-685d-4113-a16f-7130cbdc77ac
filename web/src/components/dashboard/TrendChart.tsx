import React, { useState } from 'react'
import { LineChart, Line, XAxis, YAxis, CartesianGrid, Tooltip, ResponsiveContainer, <PERSON> } from 'recharts'
import { Calendar, TrendingUp, TrendingDown, Minus } from 'lucide-react'

interface TrendData {
  timestamp: string
  date: string
  proxies_active: number
  proxies_total: number
  tasks_completed: number
  tasks_failed: number
  avg_response_time: number
  avg_quality_score: number
}

interface TrendChartProps {
  data: TrendData[]
  title: string
}

const TrendChart: React.FC<TrendChartProps> = ({ data, title }) => {
  const [selectedMetrics, setSelectedMetrics] = useState<string[]>([
    'proxies_active',
    'tasks_completed',
    'avg_quality_score'
  ])
  const [timeRange, setTimeRange] = useState<'7d' | '30d' | '90d'>('7d')

  // 指标配置
  const metrics = [
    {
      key: 'proxies_active',
      label: '活跃代理数',
      color: '#22c55e',
      unit: '个'
    },
    {
      key: 'proxies_total',
      label: '代理总数',
      color: '#3b82f6',
      unit: '个'
    },
    {
      key: 'tasks_completed',
      label: '完成任务数',
      color: '#8b5cf6',
      unit: '个'
    },
    {
      key: 'tasks_failed',
      label: '失败任务数',
      color: '#ef4444',
      unit: '个'
    },
    {
      key: 'avg_response_time',
      label: '平均响应时间',
      color: '#f59e0b',
      unit: 'ms'
    },
    {
      key: 'avg_quality_score',
      label: '平均质量评分',
      color: '#06b6d4',
      unit: '%'
    }
  ]

  // 时间范围选项
  const timeRangeOptions = [
    { value: '7d', label: '7天' },
    { value: '30d', label: '30天' },
    { value: '90d', label: '90天' }
  ]

  // 过滤数据
  const filteredData = data.slice(-{
    '7d': 7,
    '30d': 30,
    '90d': 90
  }[timeRange])

  // 切换指标
  const toggleMetric = (metricKey: string) => {
    setSelectedMetrics(prev =>
      prev.includes(metricKey)
        ? prev.filter(key => key !== metricKey)
        : [...prev, metricKey]
    )
  }

  // 计算趋势
  const calculateTrend = (metricKey: string) => {
    if (filteredData.length < 2) return 'stable'
    
    const firstValue = filteredData[0][metricKey as keyof TrendData] as number
    const lastValue = filteredData[filteredData.length - 1][metricKey as keyof TrendData] as number
    
    const change = ((lastValue - firstValue) / firstValue) * 100
    
    if (change > 5) return 'up'
    if (change < -5) return 'down'
    return 'stable'
  }

  // 获取趋势图标
  const getTrendIcon = (trend: string) => {
    switch (trend) {
      case 'up':
        return <TrendingUp className="h-4 w-4 text-green-500" />
      case 'down':
        return <TrendingDown className="h-4 w-4 text-red-500" />
      default:
        return <Minus className="h-4 w-4 text-gray-500" />
    }
  }

  // 自定义Tooltip
  const CustomTooltip = ({ active, payload, label }: any) => {
    if (active && payload && payload.length) {
      return (
        <div className="bg-white p-3 border border-gray-200 rounded-lg shadow-lg">
          <p className="font-medium text-gray-900 mb-2">{label}</p>
          {payload.map((entry: any, index: number) => {
            const metric = metrics.find(m => m.key === entry.dataKey)
            return (
              <p key={index} className="text-sm" style={{ color: entry.color }}>
                {metric?.label}: {entry.value}{metric?.unit}
              </p>
            )
          })}
        </div>
      )
    }
    return null
  }

  return (
    <div className="space-y-4">
      {/* 标题和控制 */}
      <div className="flex items-center justify-between">
        <h3 className="text-lg font-medium text-gray-900">{title}</h3>
        <div className="flex items-center space-x-2">
          {/* 时间范围选择 */}
          <div className="flex border border-gray-300 rounded-lg overflow-hidden">
            {timeRangeOptions.map(option => (
              <button
                key={option.value}
                onClick={() => setTimeRange(option.value as any)}
                className={`px-3 py-1 text-sm ${
                  timeRange === option.value
                    ? 'bg-blue-500 text-white'
                    : 'bg-white text-gray-700 hover:bg-gray-50'
                }`}
              >
                {option.label}
              </button>
            ))}
          </div>
        </div>
      </div>

      {/* 指标选择 */}
      <div className="flex flex-wrap gap-2">
        {metrics.map(metric => {
          const isSelected = selectedMetrics.includes(metric.key)
          const trend = calculateTrend(metric.key)
          
          return (
            <button
              key={metric.key}
              onClick={() => toggleMetric(metric.key)}
              className={`flex items-center space-x-2 px-3 py-2 rounded-lg border transition-colors ${
                isSelected
                  ? 'border-blue-500 bg-blue-50 text-blue-700'
                  : 'border-gray-300 text-gray-700 hover:border-gray-400'
              }`}
            >
              <div
                className="w-3 h-3 rounded-full"
                style={{ backgroundColor: metric.color }}
              />
              <span className="text-sm font-medium">{metric.label}</span>
              {getTrendIcon(trend)}
            </button>
          )
        })}
      </div>

      {/* 图表 */}
      <div className="h-80">
        {filteredData.length > 0 ? (
          <ResponsiveContainer width="100%" height="100%">
            <LineChart data={filteredData}>
              <CartesianGrid strokeDasharray="3 3" />
              <XAxis 
                dataKey="date" 
                tick={{ fontSize: 12 }}
                tickFormatter={(value) => {
                  const date = new Date(value)
                  return `${date.getMonth() + 1}/${date.getDate()}`
                }}
              />
              <YAxis tick={{ fontSize: 12 }} />
              <Tooltip content={<CustomTooltip />} />
              <Legend />
              {selectedMetrics.map(metricKey => {
                const metric = metrics.find(m => m.key === metricKey)
                if (!metric) return null
                
                return (
                  <Line
                    key={metricKey}
                    type="monotone"
                    dataKey={metricKey}
                    stroke={metric.color}
                    strokeWidth={2}
                    dot={{ r: 4 }}
                    activeDot={{ r: 6 }}
                    name={metric.label}
                  />
                )
              })}
            </LineChart>
          </ResponsiveContainer>
        ) : (
          <div className="flex items-center justify-center h-full">
            <div className="text-center">
              <Calendar className="h-12 w-12 text-gray-400 mx-auto mb-4" />
              <p className="text-gray-500">暂无趋势数据</p>
            </div>
          </div>
        )}
      </div>

      {/* 数据摘要 */}
      {filteredData.length > 0 && (
        <div className="grid grid-cols-2 md:grid-cols-3 lg:grid-cols-6 gap-4 pt-4 border-t">
          {metrics.filter(metric => selectedMetrics.includes(metric.key)).map(metric => {
            const latestValue = filteredData[filteredData.length - 1]?.[metric.key as keyof TrendData] as number
            const trend = calculateTrend(metric.key)
            
            return (
              <div key={metric.key} className="text-center">
                <div className="flex items-center justify-center space-x-1 mb-1">
                  <div
                    className="w-2 h-2 rounded-full"
                    style={{ backgroundColor: metric.color }}
                  />
                  {getTrendIcon(trend)}
                </div>
                <div className="text-lg font-semibold text-gray-900">
                  {latestValue?.toFixed(metric.key.includes('score') ? 1 : 0)}{metric.unit}
                </div>
                <div className="text-xs text-gray-500">{metric.label}</div>
              </div>
            )
          })}
        </div>
      )}
    </div>
  )
}

export default TrendChart
