import React from 'react'
import { 
  PROXY_STATUS_CONFIG, 
  TASK_STATUS_CONFIG 
} from '../../utils/constants'
import type { StatusBadgeProps } from '../../types'

const StatusBadge: React.FC<StatusBadgeProps> = ({ 
  status, 
  type = 'proxy', 
  className = '' 
}) => {
  const config = type === 'proxy' ? PROXY_STATUS_CONFIG : TASK_STATUS_CONFIG
  const statusConfig = config[status as keyof typeof config]

  if (!statusConfig) {
    return (
      <span className={`badge badge-gray ${className}`}>
        {status}
      </span>
    )
  }

  return (
    <span 
      className={`badge ${statusConfig.bgColor} ${statusConfig.textColor} ${className}`}
    >
      {statusConfig.label}
    </span>
  )
}

export default StatusBadge
