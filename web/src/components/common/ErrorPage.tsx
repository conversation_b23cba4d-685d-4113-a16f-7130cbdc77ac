import React from 'react'
import { useNavigate } from 'react-router-dom'
import { Home, ArrowLeft, RefreshCw } from 'lucide-react'

interface ErrorPageProps {
  title?: string
  message?: string
  showHomeButton?: boolean
  showBackButton?: boolean
  showRefreshButton?: boolean
}

const ErrorPage: React.FC<ErrorPageProps> = ({
  title = '404',
  message = '页面未找到',
  showHomeButton = true,
  showBackButton = true,
  showRefreshButton = false
}) => {
  const navigate = useNavigate()

  const handleGoHome = () => {
    navigate('/dashboard')
  }

  const handleGoBack = () => {
    window.history.back()
  }

  const handleRefresh = () => {
    window.location.reload()
  }

  return (
    <div className="min-h-screen flex items-center justify-center bg-gray-50">
      <div className="text-center max-w-md mx-auto px-4">
        {/* Logo */}
        <div className="flex justify-center mb-6">
          <img 
            src="/logo.svg" 
            alt="ProxyFlow Logo" 
            className="h-16 w-16"
          />
        </div>

        {/* 错误信息 */}
        <div className="mb-8">
          <h1 className="text-6xl font-bold text-gray-900 mb-4">{title}</h1>
          <p className="text-xl text-gray-600 mb-2">{message}</p>
          <p className="text-sm text-gray-500">
            抱歉，您访问的页面不存在或已被移动。
          </p>
        </div>

        {/* 操作按钮 */}
        <div className="flex flex-col sm:flex-row gap-3 justify-center">
          {showHomeButton && (
            <button
              onClick={handleGoHome}
              className="btn-primary flex items-center justify-center"
            >
              <Home className="h-4 w-4 mr-2" />
              返回首页
            </button>
          )}
          
          {showBackButton && (
            <button
              onClick={handleGoBack}
              className="btn-outline flex items-center justify-center"
            >
              <ArrowLeft className="h-4 w-4 mr-2" />
              返回上一页
            </button>
          )}
          
          {showRefreshButton && (
            <button
              onClick={handleRefresh}
              className="btn-outline flex items-center justify-center"
            >
              <RefreshCw className="h-4 w-4 mr-2" />
              刷新页面
            </button>
          )}
        </div>

        {/* 帮助信息 */}
        <div className="mt-8 text-sm text-gray-500">
          <p>如果问题持续存在，请联系系统管理员。</p>
        </div>
      </div>
    </div>
  )
}

export default ErrorPage
