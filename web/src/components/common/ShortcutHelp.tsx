import React, { useState, useEffect } from 'react'
import { X, Keyboard, Search } from 'lucide-react'
import { getShortcutCategories, formatShortcut } from '../../hooks/useKeyboardShortcuts'

interface ShortcutHelpProps {
  isOpen: boolean
  onClose: () => void
}

const ShortcutHelp: React.FC<ShortcutHelpProps> = ({ isOpen, onClose }) => {
  const [searchTerm, setSearchTerm] = useState('')
  const categories = getShortcutCategories()

  // 过滤快捷键
  const filteredCategories = Object.entries(categories).reduce((acc, [key, category]) => {
    const filteredShortcuts = category.shortcuts.filter(
      shortcut =>
        shortcut.description.toLowerCase().includes(searchTerm.toLowerCase()) ||
        shortcut.key.toLowerCase().includes(searchTerm.toLowerCase())
    )

    if (filteredShortcuts.length > 0) {
      acc[key] = {
        ...category,
        shortcuts: filteredShortcuts
      }
    }

    return acc
  }, {} as typeof categories)

  // ESC键关闭
  useEffect(() => {
    const handleEscape = (e: KeyboardEvent) => {
      if (e.key === 'Escape' && isOpen) {
        onClose()
      }
    }

    document.addEventListener('keydown', handleEscape)
    return () => document.removeEventListener('keydown', handleEscape)
  }, [isOpen, onClose])

  // 阻止背景滚动
  useEffect(() => {
    if (isOpen) {
      document.body.style.overflow = 'hidden'
    } else {
      document.body.style.overflow = 'unset'
    }

    return () => {
      document.body.style.overflow = 'unset'
    }
  }, [isOpen])

  if (!isOpen) return null

  return (
    <div className="fixed inset-0 z-50 overflow-y-auto">
      {/* 背景遮罩 */}
      <div 
        className="fixed inset-0 bg-black bg-opacity-50 transition-opacity"
        onClick={onClose}
      />
      
      {/* 模态框 */}
      <div className="flex min-h-full items-center justify-center p-4">
        <div className="relative bg-white dark:bg-gray-800 rounded-lg shadow-xl max-w-4xl w-full max-h-[90vh] overflow-hidden">
          {/* 头部 */}
          <div className="flex items-center justify-between p-6 border-b border-gray-200 dark:border-gray-700">
            <div className="flex items-center space-x-3">
              <Keyboard className="h-6 w-6 text-primary-600" />
              <h2 className="text-xl font-semibold text-gray-900 dark:text-white">
                快捷键帮助
              </h2>
            </div>
            <button
              onClick={onClose}
              className="text-gray-400 hover:text-gray-600 dark:hover:text-gray-300 transition-colors"
            >
              <X className="h-6 w-6" />
            </button>
          </div>

          {/* 搜索框 */}
          <div className="p-6 border-b border-gray-200 dark:border-gray-700">
            <div className="relative">
              <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-gray-400" />
              <input
                type="text"
                placeholder="搜索快捷键..."
                value={searchTerm}
                onChange={(e) => setSearchTerm(e.target.value)}
                className="w-full pl-10 pr-4 py-2 border border-gray-300 dark:border-gray-600 rounded-lg 
                         bg-white dark:bg-gray-700 text-gray-900 dark:text-white
                         focus:ring-2 focus:ring-primary-500 focus:border-transparent"
                autoFocus
              />
            </div>
          </div>

          {/* 快捷键列表 */}
          <div className="p-6 overflow-y-auto max-h-[60vh]">
            {Object.keys(filteredCategories).length === 0 ? (
              <div className="text-center py-8">
                <Keyboard className="h-12 w-12 text-gray-400 mx-auto mb-4" />
                <p className="text-gray-500 dark:text-gray-400">
                  没有找到匹配的快捷键
                </p>
              </div>
            ) : (
              <div className="space-y-8">
                {Object.entries(filteredCategories).map(([key, category]) => (
                  <div key={key}>
                    <h3 className="text-lg font-medium text-gray-900 dark:text-white mb-4">
                      {category.title}
                    </h3>
                    <div className="grid grid-cols-1 md:grid-cols-2 gap-3">
                      {category.shortcuts.map((shortcut, index) => (
                        <div
                          key={index}
                          className="flex items-center justify-between p-3 bg-gray-50 dark:bg-gray-700 rounded-lg"
                        >
                          <span className="text-sm text-gray-700 dark:text-gray-300">
                            {shortcut.description}
                          </span>
                          <kbd className="inline-flex items-center px-2 py-1 bg-white dark:bg-gray-800 
                                         border border-gray-300 dark:border-gray-600 rounded text-xs 
                                         font-mono text-gray-700 dark:text-gray-300 shadow-sm">
                            {formatShortcut(shortcut.key)}
                          </kbd>
                        </div>
                      ))}
                    </div>
                  </div>
                ))}
              </div>
            )}
          </div>

          {/* 底部提示 */}
          <div className="p-6 border-t border-gray-200 dark:border-gray-700 bg-gray-50 dark:bg-gray-700">
            <div className="flex items-center justify-between text-sm text-gray-600 dark:text-gray-400">
              <div className="flex items-center space-x-4">
                <span>💡 提示：大部分快捷键在输入框中不会生效</span>
              </div>
              <div className="flex items-center space-x-2">
                <kbd className="px-2 py-1 bg-white dark:bg-gray-800 border border-gray-300 dark:border-gray-600 rounded text-xs">
                  Esc
                </kbd>
                <span>关闭</span>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  )
}

// 快捷键帮助触发器组件
interface ShortcutHelpTriggerProps {
  className?: string
  variant?: 'button' | 'link'
  children?: React.ReactNode
}

export const ShortcutHelpTrigger: React.FC<ShortcutHelpTriggerProps> = ({
  className = '',
  variant = 'button',
  children
}) => {
  const [isOpen, setIsOpen] = useState(false)

  // 监听全局快捷键事件
  useEffect(() => {
    const handleShowHelp = () => setIsOpen(true)
    window.addEventListener('shortcut:show-help', handleShowHelp)
    return () => window.removeEventListener('shortcut:show-help', handleShowHelp)
  }, [])

  const baseClasses = variant === 'button'
    ? 'inline-flex items-center px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-md text-sm font-medium text-gray-700 dark:text-gray-300 bg-white dark:bg-gray-800 hover:bg-gray-50 dark:hover:bg-gray-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-primary-500'
    : 'text-sm text-primary-600 dark:text-primary-400 hover:text-primary-500 dark:hover:text-primary-300'

  return (
    <>
      <button
        onClick={() => setIsOpen(true)}
        className={`${baseClasses} ${className}`}
        title="显示快捷键帮助 (F1)"
      >
        {children || (
          <>
            <Keyboard className="h-4 w-4 mr-2" />
            快捷键
          </>
        )}
      </button>
      
      <ShortcutHelp isOpen={isOpen} onClose={() => setIsOpen(false)} />
    </>
  )
}

// 快捷键提示组件
interface ShortcutTooltipProps {
  shortcut: string
  description: string
  children: React.ReactNode
  className?: string
}

export const ShortcutTooltip: React.FC<ShortcutTooltipProps> = ({
  shortcut,
  description,
  children,
  className = ''
}) => {
  return (
    <div className={`group relative ${className}`}>
      {children}
      <div className="absolute bottom-full left-1/2 transform -translate-x-1/2 mb-2 px-3 py-2 
                      bg-gray-900 dark:bg-gray-700 text-white text-xs rounded-lg opacity-0 
                      group-hover:opacity-100 transition-opacity pointer-events-none whitespace-nowrap z-10">
        <div className="flex items-center space-x-2">
          <span>{description}</span>
          <kbd className="px-1.5 py-0.5 bg-gray-800 dark:bg-gray-600 rounded text-xs">
            {formatShortcut(shortcut)}
          </kbd>
        </div>
        <div className="absolute top-full left-1/2 transform -translate-x-1/2 border-4 border-transparent border-t-gray-900 dark:border-t-gray-700"></div>
      </div>
    </div>
  )
}

export default ShortcutHelp
