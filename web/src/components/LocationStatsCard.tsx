import React from 'react'
import { Globe, MapPin, Activity, Clock,  } from 'lucide-react'
import type { LocationStats } from '../types'

interface LocationStatsCardProps {
  stats: LocationStats
  type: 'country' | 'city' | 'unknown'
  className?: string
}

const LocationStatsCard: React.FC<LocationStatsCardProps> = ({
  stats,
  type,
  className = ''
}) => {
  const getIcon = () => {
    switch (type) {
      case 'country':
        return <Globe className="h-5 w-5 text-blue-500" />
      case 'city':
        return <MapPin className="h-5 w-5 text-green-500" />
      default:
        return <Activity className="h-5 w-5 text-gray-500" />
    }
  }

  const getTitle = () => {
    if (type === 'unknown') return '未知位置'
    if (type === 'country') return stats.country_code
    return `${stats.city_name} (${stats.country_code})`
  }

  const getSuccessRateColor = (rate: number) => {
    if (rate >= 80) return 'text-green-600'
    if (rate >= 60) return 'text-yellow-600'
    return 'text-red-600'
  }

  const getSuccessRateBgColor = (rate: number) => {
    if (rate >= 80) return 'bg-green-100'
    if (rate >= 60) return 'bg-yellow-100'
    return 'bg-red-100'
  }

  return (
    <div className={`bg-white rounded-lg border border-gray-200 p-4 hover:shadow-md transition-shadow ${className}`}>
      {/* 标题 */}
      <div className="flex items-center gap-2 mb-3">
        {getIcon()}
        <h3 className="font-medium text-gray-900 truncate">
          {getTitle()}
        </h3>
      </div>

      {/* 统计数据 */}
      <div className="grid grid-cols-2 gap-3 mb-3">
        <div className="text-center">
          <div className="text-2xl font-bold text-gray-900">{stats.total}</div>
          <div className="text-xs text-gray-500">总数</div>
        </div>
        <div className="text-center">
          <div className="text-2xl font-bold text-green-600">{stats.active}</div>
          <div className="text-xs text-gray-500">活跃</div>
        </div>
      </div>

      {/* 详细统计 */}
      <div className="space-y-2">
        {/* 成功率 */}
        <div className="flex items-center justify-between">
          <span className="text-sm text-gray-600">成功率</span>
          <span className={`text-sm font-medium px-2 py-1 rounded-full ${getSuccessRateBgColor(stats.success_rate)} ${getSuccessRateColor(stats.success_rate)}`}>
            {stats.success_rate.toFixed(1)}%
          </span>
        </div>

        {/* 平均响应时间 */}
        {stats.avg_response > 0 && (
          <div className="flex items-center justify-between">
            <span className="text-sm text-gray-600 flex items-center gap-1">
              <Clock className="h-3 w-3" />
              响应时间
            </span>
            <span className="text-sm text-gray-900">
              {stats.avg_response.toFixed(0)}ms
            </span>
          </div>
        )}

        {/* 状态分布 */}
        <div className="pt-2 border-t border-gray-100">
          <div className="flex justify-between text-xs text-gray-500 mb-1">
            <span>状态分布</span>
          </div>
          <div className="flex gap-1">
            {stats.active > 0 && (
              <div 
                className="bg-green-500 h-2 rounded-full"
                style={{ width: `${(stats.active / stats.total) * 100}%` }}
                title={`活跃: ${stats.active}`}
              />
            )}
            {stats.inactive > 0 && (
              <div 
                className="bg-yellow-500 h-2 rounded-full"
                style={{ width: `${(stats.inactive / stats.total) * 100}%` }}
                title={`非活跃: ${stats.inactive}`}
              />
            )}
            {stats.failed > 0 && (
              <div 
                className="bg-red-500 h-2 rounded-full"
                style={{ width: `${(stats.failed / stats.total) * 100}%` }}
                title={`失败: ${stats.failed}`}
              />
            )}
          </div>
          <div className="flex justify-between text-xs text-gray-400 mt-1">
            <span>活跃 {stats.active}</span>
            <span>非活跃 {stats.inactive}</span>
            <span>失败 {stats.failed}</span>
          </div>
        </div>
      </div>
    </div>
  )
}

export default LocationStatsCard
