import React, { useState, useEffect } from 'react';
import { 
  <PERSON>, 
  Copy, 
  Refresh<PERSON>w, 
  Trash2, 
  Plus, 
  Eye, 
  EyeOff,
  AlertTriangle,
  CheckCircle,
  Clock,
  Activity
} from 'lucide-react';
import { APIKeyInfo } from '../types';
import { APIKeyService } from '../services/apiKeyService';
import LoadingSpinner from './common/LoadingSpinner';
import Modal from './common/Modal';

interface APIKeyManagerProps {
  className?: string;
}

export const APIKeyManager: React.FC<APIKeyManagerProps> = ({ className = '' }) => {
  const [apiKeyInfo, setApiKeyInfo] = useState<APIKeyInfo | null>(null);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [showFullKey, setShowFullKey] = useState(false);
  const [fullApiKey, setFullApiKey] = useState<string | null>(null);
  const [copySuccess, setCopySuccess] = useState(false);
  const [showDeleteModal, setShowDeleteModal] = useState(false);
  const [showRegenerateModal, setShowRegenerateModal] = useState(false);
  const [actionLoading, setActionLoading] = useState(false);

  useEffect(() => {
    loadAPIKeyInfo();
  }, []);

  const loadAPIKeyInfo = async () => {
    setLoading(true);
    setError(null);

    const info = await APIKeyService.getAPIKeyInfo();
    if (info) {
      setApiKeyInfo(info);
    } else {
      setError('获取API Key信息失败');
    }

    setLoading(false);
  };

  const handleGenerateAPIKey = async () => {
    setActionLoading(true);
    setError(null);

    const response = await APIKeyService.generateAPIKey();
    if (response) {
      setApiKeyInfo(response);
      setFullApiKey(response.api_key);
      setShowFullKey(true);
    } else {
      setError('生成API Key失败');
    }

    setActionLoading(false);
  };

  const handleRegenerateAPIKey = async () => {
    setActionLoading(true);
    setError(null);

    const response = await APIKeyService.regenerateAPIKey();
    if (response) {
      setApiKeyInfo(response);
      setFullApiKey(response.api_key);
      setShowFullKey(true);
      setShowRegenerateModal(false);
    } else {
      setError('重新生成API Key失败');
    }

    setActionLoading(false);
  };

  const handleDeleteAPIKey = async () => {
    setActionLoading(true);
    setError(null);

    const success = await APIKeyService.deleteAPIKey();
    if (success) {
      setApiKeyInfo(null);
      setFullApiKey(null);
      setShowFullKey(false);
      setShowDeleteModal(false);
    } else {
      setError('删除API Key失败');
    }

    setActionLoading(false);
  };

  const handleCopyAPIKey = async () => {
    const keyToCopy = showFullKey && fullApiKey ? fullApiKey : apiKeyInfo?.masked_api_key;
    if (!keyToCopy) return;

    const success = await APIKeyService.copyToClipboard(keyToCopy);
    if (success) {
      setCopySuccess(true);
      setTimeout(() => setCopySuccess(false), 2000);
    }
  };

  if (loading) {
    return (
      <div className={`bg-white rounded-lg shadow-sm border p-6 ${className}`}>
        <div className="flex items-center justify-center py-8">
          <LoadingSpinner size="md" text="Loading API key info..." />
        </div>
      </div>
    );
  }

  return (
    <div className={`bg-white rounded-lg shadow-sm border ${className}`}>
      <div className="p-6">
        <div className="flex items-center justify-between mb-6">
          <div className="flex items-center space-x-3">
            <Key className="h-6 w-6 text-blue-600" />
            <h2 className="text-xl font-semibold text-gray-900">API Key Management</h2>
          </div>
        </div>

        {error && (
          <div className="mb-6 p-4 bg-red-50 border border-red-200 rounded-lg">
            <div className="flex items-center space-x-2">
              <AlertTriangle className="h-5 w-5 text-red-600" />
              <p className="text-red-700">{error}</p>
            </div>
          </div>
        )}

        {!apiKeyInfo ? (
          <div className="text-center py-12">
            <div className="bg-gray-50 rounded-full w-20 h-20 flex items-center justify-center mx-auto mb-6">
              <Key className="h-10 w-10 text-gray-400" />
            </div>
            <h3 className="text-xl font-semibold text-gray-900 mb-3">暂无API密钥</h3>
            <p className="text-gray-600 mb-8 max-w-md mx-auto leading-relaxed">
              您还没有API密钥。生成一个API密钥来开始使用程序化接口访问系统功能。
            </p>
            <div className="space-y-4">
              <button
                onClick={handleGenerateAPIKey}
                disabled={actionLoading}
                className="inline-flex items-center px-6 py-3 bg-blue-600 text-white rounded-lg hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:ring-offset-2 disabled:opacity-50 disabled:cursor-not-allowed transition-colors duration-200 font-medium"
              >
                {actionLoading ? (
                  <LoadingSpinner size="sm" className="mr-2" />
                ) : (
                  <Plus className="h-5 w-5 mr-2" />
                )}
                生成API密钥
              </button>
              <div className="text-sm text-gray-500">
                <p>生成后请妥善保管您的API密钥，它将用于身份验证</p>
              </div>
            </div>
          </div>
        ) : (
          <div className="space-y-6">
            {/* API Key Display */}
            <div className="bg-gray-50 rounded-lg p-4">
              <div className="flex items-center justify-between mb-3">
                <label className="text-sm font-medium text-gray-700">API Key</label>
                <div className="flex items-center space-x-2">
                  <button
                    onClick={() => setShowFullKey(!showFullKey)}
                    className="text-gray-500 hover:text-gray-700"
                    title={showFullKey ? 'Hide API key' : 'Show API key'}
                  >
                    {showFullKey ? <EyeOff className="h-4 w-4" /> : <Eye className="h-4 w-4" />}
                  </button>
                  <button
                    onClick={handleCopyAPIKey}
                    className="text-gray-500 hover:text-gray-700"
                    title="Copy to clipboard"
                  >
                    {copySuccess ? (
                      <CheckCircle className="h-4 w-4 text-green-600" />
                    ) : (
                      <Copy className="h-4 w-4" />
                    )}
                  </button>
                </div>
              </div>
              <div className="font-mono text-sm bg-white border rounded px-3 py-2 break-all">
                {showFullKey && fullApiKey ? fullApiKey : apiKeyInfo.masked_api_key}
              </div>
              {copySuccess && (
                <p className="text-sm text-green-600 mt-2">Copied to clipboard!</p>
              )}
            </div>

            {/* Statistics */}
            <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
              <div className="bg-blue-50 rounded-lg p-4">
                <div className="flex items-center space-x-3">
                  <Clock className="h-5 w-5 text-blue-600" />
                  <div>
                    <p className="text-sm text-blue-600 font-medium">Created</p>
                    <p className="text-sm text-gray-700">
                      {APIKeyService.formatDateTime(apiKeyInfo.api_key_created)}
                    </p>
                  </div>
                </div>
              </div>

              <div className="bg-green-50 rounded-lg p-4">
                <div className="flex items-center space-x-3">
                  <Activity className="h-5 w-5 text-green-600" />
                  <div>
                    <p className="text-sm text-green-600 font-medium">Last Used</p>
                    <p className="text-sm text-gray-700">
                      {APIKeyService.formatDateTime(apiKeyInfo.api_key_last_used)}
                    </p>
                  </div>
                </div>
              </div>

              <div className="bg-purple-50 rounded-lg p-4">
                <div className="flex items-center space-x-3">
                  <Key className="h-5 w-5 text-purple-600" />
                  <div>
                    <p className="text-sm text-purple-600 font-medium">Usage Count</p>
                    <p className="text-sm text-gray-700">
                      {APIKeyService.formatUsageCount(apiKeyInfo.api_key_usage_count)}
                    </p>
                  </div>
                </div>
              </div>
            </div>

            {/* Actions */}
            <div className="flex flex-wrap gap-3">
              <button
                onClick={() => setShowRegenerateModal(true)}
                disabled={actionLoading}
                className="inline-flex items-center px-4 py-2 bg-yellow-600 text-white rounded-lg hover:bg-yellow-700 focus:outline-none focus:ring-2 focus:ring-yellow-500 focus:ring-offset-2 disabled:opacity-50 disabled:cursor-not-allowed transition-colors duration-200 font-medium"
              >
                <RefreshCw className="h-4 w-4 mr-2" />
                重新生成
              </button>
              <button
                onClick={() => setShowDeleteModal(true)}
                disabled={actionLoading}
                className="inline-flex items-center px-4 py-2 bg-red-600 text-white rounded-lg hover:bg-red-700 focus:outline-none focus:ring-2 focus:ring-red-500 focus:ring-offset-2 disabled:opacity-50 disabled:cursor-not-allowed transition-colors duration-200 font-medium"
              >
                <Trash2 className="h-4 w-4 mr-2" />
                删除
              </button>
            </div>
          </div>
        )}
      </div>

      {/* Regenerate Confirmation Modal */}
      <Modal
        isOpen={showRegenerateModal}
        onClose={() => setShowRegenerateModal(false)}
        title="重新生成API密钥"
      >
        <div className="space-y-4">
          <div className="flex items-start space-x-3">
            <AlertTriangle className="h-6 w-6 text-yellow-600 mt-0.5" />
            <div>
              <p className="text-gray-900 font-medium">确定要重新生成API密钥吗？</p>
              <p className="text-gray-600 text-sm mt-1">
                这将使您当前的API密钥失效。使用旧密钥的应用程序将停止工作。
              </p>
            </div>
          </div>
          <div className="flex justify-end space-x-3">
            <button
              onClick={() => setShowRegenerateModal(false)}
              className="px-4 py-2 text-gray-700 bg-gray-100 rounded-lg hover:bg-gray-200 focus:outline-none focus:ring-2 focus:ring-gray-500 focus:ring-offset-2 transition-colors duration-200"
            >
              取消
            </button>
            <button
              onClick={handleRegenerateAPIKey}
              disabled={actionLoading}
              className="px-4 py-2 bg-yellow-600 text-white rounded-lg hover:bg-yellow-700 focus:outline-none focus:ring-2 focus:ring-yellow-500 focus:ring-offset-2 disabled:opacity-50 transition-colors duration-200"
            >
              {actionLoading ? <LoadingSpinner size="sm" className="mr-2" /> : null}
              重新生成
            </button>
          </div>
        </div>
      </Modal>

      {/* Delete Confirmation Modal */}
      <Modal
        isOpen={showDeleteModal}
        onClose={() => setShowDeleteModal(false)}
        title="删除API密钥"
      >
        <div className="space-y-4">
          <div className="flex items-start space-x-3">
            <AlertTriangle className="h-6 w-6 text-red-600 mt-0.5" />
            <div>
              <p className="text-gray-900 font-medium">确定要删除您的API密钥吗？</p>
              <p className="text-gray-600 text-sm mt-1">
                此操作无法撤销。您需要生成新的API密钥才能再次使用API。
              </p>
            </div>
          </div>
          <div className="flex justify-end space-x-3">
            <button
              onClick={() => setShowDeleteModal(false)}
              className="px-4 py-2 text-gray-700 bg-gray-100 rounded-lg hover:bg-gray-200 focus:outline-none focus:ring-2 focus:ring-gray-500 focus:ring-offset-2 transition-colors duration-200"
            >
              取消
            </button>
            <button
              onClick={handleDeleteAPIKey}
              disabled={actionLoading}
              className="px-4 py-2 bg-red-600 text-white rounded-lg hover:bg-red-700 focus:outline-none focus:ring-2 focus:ring-red-500 focus:ring-offset-2 disabled:opacity-50 transition-colors duration-200"
            >
              {actionLoading ? <LoadingSpinner size="sm" className="mr-2" /> : null}
              删除
            </button>
          </div>
        </div>
      </Modal>
    </div>
  );
};
