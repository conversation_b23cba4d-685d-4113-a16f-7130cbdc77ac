
import { Routes, Route, Navigate } from 'react-router-dom'
import { useAuth } from './contexts/AuthContext'
import Layout from './components/layout/Layout'
import ProtectedRoute from './components/auth/ProtectedRoute'
import LoginForm from './components/auth/LoginForm'
import RegisterForm from './components/auth/RegisterForm'
import Dashboard from './pages/Dashboard'
import ProxyManagement from './pages/ProxyManagement'
import TaskManagement from './pages/TaskManagement'
import SystemMonitoring from './pages/SystemMonitoring'
import Settings from './pages/Settings'
import { APIKeyPage } from './pages/APIKeyPage'
import LoadingSpinner from './components/common/LoadingSpinner'
import ErrorPage from './components/common/ErrorPage'
import ErrorBoundary from './components/common/ErrorBoundary'
import EnhancedTaskManagement from './pages/EnhancedTaskManagement'
import DevTools from './components/dev/DevTools'

function App(): JSX.Element {
  const { loading, isAuthenticated } = useAuth()

  if (loading) {
    return <LoadingSpinner size="xl" text="初始化应用..." />
  }

  return (
    <ErrorBoundary>
      <div className="App">
        <Routes>
        {/* 公开路由 */}
        <Route 
          path="/login" 
          element={
            isAuthenticated ? <Navigate to="/dashboard" replace /> : <LoginForm />
          } 
        />
        <Route 
          path="/register" 
          element={
            isAuthenticated ? <Navigate to="/dashboard" replace /> : <RegisterForm />
          } 
        />

        {/* 受保护的路由 */}
        <Route 
          path="/" 
          element={
            <ProtectedRoute>
              <Layout />
            </ProtectedRoute>
          }
        >
          <Route index element={<Navigate to="/dashboard" replace />} />
          <Route path="dashboard" element={<Dashboard />} />
          <Route path="proxies" element={<ProxyManagement />} />
          <Route path="tasks" element={<EnhancedTaskManagement />} />
          <Route path="monitoring" element={<SystemMonitoring />} />
          <Route path="keys" element={<APIKeyPage />} />
          <Route path="settings" element={<Settings />} />
        </Route>

        {/* 404 页面 */}
        <Route
          path="*"
          element={<ErrorPage />}
        />
        </Routes>

        {/* 开发工具 */}
        <DevTools />
      </div>
    </ErrorBoundary>
  )
}

export default App
