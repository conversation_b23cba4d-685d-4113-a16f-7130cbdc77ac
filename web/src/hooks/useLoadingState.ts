import { useState, useCallback, useRef, useEffect } from 'react'
import { enhancedToast } from '../utils/toastConfig'

// 加载状态类型
export interface LoadingState {
  isLoading: boolean
  loadingMessage: string
  progress: number // 0-100
  error: string | null
  startTime: number | null
  duration: number | null
}

// 加载操作配置
export interface LoadingOptions {
  message?: string
  showProgress?: boolean
  timeout?: number // 超时时间（毫秒）
  showToast?: boolean
  onTimeout?: () => void
}

// 批量加载状态
export interface BatchLoadingState {
  total: number
  completed: number
  failed: number
  current: string | null
  errors: Array<{ item: string; error: string }>
}

export const useLoadingState = () => {
  const [state, setState] = useState<LoadingState>({
    isLoading: false,
    loadingMessage: '',
    progress: 0,
    error: null,
    startTime: null,
    duration: null,
  })

  const [batchState, setBatchState] = useState<BatchLoadingState>({
    total: 0,
    completed: 0,
    failed: 0,
    current: null,
    errors: [],
  })

  const timeoutRef = useRef<NodeJS.Timeout | null>(null)
  const toastIdRef = useRef<string | null>(null)

  // 清除超时定时器
  const clearTimeoutTimer = useCallback(() => {
    if (timeoutRef.current) {
      clearTimeout(timeoutRef.current)
      timeoutRef.current = null
    }
  }, [])

  // 开始加载
  const startLoading = useCallback((options: LoadingOptions = {}) => {
    const {
      message = '加载中...',
      showProgress = false,
      timeout,
      showToast = false,
      onTimeout,
    } = options

    clearTimeoutTimer()

    setState({
      isLoading: true,
      loadingMessage: message,
      progress: showProgress ? 0 : -1, // -1 表示不显示进度
      error: null,
      startTime: Date.now(),
      duration: null,
    })

    // 显示加载Toast
    if (showToast) {
      toastIdRef.current = enhancedToast.loading(message)
    }

    // 设置超时
    if (timeout && timeout > 0) {
      timeoutRef.current = setTimeout(() => {
        setState(prev => ({
          ...prev,
          error: '操作超时',
        }))
        
        if (toastIdRef.current) {
          enhancedToast.dismiss(toastIdRef.current)
          enhancedToast.error('操作超时')
        }
        
        onTimeout?.()
      }, timeout)
    }
  }, [clearTimeoutTimer])

  // 更新进度
  const updateProgress = useCallback((progress: number, message?: string) => {
    setState(prev => ({
      ...prev,
      progress: Math.max(0, Math.min(100, progress)),
      loadingMessage: message || prev.loadingMessage,
    }))
  }, [])

  // 设置错误
  const setError = useCallback((error: string) => {
    clearTimeoutTimer()
    
    setState(prev => ({
      ...prev,
      isLoading: false,
      error,
      duration: prev.startTime ? Date.now() - prev.startTime : null,
    }))

    if (toastIdRef.current) {
      enhancedToast.dismiss(toastIdRef.current)
      enhancedToast.error(error)
      toastIdRef.current = null
    }
  }, [clearTimeoutTimer])

  // 完成加载
  const finishLoading = useCallback((successMessage?: string) => {
    clearTimeoutTimer()
    
    setState(prev => ({
      ...prev,
      isLoading: false,
      progress: 100,
      duration: prev.startTime ? Date.now() - prev.startTime : null,
    }))

    if (toastIdRef.current) {
      enhancedToast.dismiss(toastIdRef.current)
      if (successMessage) {
        enhancedToast.success(successMessage)
      }
      toastIdRef.current = null
    }
  }, [clearTimeoutTimer])

  // 重置状态
  const resetLoading = useCallback(() => {
    clearTimeoutTimer()
    
    if (toastIdRef.current) {
      enhancedToast.dismiss(toastIdRef.current)
      toastIdRef.current = null
    }

    setState({
      isLoading: false,
      loadingMessage: '',
      progress: 0,
      error: null,
      startTime: null,
      duration: null,
    })

    setBatchState({
      total: 0,
      completed: 0,
      failed: 0,
      current: null,
      errors: [],
    })
  }, [clearTimeoutTimer])

  // 批量操作开始
  const startBatchLoading = useCallback((total: number, message = '批量处理中...') => {
    setBatchState({
      total,
      completed: 0,
      failed: 0,
      current: null,
      errors: [],
    })

    startLoading({
      message,
      showProgress: true,
      showToast: true,
    })
  }, [startLoading])

  // 批量操作更新
  const updateBatchProgress = useCallback((
    completed: number,
    failed: number,
    current?: string,
    error?: { item: string; error: string }
  ) => {
    setBatchState(prev => {
      const newState = {
        ...prev,
        completed,
        failed,
        current: current || prev.current,
        errors: error ? [...prev.errors, error] : prev.errors,
      }

      // 更新总体进度
      const progress = (completed + failed) / prev.total * 100
      updateProgress(progress, `处理中... (${completed + failed}/${prev.total})`)

      return newState
    })
  }, [updateProgress])

  // 批量操作完成
  const finishBatchLoading = useCallback(() => {
    const { completed, failed } = batchState

    if (failed === 0) {
      finishLoading(`批量操作完成，成功处理 ${completed} 项`)
    } else {
      finishLoading()
      enhancedToast.warning(`批量操作完成，成功 ${completed} 项，失败 ${failed} 项`)
    }
  }, [batchState, finishLoading])

  // 包装异步操作
  const withLoading = useCallback(async <T>(
    operation: () => Promise<T>,
    options: LoadingOptions = {}
  ): Promise<T | null> => {
    try {
      startLoading(options)
      const result = await operation()
      finishLoading(options.showToast ? '操作成功' : undefined)
      return result
    } catch (error) {
      const errorMessage = error instanceof Error ? error.message : '操作失败'
      setError(errorMessage)
      return null
    }
  }, [startLoading, finishLoading, setError])

  // 包装批量异步操作
  const withBatchLoading = useCallback(async <T>(
    items: T[],
    operation: (item: T, index: number) => Promise<void>,
    options: {
      message?: string
      continueOnError?: boolean
      onProgress?: (completed: number, failed: number, current: T) => void
    } = {}
  ): Promise<{ completed: number; failed: number; errors: Array<{ item: T; error: string }> }> => {
    const {
      message = '批量处理中...',
      continueOnError = true,
      onProgress,
    } = options

    startBatchLoading(items.length, message)

    let completed = 0
    let failed = 0
    const errors: Array<{ item: T; error: string }> = []

    for (let i = 0; i < items.length; i++) {
      const item = items[i]
      
      try {
        await operation(item, i)
        completed++
        updateBatchProgress(completed, failed, `处理项目 ${i + 1}`)
        onProgress?.(completed, failed, item)
      } catch (error) {
        failed++
        const errorMessage = error instanceof Error ? error.message : '处理失败'
        const errorInfo = { item, error: errorMessage }
        errors.push(errorInfo)
        updateBatchProgress(completed, failed, `处理项目 ${i + 1}`, {
          item: String(item),
          error: errorMessage,
        })
        onProgress?.(completed, failed, item)

        if (!continueOnError) {
          break
        }
      }
    }

    finishBatchLoading()

    return { completed, failed, errors }
  }, [startBatchLoading, updateBatchProgress, finishBatchLoading])

  // 获取格式化的持续时间
  const getFormattedDuration = useCallback(() => {
    if (!state.duration) return null
    
    const seconds = Math.floor(state.duration / 1000)
    const minutes = Math.floor(seconds / 60)
    
    if (minutes > 0) {
      return `${minutes}分${seconds % 60}秒`
    } else {
      return `${seconds}秒`
    }
  }, [state.duration])

  // 获取进度百分比文本
  const getProgressText = useCallback(() => {
    if (state.progress < 0) return null
    return `${Math.round(state.progress)}%`
  }, [state.progress])

  // 清理副作用
  useEffect(() => {
    return () => {
      clearTimeoutTimer()
      if (toastIdRef.current) {
        enhancedToast.dismiss(toastIdRef.current)
      }
    }
  }, [clearTimeoutTimer])

  return {
    // 状态
    ...state,
    batchState,
    
    // 方法
    startLoading,
    updateProgress,
    setError,
    finishLoading,
    resetLoading,
    startBatchLoading,
    updateBatchProgress,
    finishBatchLoading,
    withLoading,
    withBatchLoading,
    
    // 计算属性
    getFormattedDuration,
    getProgressText,
    
    // 便捷属性
    hasError: !!state.error,
    isCompleted: !state.isLoading && state.progress === 100,
    progressPercentage: Math.max(0, Math.min(100, state.progress)),
  }
}
