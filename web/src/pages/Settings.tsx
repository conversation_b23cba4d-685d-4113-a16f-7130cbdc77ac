import React, { useState, useEffect } from 'react'
import {
  Save,
  User,
  Bell,
  Shield,
  Palette,
  Globe,
  RefreshCw,
  Info,
  GitBranch,
  ExternalLink,
  AlertTriangle
} from 'lucide-react'
import { useAuth } from '../contexts/AuthContext'
import { useSearchParams } from 'react-router-dom'
import { settingsAPI } from '../services/api'
import {
  validateUserSettings,
  validateProxySettings,
  getValidationErrors,
  validateField,
  hasValidationErrors
} from '../utils/settingsValidation'
import toast from 'react-hot-toast'

interface SettingsForm {
  // 个人设置
  username: string
  email: string

  // 系统设置
  autoRefresh: boolean
  refreshInterval: number
  theme: 'light' | 'dark' | 'auto'
  language: 'zh' | 'en'

  // 代理设置
  defaultTimeout: number
  maxRetries: number
  defaultStrategy: string
}

const Settings: React.FC = () => {
  const { user } = useAuth()
  const [searchParams] = useSearchParams()
  const [loading, setLoading] = useState<boolean>(false)
  const [activeTab, setActiveTab] = useState<string>('profile')
  
  const [settings, setSettings] = useState<SettingsForm>({
    username: user?.username || '',
    email: user?.email || '',
    autoRefresh: true,
    refreshInterval: 30,
    theme: 'light',
    language: 'zh',
    defaultTimeout: 30,
    maxRetries: 3,
    defaultStrategy: 'round_robin',
  })

  // 验证状态
  const [validationErrors, setValidationErrors] = useState<Record<string, string>>({})
  const [isValidating, setIsValidating] = useState<boolean>(false)

  // 实时验证函数
  const validateSettingsData = (settingsData: any, type: 'user' | 'proxy') => {
    let result
    switch (type) {
      case 'user':
        result = validateUserSettings(settingsData)
        break
      case 'proxy':
        result = validateProxySettings(settingsData)
        break
      default:
        return []
    }

    return result.success ? [] : getValidationErrors(result)
  }

  // 字段级验证
  const validateSingleField = (fieldName: string, value: any) => {
    const error = validateField(fieldName, value)
    setValidationErrors(prev => ({
      ...prev,
      [fieldName]: error || ''
    }))
    return !error
  }

  // 根据URL参数设置活动标签
  useEffect(() => {
    const tab = searchParams.get('tab')
    if (tab && ['profile', 'notifications', 'system', 'appearance', 'proxy', 'about'].includes(tab)) {
      setActiveTab(tab)
    }

    // 加载用户设置
    loadSettings()
  }, [searchParams])

  const loadSettings = async () => {
    try {
      setLoading(true)

      // 尝试加载用户设置
      try {
        const userSettingsRes = await settingsAPI.getUserSettings()
        if (userSettingsRes.data.data) {
          setSettings(prev => ({
            ...prev,
            ...userSettingsRes.data.data
          }))
        }
      } catch (error) {
        console.warn('Failed to load user settings:', error)
      }



      // 尝试加载代理设置
      try {
        const proxyRes = await settingsAPI.getProxySettings()
        if (proxyRes.data.data) {
          setSettings(prev => ({
            ...prev,
            ...proxyRes.data.data
          }))
        }
      } catch (error) {
        console.warn('Failed to load proxy settings:', error)
      }

    } catch (error) {
      console.error('Failed to load settings:', error)
      toast.error('加载设置失败')
    } finally {
      setLoading(false)
    }
  }

  const handleChange = (field: keyof SettingsForm, value: any) => {
    setSettings(prev => ({
      ...prev,
      [field]: value
    }))
  }

  const handleSave = async () => {
    try {
      setLoading(true)
      setIsValidating(true)

      // 清除之前的验证错误
      setValidationErrors({})

      // 准备设置数据
      const userSettings = {
        profile: {
          username: settings.username,
          email: settings.email
        },
        preferences: {
          theme: settings.theme,
          language: settings.language,
          autoRefresh: settings.autoRefresh,
          refreshInterval: settings.refreshInterval
        }
      }



      const proxySettings = {
        defaultSettings: {
          timeout: settings.defaultTimeout,
          maxRetries: settings.maxRetries,
          strategy: settings.defaultStrategy
        }
      }

      // 验证所有设置
      const userValidationErrors = validateSettingsData(userSettings, 'user')
      const proxyValidationErrors = validateSettingsData(proxySettings, 'proxy')

      const allErrors = [
        ...userValidationErrors,
        ...proxyValidationErrors
      ]

      if (allErrors.length > 0) {
        setValidationErrors(
          allErrors.reduce((acc, error, index) => {
            acc[`error_${index}`] = error
            return acc
          }, {} as Record<string, string>)
        )
        toast.error(`设置验证失败：${allErrors[0]}`)
        return
      }

      // 分别保存不同类型的设置
      const savePromises = []
      // 保存用户设置
      savePromises.push(
        settingsAPI.updateUserSettings(userSettings).catch(error => {
          console.warn('Failed to save user settings:', error)
          throw new Error('用户设置保存失败')
        })
      )



      // 保存代理设置
      savePromises.push(
        settingsAPI.updateProxySettings(proxySettings).catch(error => {
          console.warn('Failed to save proxy settings:', error)
          throw new Error('代理设置保存失败')
        })
      )

      // 等待所有设置保存完成
      await Promise.all(savePromises)

      toast.success('设置保存成功')
    } catch (error: any) {
      console.error('Failed to save settings:', error)
      const errorMessage = error?.message || '保存设置失败'
      toast.error(errorMessage)
    } finally {
      setLoading(false)
      setIsValidating(false)
    }
  }

  // 重置设置
  const handleReset = async () => {
    if (!confirm('确定要重置所有设置到默认值吗？此操作无法撤销。')) {
      return
    }

    try {
      setLoading(true)
      await settingsAPI.resetSettings()
      await loadSettings() // 重新加载设置
      toast.success('设置已重置为默认值')
    } catch (error) {
      console.error('Failed to reset settings:', error)
      toast.error('重置设置失败')
    } finally {
      setLoading(false)
    }
  }

  // 导出设置
  const handleExport = async () => {
    try {
      const response = await settingsAPI.exportSettings()
      const blob = new Blob([JSON.stringify(response.data.data, null, 2)], {
        type: 'application/json'
      })
      const url = URL.createObjectURL(blob)
      const a = document.createElement('a')
      a.href = url
      a.download = `settings-${new Date().toISOString().split('T')[0]}.json`
      document.body.appendChild(a)
      a.click()
      document.body.removeChild(a)
      URL.revokeObjectURL(url)
      toast.success('设置导出成功')
    } catch (error) {
      console.error('Failed to export settings:', error)
      toast.error('导出设置失败')
    }
  }

  // 导入设置
  const handleImport = async (event: React.ChangeEvent<HTMLInputElement>) => {
    const file = event.target.files?.[0]
    if (!file) return

    try {
      const text = await file.text()
      const importedSettings = JSON.parse(text)

      await settingsAPI.importSettings(importedSettings)
      await loadSettings() // 重新加载设置
      toast.success('设置导入成功')
    } catch (error) {
      console.error('Failed to import settings:', error)
      toast.error('导入设置失败，请检查文件格式')
    }

    // 清空文件输入
    event.target.value = ''
  }

  const tabs = [
    { id: 'profile', name: '个人资料', icon: User },
    { id: 'system', name: '系统设置', icon: Shield },
    { id: 'appearance', name: '外观设置', icon: Palette },
    { id: 'proxy', name: '代理设置', icon: Globe },
    { id: 'about', name: '关于', icon: Info },
  ]

  return (
    <div className="space-y-6">
      {/* 页面标题 */}
      <div>
        <h1 className="text-2xl font-bold text-gray-900">设置</h1>
        <p className="mt-1 text-sm text-gray-500">
          管理您的账户和应用程序设置
        </p>
      </div>

      {/* 验证错误显示 */}
      {Object.keys(validationErrors).length > 0 && (
        <div className="bg-red-50 border border-red-200 rounded-lg p-4">
          <div className="flex">
            <AlertTriangle className="h-5 w-5 text-red-400" />
            <div className="ml-3">
              <h3 className="text-sm font-medium text-red-800">设置验证错误</h3>
              <div className="mt-2 text-sm text-red-700">
                <ul className="list-disc list-inside space-y-1">
                  {Object.values(validationErrors).filter(error => error).map((error, index) => (
                    <li key={index}>{error}</li>
                  ))}
                </ul>
              </div>
            </div>
          </div>
        </div>
      )}

      <div className="flex flex-col lg:flex-row lg:space-x-8">
        {/* 侧边栏导航 */}
        <div className="lg:w-64 mb-6 lg:mb-0">
          <nav className="space-y-1">
            {tabs.map((tab) => {
              const Icon = tab.icon
              return (
                <button
                  key={tab.id}
                  onClick={() => setActiveTab(tab.id)}
                  className={`w-full flex items-center px-3 py-2 text-sm font-medium rounded-md transition-colors ${
                    activeTab === tab.id
                      ? 'bg-primary-100 text-primary-700'
                      : 'text-gray-600 hover:text-gray-900 hover:bg-gray-50'
                  }`}
                >
                  <Icon className="mr-3 h-5 w-5" />
                  {tab.name}
                </button>
              )
            })}
          </nav>
        </div>

        {/* 主内容区域 */}
        <div className="flex-1">
          <div className="card">
            <div className="card-body">
              {/* 个人资料 */}
              {activeTab === 'profile' && (
                <div className="space-y-6">
                  <div>
                    <h3 className="text-lg font-medium text-gray-900">个人资料</h3>
                    <p className="mt-1 text-sm text-gray-500">
                      更新您的个人信息
                    </p>
                  </div>
                  
                  <div className="grid grid-cols-1 md:grid-cols-2 gap-6 border p-4">
                    <div>
                      <label className="block text-sm font-medium text-gray-700">
                        用户名
                      </label>
                      <input
                        type="text"
                        className="input mt-1"
                        value={settings.username}
                        onChange={(e) => handleChange('username', e.target.value)}
                      />
                    </div>
                    
                    <div>
                      <label className="block text-sm font-medium text-gray-700">
                        邮箱地址
                      </label>
                      <input
                        type="email"
                        className="input mt-1"
                        value={settings.email}
                        onChange={(e) => handleChange('email', e.target.value)}
                      />
                    </div>
                  </div>
                </div>
              )}



              {/* 系统设置 */}
              {activeTab === 'system' && (
                <div className="space-y-6">
                  <div>
                    <h3 className="text-lg font-medium text-gray-900">系统设置</h3>
                    <p className="mt-1 text-sm text-gray-500">
                      配置应用程序行为
                    </p>
                  </div>
                  
                  <div className="space-y-4">
                    <div className="flex items-center justify-between">
                      <div>
                        <h4 className="text-sm font-medium text-gray-900">自动刷新</h4>
                        <p className="text-sm text-gray-500">自动刷新数据</p>
                      </div>
                      <label className="relative inline-flex items-center cursor-pointer">
                        <input
                          type="checkbox"
                          className="sr-only peer"
                          checked={settings.autoRefresh}
                          onChange={(e) => handleChange('autoRefresh', e.target.checked)}
                        />
                        <div className="w-11 h-6 bg-gray-200 peer-focus:outline-none peer-focus:ring-4 peer-focus:ring-primary-300 rounded-full peer peer-checked:after:translate-x-full peer-checked:after:border-white after:content-[''] after:absolute after:top-[2px] after:left-[2px] after:bg-white after:border-gray-300 after:border after:rounded-full after:h-5 after:w-5 after:transition-all peer-checked:bg-primary-600"></div>
                      </label>
                    </div>
                    
                    {settings.autoRefresh && (
                      <div>
                        <label className="block text-sm font-medium text-gray-700">
                          刷新间隔 (秒)
                        </label>
                        <select
                          className="input mt-1 w-full md:w-48"
                          value={settings.refreshInterval}
                          onChange={(e) => handleChange('refreshInterval', parseInt(e.target.value))}
                        >
                          <option value={10}>10 秒</option>
                          <option value={30}>30 秒</option>
                          <option value={60}>1 分钟</option>
                          <option value={300}>5 分钟</option>
                        </select>
                      </div>
                    )}
                  </div>
                </div>
              )}

              {/* 外观设置 */}
              {activeTab === 'appearance' && (
                <div className="space-y-6">
                  <div>
                    <h3 className="text-lg font-medium text-gray-900">外观设置</h3>
                    <p className="mt-1 text-sm text-gray-500">
                      自定义应用程序外观
                    </p>
                  </div>
                  
                  <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                    <div>
                      <label className="block text-sm font-medium text-gray-700">
                        主题
                      </label>
                      <select
                        className="input mt-1"
                        value={settings.theme}
                        onChange={(e) => handleChange('theme', e.target.value)}
                      >
                        <option value="light">浅色</option>
                        <option value="dark">深色</option>
                        <option value="auto">跟随系统</option>
                      </select>
                    </div>
                    
                    <div>
                      <label className="block text-sm font-medium text-gray-700">
                        语言
                      </label>
                      <select
                        className="input mt-1"
                        value={settings.language}
                        onChange={(e) => handleChange('language', e.target.value)}
                      >
                        <option value="zh">中文</option>
                        <option value="en">English</option>
                      </select>
                    </div>
                  </div>
                </div>
              )}

              {/* 代理设置 */}
              {activeTab === 'proxy' && (
                <div className="space-y-6">
                  <div>
                    <h3 className="text-lg font-medium text-gray-900">代理设置</h3>
                    <p className="mt-1 text-sm text-gray-500">
                      配置默认代理参数
                    </p>
                  </div>
                  
                  <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                    <div>
                      <label className="block text-sm font-medium text-gray-700">
                        默认超时时间 (秒)
                      </label>
                      <input
                        type="number"
                        min="1"
                        max="300"
                        className="input mt-1"
                        value={settings.defaultTimeout}
                        onChange={(e) => handleChange('defaultTimeout', parseInt(e.target.value))}
                      />
                    </div>
                    
                    <div>
                      <label className="block text-sm font-medium text-gray-700">
                        默认重试次数
                      </label>
                      <input
                        type="number"
                        min="0"
                        max="10"
                        className="input mt-1"
                        value={settings.maxRetries}
                        onChange={(e) => handleChange('maxRetries', parseInt(e.target.value))}
                      />
                    </div>
                    
                    <div className="md:col-span-2">
                      <label className="block text-sm font-medium text-gray-700">
                        默认代理策略
                      </label>
                      <select
                        className="input mt-1"
                        value={settings.defaultStrategy}
                        onChange={(e) => handleChange('defaultStrategy', e.target.value)}
                      >
                        <option value="round_robin">轮询</option>
                        <option value="least_used">最少使用</option>
                        <option value="random">随机</option>
                        <option value="weighted">加权</option>
                      </select>
                    </div>
                  </div>
                </div>
              )}

              {/* 关于 */}
              {activeTab === 'about' && (
                <div className="space-y-6">
                  <div>
                    <h3 className="text-lg font-medium text-gray-900">关于 ProxyFlow</h3>
                    <p className="mt-1 text-sm text-gray-500">
                      高性能代理服务管理系统
                    </p>
                  </div>

                  <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                    <div className="space-y-4">
                      <div>
                        <h4 className="text-sm font-medium text-gray-900">版本信息</h4>
                        <p className="text-sm text-gray-600 mt-1">v1.0.0</p>
                      </div>

                      <div>
                        <h4 className="text-sm font-medium text-gray-900">技术栈</h4>
                        <div className="text-sm text-gray-600 mt-1 space-y-1">
                          <p>• React 18 + TypeScript</p>
                          <p>• Vite + Tailwind CSS</p>
                          <p>• Go + Gin Framework</p>
                          <p>• Redis + PostgreSQL</p>
                        </div>
                      </div>
                    </div>

                    <div className="space-y-4">
                      <div>
                        <h4 className="text-sm font-medium text-gray-900">开源项目</h4>
                        <p className="text-sm text-gray-600 mt-1">
                          ProxyFlow 是一个开源项目，欢迎贡献代码和反馈问题。
                        </p>
                      </div>

                      <div className="flex flex-col space-y-2">
                        <a
                          href={import.meta.env.VITE_GITHUB_URL || 'https://github.com/your-username/proxyflow'}
                          target="_blank"
                          rel="noopener noreferrer"
                          className="inline-flex items-center text-sm text-blue-600 hover:text-blue-800 transition-colors"
                        >
                          <GitBranch className="h-4 w-4 mr-2" />
                          查看源码
                          <ExternalLink className="h-3 w-3 ml-1" />
                        </a>

                        <a
                          href={`${import.meta.env.VITE_GITHUB_URL || 'https://github.com/your-username/proxyflow'}/issues`}
                          target="_blank"
                          rel="noopener noreferrer"
                          className="inline-flex items-center text-sm text-blue-600 hover:text-blue-800 transition-colors"
                        >
                          <Bell className="h-4 w-4 mr-2" />
                          报告问题
                          <ExternalLink className="h-3 w-3 ml-1" />
                        </a>
                      </div>
                    </div>
                  </div>

                  <div className="border-t pt-6">
                    <div className="text-center text-sm text-gray-500">
                      <p>© {new Date().getFullYear()} ProxyFlow. 基于 MIT 许可证开源。</p>
                    </div>
                  </div>
                </div>
              )}

              {/* 操作按钮 */}
              <div className="pt-6 border-t border-gray-200">
                <div className="flex flex-col sm:flex-row justify-between items-start sm:items-center space-y-3 sm:space-y-0">
                  {/* 左侧：高级操作 */}
                  <div className="flex flex-wrap items-center space-x-3">
                    <button
                      onClick={handleReset}
                      disabled={loading}
                      className="btn-outline text-red-600 border-red-300 hover:bg-red-50"
                    >
                      重置设置
                    </button>

                    <button
                      onClick={handleExport}
                      disabled={loading}
                      className="btn-outline"
                    >
                      导出设置
                    </button>

                    <div className="relative">
                      <input
                        type="file"
                        accept=".json"
                        onChange={handleImport}
                        className="absolute inset-0 w-full h-full opacity-0 cursor-pointer"
                        disabled={loading}
                      />
                      <button
                        disabled={loading}
                        className="btn-outline"
                      >
                        导入设置
                      </button>
                    </div>
                  </div>

                  {/* 右侧：保存按钮 */}
                  <button
                    onClick={handleSave}
                    disabled={loading}
                    className="btn-primary"
                  >
                    {loading ? (
                      <RefreshCw className="h-4 w-4 mr-2 animate-spin" />
                    ) : (
                      <Save className="h-4 w-4 mr-2" />
                    )}
                    保存设置
                  </button>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  )
}

export default Settings
