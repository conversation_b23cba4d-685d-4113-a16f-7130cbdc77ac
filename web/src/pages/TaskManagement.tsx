import React, { useState, useEffect } from 'react'
import { 
  Plus, 
  RefreshCw, 
  Search, 
  Eye, 
  X,
  ListTodo,
  Filter
} from 'lucide-react'
import { taskAPI } from '../services/api'
import LoadingSpinner from '../components/common/LoadingSpinner'
import StatusBadge from '../components/common/StatusBadge'
import Modal from '../components/common/Modal'
import CreateTaskForm from '../components/task/CreateTaskForm'
import TaskDetailModal from '../components/task/TaskDetailModal'
import { formatDate, formatRelativeTime } from '../utils/helpers'
import { REFRESH_INTERVALS, TASK_STATUS } from '../utils/constants'
import toast from 'react-hot-toast'
import type { Task, TaskStatus } from '../types'

const TaskManagement: React.FC = () => {
  const [tasks, setTasks] = useState<Task[]>([])
  const [loading, setLoading] = useState<boolean>(true)
  const [refreshing, setRefreshing] = useState<boolean>(false)
  const [searchTerm, setSearchTerm] = useState<string>('')
  const [statusFilter, setStatusFilter] = useState<TaskStatus | 'all'>('all')
  const [showCreateModal, setShowCreateModal] = useState<boolean>(false)
  const [selectedTask, setSelectedTask] = useState<Task | null>(null)
  const [showDetailModal, setShowDetailModal] = useState<boolean>(false)
  const [cancellingId, setCancellingId] = useState<string | null>(null)

  // 获取任务列表
  const fetchTasks = async (showLoading = true): Promise<void> => {
    try {
      if (showLoading) setLoading(true)
      else setRefreshing(true)

      const response = await taskAPI.getTasks()
      setTasks(response.data.data || [])
    } catch (error) {
      console.error('Failed to fetch tasks:', error)
      toast.error('获取任务列表失败')
    } finally {
      setLoading(false)
      setRefreshing(false)
    }
  }

  // 取消任务
  const handleCancel = async (id: string): Promise<void> => {
    if (!confirm('确定要取消这个任务吗？')) return

    try {
      setCancellingId(id)
      await taskAPI.cancelTask(id)
      toast.success('任务已取消')
      fetchTasks(false)
    } catch (error) {
      toast.error('取消任务失败')
    } finally {
      setCancellingId(null)
    }
  }

  // 查看任务详情
  const handleViewDetail = (task: Task): void => {
    setSelectedTask(task)
    setShowDetailModal(true)
  }

  // 过滤任务
  const filteredTasks = tasks.filter(task => {
    const matchesSearch = 
      task.name.toLowerCase().includes(searchTerm.toLowerCase()) ||
      task.url.toLowerCase().includes(searchTerm.toLowerCase()) ||
      task.method.toLowerCase().includes(searchTerm.toLowerCase())
    
    const matchesStatus = statusFilter === 'all' || task.status === statusFilter
    
    return matchesSearch && matchesStatus
  })

  // 获取状态统计
  const getStatusCount = (status: TaskStatus): number => {
    return tasks.filter(task => task.status === status).length
  }

  // 初始加载和定时刷新
  useEffect(() => {
    fetchTasks()
    
    const interval = setInterval(() => {
      fetchTasks(false)
    }, REFRESH_INTERVALS.TASK_LIST)
    
    return () => clearInterval(interval)
  }, [])

  if (loading) {
    return <LoadingSpinner size="lg" text="加载任务列表..." />
  }

  return (
    <div className="space-y-6">
      {/* 页面标题和操作 */}
      <div className="flex flex-col sm:flex-row sm:items-center sm:justify-between">
        <div>
          <h1 className="text-2xl font-bold text-gray-900">任务管理</h1>
          <p className="mt-1 text-sm text-gray-500">
            创建和管理HTTP请求任务
          </p>
        </div>
        <div className="mt-4 sm:mt-0 flex space-x-3">
          <button
            onClick={() => fetchTasks(false)}
            disabled={refreshing}
            className="btn-outline"
          >
            <RefreshCw className={`h-4 w-4 mr-2 ${refreshing ? 'animate-spin' : ''}`} />
            刷新
          </button>
          <button
            onClick={() => setShowCreateModal(true)}
            className="btn-primary"
          >
            <Plus className="h-4 w-4 mr-2" />
            创建任务
          </button>
        </div>
      </div>

      {/* 搜索和过滤 */}
      <div className="flex flex-col lg:flex-row lg:items-center lg:justify-between space-y-4 lg:space-y-0">
        <div className="flex flex-col sm:flex-row space-y-4 sm:space-y-0 sm:space-x-4">
          <div className="relative">
            <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-gray-400" />
            <input
              type="text"
              placeholder="搜索任务..."
              className="pl-10 input w-full sm:w-64"
              value={searchTerm}
              onChange={(e) => setSearchTerm(e.target.value)}
            />
          </div>
          
          <div className="relative">
            <Filter className="absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-gray-400" />
            <select
              className="pl-10 input w-full sm:w-48"
              value={statusFilter}
              onChange={(e) => setStatusFilter(e.target.value as TaskStatus | 'all')}
            >
              <option value="all">所有状态</option>
              <option value="pending">等待中</option>
              <option value="running">运行中</option>
              <option value="completed">已完成</option>
              <option value="failed">失败</option>
              <option value="cancelled">已取消</option>
            </select>
          </div>
        </div>
        
        <div className="flex items-center space-x-6 text-sm text-gray-600">
          <div className="flex items-center">
            <div className="w-3 h-3 bg-gray-500 rounded-full mr-2"></div>
            等待: {getStatusCount(TASK_STATUS.PENDING as TaskStatus)}
          </div>
          <div className="flex items-center">
            <div className="w-3 h-3 bg-blue-500 rounded-full mr-2"></div>
            运行: {getStatusCount(TASK_STATUS.RUNNING as TaskStatus)}
          </div>
          <div className="flex items-center">
            <div className="w-3 h-3 bg-green-500 rounded-full mr-2"></div>
            完成: {getStatusCount(TASK_STATUS.COMPLETED as TaskStatus)}
          </div>
          <div className="flex items-center">
            <div className="w-3 h-3 bg-red-500 rounded-full mr-2"></div>
            失败: {getStatusCount(TASK_STATUS.FAILED as TaskStatus)}
          </div>
        </div>
      </div>

      {/* 任务列表 */}
      <div className="card">
        <div className="card-header">
          <h3 className="text-lg font-medium text-gray-900">
            任务列表 ({filteredTasks.length})
          </h3>
        </div>
        <div className="card-body p-0">
          {filteredTasks.length === 0 ? (
            <div className="text-center py-12">
              <ListTodo className="mx-auto h-12 w-12 text-gray-400" />
              <h3 className="mt-2 text-sm font-medium text-gray-900">
                {searchTerm || statusFilter !== 'all' ? '没有找到匹配的任务' : '暂无任务'}
              </h3>
              <p className="mt-1 text-sm text-gray-500">
                {searchTerm || statusFilter !== 'all' ? '尝试调整搜索或过滤条件' : '点击上方按钮创建第一个任务'}
              </p>
            </div>
          ) : (
            <div className="overflow-x-auto">
              <table className="min-w-full divide-y divide-gray-200">
                <thead className="bg-gray-50">
                  <tr>
                    <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                      任务信息
                    </th>
                    <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                      请求详情
                    </th>
                    <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                      状态
                    </th>
                    <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                      优先级
                    </th>
                    <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                      创建时间
                    </th>
                    <th className="px-6 py-3 text-right text-xs font-medium text-gray-500 uppercase tracking-wider">
                      操作
                    </th>
                  </tr>
                </thead>
                <tbody className="bg-white divide-y divide-gray-200">
                  {filteredTasks.map((task) => (
                    <tr key={task.id} className="hover:bg-gray-50">
                      <td className="px-6 py-4 whitespace-nowrap">
                        <div>
                          <div className="text-sm font-medium text-gray-900">
                            {task.name}
                          </div>
                          <div className="text-sm text-gray-500 truncate max-w-xs" title={task.url}>
                            {task.url}
                          </div>
                        </div>
                      </td>
                      <td className="px-6 py-4 whitespace-nowrap">
                        <div className="text-sm text-gray-900">
                          <span className="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-blue-100 text-blue-800">
                            {task.method}
                          </span>
                        </div>
                        <div className="text-sm text-gray-500">
                          {task.proxy_strategy}
                        </div>
                      </td>
                      <td className="px-6 py-4 whitespace-nowrap">
                        <StatusBadge status={task.status} type="task" />
                        {task.retry_count && task.retry_count > 0 && (
                          <div className="text-xs text-gray-500 mt-1">
                            重试: {task.retry_count}
                          </div>
                        )}
                      </td>
                      <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-900">
                        {task.priority}
                      </td>
                      <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
                        <div>{formatRelativeTime(task.created_at)}</div>
                        <div className="text-xs">{formatDate(task.created_at)}</div>
                      </td>
                      <td className="px-6 py-4 whitespace-nowrap text-right text-sm font-medium">
                        <div className="flex items-center justify-end space-x-2">
                          <button
                            onClick={() => handleViewDetail(task)}
                            className="text-blue-600 hover:text-blue-900"
                            title="查看详情"
                          >
                            <Eye className="h-4 w-4" />
                          </button>
                          {(task.status === 'pending' || task.status === 'running') && (
                            <button
                              onClick={() => handleCancel(task.id)}
                              disabled={cancellingId === task.id}
                              className="text-red-600 hover:text-red-900 disabled:opacity-50"
                              title="取消任务"
                            >
                              {cancellingId === task.id ? (
                                <RefreshCw className="h-4 w-4 animate-spin" />
                              ) : (
                                <X className="h-4 w-4" />
                              )}
                            </button>
                          )}
                        </div>
                      </td>
                    </tr>
                  ))}
                </tbody>
              </table>
            </div>
          )}
        </div>
      </div>

      {/* 创建任务模态框 */}
      <Modal
        isOpen={showCreateModal}
        onClose={() => setShowCreateModal(false)}
        title="创建任务"
        size="lg"
      >
        <CreateTaskForm
          onSuccess={() => {
            setShowCreateModal(false)
            fetchTasks(false)
          }}
          onCancel={() => setShowCreateModal(false)}
        />
      </Modal>

      {/* 任务详情模态框 */}
      <TaskDetailModal
        task={selectedTask}
        isOpen={showDetailModal}
        onClose={() => {
          setShowDetailModal(false)
          setSelectedTask(null)
        }}
      />
    </div>
  )
}

export default TaskManagement
