import React, { useState, useEffect } from 'react'
import {
  RefreshCw,
  Activity,
  Server,
  Cpu,
  HardDrive,
  Network,
  Clock,
  AlertTriangle,
  CheckCircle,
  BarChart3,
  Wifi,
  WifiOff
} from 'lucide-react'
import { systemAPI, proxyAPI, taskAPI } from '../services/api'
import LoadingSpinner from '../components/common/LoadingSpinner'
import StatsCard from '../components/dashboard/StatsCard'
import { formatDate, formatFileSize, formatPercentage } from '../utils/helpers'
import { REFRESH_INTERVALS } from '../utils/constants'
import { useSystemMonitoring } from '../hooks/useRealtimeData'
import toast from 'react-hot-toast'
import type { SystemHealth, ProxyStats, TaskStats } from '../types'

interface SystemMetrics {
  health: SystemHealth | null
  proxyStats: ProxyStats | null
  taskStats: TaskStats | null
  performanceMetrics: any | null
  systemLogs: any[] | null
  systemEvents: any[] | null
  networkStatus: any | null
  diskUsage: any | null
}

const SystemMonitoring: React.FC = () => {
  const [metrics, setMetrics] = useState<SystemMetrics>({
    health: null,
    proxyStats: null,
    taskStats: null,
    performanceMetrics: null,
    systemLogs: null,
    systemEvents: null,
    networkStatus: null,
    diskUsage: null,
  })
  const [loading, setLoading] = useState<boolean>(true)
  const [refreshing, setRefreshing] = useState<boolean>(false)
  const [lastUpdate, setLastUpdate] = useState<Date | null>(null)
  const [error, setError] = useState<string | null>(null)

  // 集成实时监控
  const {
    connectionState,
    systemMetrics: realtimeMetrics,
    systemEvents: realtimeEvents,
    connect: connectRealtime,
    disconnect: disconnectRealtime
  } = useSystemMonitoring()

  // 处理实时指标更新
  useEffect(() => {
    if (realtimeMetrics) {
      setMetrics(prev => ({
        ...prev,
        performanceMetrics: {
          cpu: { usage: realtimeMetrics.cpu_usage },
          memory: { usage: realtimeMetrics.memory_usage },
          disk: { usage: realtimeMetrics.disk_usage },
          network: {
            inbound: realtimeMetrics.network_in,
            outbound: realtimeMetrics.network_out
          },
          connections: realtimeMetrics.active_connections,
          activeTasks: realtimeMetrics.active_tasks,
          activeProxies: realtimeMetrics.active_proxies,
          timestamp: new Date().toISOString()
        }
      }))
    }
  }, [realtimeMetrics])

  // 处理实时事件
  useEffect(() => {
    if (realtimeEvents.length > 0) {
      setMetrics(prev => ({
        ...prev,
        systemEvents: realtimeEvents
      }))
    }
  }, [realtimeEvents])

  // 获取系统指标
  const fetchMetrics = async (showLoading = true): Promise<void> => {
    try {
      if (showLoading) setLoading(true)
      else setRefreshing(true)

      setError(null)

      // 尝试获取真实数据，如果失败则使用模拟数据
      try {
        const [healthRes, proxyStatsRes, taskStatsRes] = await Promise.all([
          systemAPI.healthCheck(),
          proxyAPI.getProxyStats(),
          taskAPI.getTaskStats()
        ])

        // 处理健康检查响应 - 后端直接返回 {status: "ok"}，不是包装在 data 中
        const healthData = healthRes.data.status ? healthRes.data : healthRes.data.data

        setMetrics({
          health: healthData as SystemHealth,
          proxyStats: proxyStatsRes.data.data!,
          taskStats: taskStatsRes.data.data!,
          performanceMetrics: null,
          systemLogs: null,
          systemEvents: null,
          networkStatus: null,
          diskUsage: null,
        })
      } catch (apiError) {
        console.warn('API调用失败，使用模拟数据:', apiError)

        // 使用模拟数据进行开发测试
        setMetrics({
          health: {
            status: 'ok',
            uptime: '2天 3小时 45分钟',
            memory: {
              used: 1024 * 1024 * 512, // 512MB
              total: 1024 * 1024 * 1024 * 2 // 2GB
            },
            cpu: {
              usage: 35.5
            }
          },
          proxyStats: {
            total: 25,
            active: 18,
            inactive: 5,
            failed: 2
          },
          taskStats: {
            total: 150,
            pending: 5,
            running: 3,
            completed: 135,
            failed: 7,
            cancelled: 0
          },
          performanceMetrics: null,
          systemLogs: null,
          systemEvents: null,
          networkStatus: null,
          diskUsage: null,
        })
      }

      setLastUpdate(new Date())
    } catch (error) {
      console.error('Failed to fetch system metrics:', error)
      setError('获取系统监控数据失败，请稍后重试')
    } finally {
      setLoading(false)
      setRefreshing(false)
    }
  }

  // 初始加载和定时刷新
  useEffect(() => {
    fetchMetrics()
    
    const interval = setInterval(() => {
      fetchMetrics(false)
    }, REFRESH_INTERVALS.DASHBOARD)
    
    return () => clearInterval(interval)
  }, [])

  if (loading) {
    return <LoadingSpinner size="lg" text="加载系统监控数据..." />
  }

  if (error) {
    return (
      <div className="flex flex-col items-center justify-center min-h-96">
        <AlertTriangle className="h-12 w-12 text-red-500 mb-4" />
        <h3 className="text-lg font-medium text-gray-900 mb-2">加载失败</h3>
        <p className="text-gray-600 mb-4">{error}</p>
        <button
          onClick={() => fetchMetrics()}
          className="btn-primary"
        >
          <RefreshCw className="h-4 w-4 mr-2" />
          重新加载
        </button>
      </div>
    )
  }

  const { health, proxyStats, taskStats } = metrics

  return (
    <div className="space-y-6">
      {/* 页面标题和操作 */}
      <div className="flex flex-col sm:flex-row sm:items-center sm:justify-between">
        <div>
          <h1 className="text-2xl font-bold text-gray-900">系统监控</h1>
          <p className="mt-1 text-sm text-gray-500">
            实时监控系统状态和性能指标
          </p>
        </div>
        <div className="mt-4 sm:mt-0 flex items-center space-x-4">
          {/* 实时连接状态 */}
          <div className="flex items-center space-x-2">
            {connectionState.isConnected ? (
              <>
                <Wifi className="h-4 w-4 text-green-500" />
                <span className="text-sm text-green-600">实时连接</span>
              </>
            ) : connectionState.isConnecting ? (
              <>
                <RefreshCw className="h-4 w-4 text-yellow-500 animate-spin" />
                <span className="text-sm text-yellow-600">连接中...</span>
              </>
            ) : (
              <>
                <WifiOff className="h-4 w-4 text-red-500" />
                <span className="text-sm text-red-600">离线</span>
              </>
            )}
          </div>

          {lastUpdate && (
            <span className="text-sm text-gray-500">
              最后更新: {formatDate(lastUpdate, 'HH:mm:ss')}
            </span>
          )}
          <button
            onClick={() => fetchMetrics(false)}
            disabled={refreshing}
            className="btn-outline"
          >
            <RefreshCw className={`h-4 w-4 mr-2 ${refreshing ? 'animate-spin' : ''}`} />
            刷新
          </button>
        </div>
      </div>

      {/* 系统状态概览 */}
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
        <StatsCard
          title="系统状态"
          value={health?.status === 'ok' ? '正常' : '异常'}
          icon={health?.status === 'ok' ? CheckCircle : AlertTriangle}
          color={health?.status === 'ok' ? 'green' : 'red'}
          trend="neutral"
        />
        
        <StatsCard
          title="运行时间"
          value={health?.uptime || '未知'}
          icon={Clock}
          color="blue"
          trend="neutral"
        />
        
        <StatsCard
          title="活跃代理"
          value={proxyStats?.active || 0}
          icon={Server}
          color="purple"
          trend={proxyStats && proxyStats.active > 0 ? 'up' : 'down'}
          trendValue={`总计 ${proxyStats?.total || 0} 个`}
        />
        
        <StatsCard
          title="运行任务"
          value={taskStats?.running || 0}
          icon={Activity}
          color="yellow"
          trend={taskStats && taskStats.running > 0 ? 'up' : 'neutral'}
          trendValue={`总计 ${taskStats?.total || 0} 个`}
        />
      </div>

      {/* 系统资源使用情况 */}
      {health && (health.memory || health.cpu) && (
        <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
          {/* 内存使用情况 */}
          {health.memory && (
            <div className="card">
              <div className="card-header">
                <h3 className="text-lg font-medium text-gray-900 flex items-center">
                  <HardDrive className="h-5 w-5 mr-2" />
                  内存使用情况
                </h3>
              </div>
              <div className="card-body">
                <div className="space-y-4">
                  <div className="flex items-center justify-between">
                    <span className="text-sm font-medium text-gray-700">已使用</span>
                    <span className="text-sm text-gray-900">
                      {formatFileSize(health.memory.used)} / {formatFileSize(health.memory.total)}
                    </span>
                  </div>
                  
                  <div className="w-full bg-gray-200 rounded-full h-2">
                    <div 
                      className="bg-blue-600 h-2 rounded-full transition-all duration-300"
                      style={{ 
                        width: formatPercentage(health.memory.used, health.memory.total)
                      }}
                    ></div>
                  </div>
                  
                  <div className="flex items-center justify-between text-sm text-gray-600">
                    <span>使用率</span>
                    <span>{formatPercentage(health.memory.used, health.memory.total)}</span>
                  </div>
                </div>
              </div>
            </div>
          )}

          {/* CPU使用情况 */}
          {health.cpu && (
            <div className="card">
              <div className="card-header">
                <h3 className="text-lg font-medium text-gray-900 flex items-center">
                  <Cpu className="h-5 w-5 mr-2" />
                  CPU使用情况
                </h3>
              </div>
              <div className="card-body">
                <div className="space-y-4">
                  <div className="flex items-center justify-between">
                    <span className="text-sm font-medium text-gray-700">CPU使用率</span>
                    <span className="text-sm text-gray-900">
                      {health.cpu.usage.toFixed(1)}%
                    </span>
                  </div>
                  
                  <div className="w-full bg-gray-200 rounded-full h-2">
                    <div 
                      className={`h-2 rounded-full transition-all duration-300 ${
                        health.cpu.usage > 80 ? 'bg-red-600' : 
                        health.cpu.usage > 60 ? 'bg-yellow-600' : 'bg-green-600'
                      }`}
                      style={{ width: `${health.cpu.usage}%` }}
                    ></div>
                  </div>
                  
                  <div className="flex items-center justify-between text-sm text-gray-600">
                    <span>状态</span>
                    <span className={
                      health.cpu.usage > 80 ? 'text-red-600' : 
                      health.cpu.usage > 60 ? 'text-yellow-600' : 'text-green-600'
                    }>
                      {health.cpu.usage > 80 ? '高负载' : 
                       health.cpu.usage > 60 ? '中等负载' : '正常'}
                    </span>
                  </div>
                </div>
              </div>
            </div>
          )}
        </div>
      )}

      {/* 服务状态 */}
      <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
        {/* 代理服务状态 */}
        <div className="card">
          <div className="card-header">
            <h3 className="text-lg font-medium text-gray-900 flex items-center">
              <Network className="h-5 w-5 mr-2" />
              代理服务状态
            </h3>
          </div>
          <div className="card-body">
            {proxyStats ? (
              <div className="space-y-4">
                <div className="grid grid-cols-2 gap-4">
                  <div className="text-center">
                    <div className="text-2xl font-bold text-green-600">{proxyStats.active}</div>
                    <div className="text-sm text-gray-500">正常代理</div>
                  </div>
                  <div className="text-center">
                    <div className="text-2xl font-bold text-red-600">{proxyStats.failed}</div>
                    <div className="text-sm text-gray-500">失败代理</div>
                  </div>
                </div>
                
                <div className="border-t pt-4">
                  <div className="flex items-center justify-between text-sm">
                    <span className="text-gray-700">总代理数</span>
                    <span className="font-medium">{proxyStats.total}</span>
                  </div>
                  <div className="flex items-center justify-between text-sm mt-2">
                    <span className="text-gray-700">可用率</span>
                    <span className="font-medium">
                      {proxyStats.total > 0 ? 
                        formatPercentage(proxyStats.active, proxyStats.total) : 
                        '0%'
                      }
                    </span>
                  </div>
                </div>
              </div>
            ) : (
              <div className="text-center text-gray-500">暂无数据</div>
            )}
          </div>
        </div>

        {/* 任务执行状态 */}
        <div className="card">
          <div className="card-header">
            <h3 className="text-lg font-medium text-gray-900 flex items-center">
              <Activity className="h-5 w-5 mr-2" />
              任务执行状态
            </h3>
          </div>
          <div className="card-body">
            {taskStats ? (
              <div className="space-y-4">
                <div className="grid grid-cols-2 gap-4">
                  <div className="text-center">
                    <div className="text-2xl font-bold text-blue-600">{taskStats.running}</div>
                    <div className="text-sm text-gray-500">运行中</div>
                  </div>
                  <div className="text-center">
                    <div className="text-2xl font-bold text-gray-600">{taskStats.pending}</div>
                    <div className="text-sm text-gray-500">等待中</div>
                  </div>
                </div>
                
                <div className="border-t pt-4">
                  <div className="flex items-center justify-between text-sm">
                    <span className="text-gray-700">已完成</span>
                    <span className="font-medium text-green-600">{taskStats.completed}</span>
                  </div>
                  <div className="flex items-center justify-between text-sm mt-2">
                    <span className="text-gray-700">失败</span>
                    <span className="font-medium text-red-600">{taskStats.failed}</span>
                  </div>
                  <div className="flex items-center justify-between text-sm mt-2">
                    <span className="text-gray-700">成功率</span>
                    <span className="font-medium">
                      {taskStats.total > 0 ? 
                        formatPercentage(taskStats.completed, taskStats.total) : 
                        '0%'
                      }
                    </span>
                  </div>
                </div>
              </div>
            ) : (
              <div className="text-center text-gray-500">暂无数据</div>
            )}
          </div>
        </div>
      </div>

      {/* 系统日志和事件 */}
      <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
        {/* 系统日志 */}
        <div className="card">
          <div className="card-header">
            <h3 className="text-lg font-medium text-gray-900 flex items-center">
              <AlertTriangle className="h-5 w-5 mr-2" />
              系统日志
            </h3>
          </div>
          <div className="card-body">
            <div className="space-y-3">
              <div className="text-center py-8 text-gray-500">
                <div className="text-sm">系统日志功能开发中...</div>
                <div className="text-xs mt-1">将显示系统错误、警告和信息日志</div>
              </div>
            </div>
          </div>
        </div>

        {/* 系统事件 */}
        <div className="card">
          <div className="card-header">
            <h3 className="text-lg font-medium text-gray-900 flex items-center">
              <Activity className="h-5 w-5 mr-2" />
              系统事件
            </h3>
          </div>
          <div className="card-body">
            <div className="space-y-3">
              <div className="text-center py-8 text-gray-500">
                <div className="text-sm">系统事件功能开发中...</div>
                <div className="text-xs mt-1">将显示代理状态变化、任务执行等事件</div>
              </div>
            </div>
          </div>
        </div>
      </div>

      {/* 网络和磁盘状态 */}
      <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
        {/* 网络状态 */}
        <div className="card">
          <div className="card-header">
            <h3 className="text-lg font-medium text-gray-900 flex items-center">
              <Network className="h-5 w-5 mr-2" />
              网络状态
            </h3>
          </div>
          <div className="card-body">
            <div className="space-y-3">
              <div className="text-center py-8 text-gray-500">
                <div className="text-sm">网络监控功能开发中...</div>
                <div className="text-xs mt-1">将显示网络连接状态、带宽使用等信息</div>
              </div>
            </div>
          </div>
        </div>

        {/* 磁盘使用 */}
        <div className="card">
          <div className="card-header">
            <h3 className="text-lg font-medium text-gray-900 flex items-center">
              <HardDrive className="h-5 w-5 mr-2" />
              磁盘使用
            </h3>
          </div>
          <div className="card-body">
            <div className="space-y-3">
              <div className="text-center py-8 text-gray-500">
                <div className="text-sm">磁盘监控功能开发中...</div>
                <div className="text-xs mt-1">将显示磁盘空间使用情况和I/O统计</div>
              </div>
            </div>
          </div>
        </div>
      </div>

      {/* 性能指标 */}
      <div className="card">
        <div className="card-header">
          <h3 className="text-lg font-medium text-gray-900 flex items-center">
            <BarChart3 className="h-5 w-5 mr-2" />
            性能指标
          </h3>
        </div>
        <div className="card-body">
          <div className="text-center py-8 text-gray-500">
            <div className="text-sm">性能监控功能开发中...</div>
            <div className="text-xs mt-1">将显示响应时间、吞吐量、错误率等性能指标图表</div>
          </div>
        </div>
      </div>
    </div>
  )
}

export default SystemMonitoring
