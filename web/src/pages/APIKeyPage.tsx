import React from 'react';
import { Key, Shield, Code, BookOpen } from 'lucide-react';
import { EnhancedAPIKeyManager } from '../components/EnhancedAPIKeyManager';

export const APIKeyPage: React.FC = () => {
  return (
    <div className="space-y-6">
      {/* Page Header */}
      <div className="bg-white rounded-lg shadow-sm border p-6">
        <div className="flex items-center space-x-3 mb-4">
          <Key className="h-8 w-8 text-blue-600" />
          <div>
            <h1 className="text-2xl font-bold text-gray-900">API密钥管理</h1>
            <p className="text-gray-600">管理您的API密钥以进行程序化访问</p>
          </div>
        </div>
      </div>

      {/* API Key Manager */}
      <EnhancedAPIKeyManager />

      {/* Documentation and Security Info */}
      <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
        {/* API Documentation */}
        <div className="bg-white rounded-lg shadow-sm border p-6">
          <div className="flex items-center space-x-3 mb-4">
            <BookOpen className="h-6 w-6 text-green-600" />
            <h2 className="text-lg font-semibold text-gray-900">API文档</h2>
          </div>
          <div className="space-y-4">
            <div>
              <h3 className="font-medium text-gray-900 mb-2">身份验证</h3>
              <p className="text-sm text-gray-600 mb-2">
                在请求头中包含您的API密钥：
              </p>
              <div className="bg-gray-50 rounded p-3 font-mono text-sm">
                X-API-Key: your_api_key_here
              </div>
            </div>
            <div>
              <h3 className="font-medium text-gray-900 mb-2">基础URL</h3>
              <div className="bg-gray-50 rounded p-3 font-mono text-sm">
                {window.location.origin}/api/v1
              </div>
            </div>
            <div>
              <h3 className="font-medium text-gray-900 mb-2">请求示例</h3>
              <div className="bg-gray-50 rounded p-3 font-mono text-sm">
                <div>curl -H "X-API-Key: your_api_key" \</div>
                <div className="ml-4">{window.location.origin}/api/v1/proxies</div>
              </div>
            </div>
          </div>
        </div>

        {/* Security Best Practices */}
        <div className="bg-white rounded-lg shadow-sm border p-6">
          <div className="flex items-center space-x-3 mb-4">
            <Shield className="h-6 w-6 text-red-600" />
            <h2 className="text-lg font-semibold text-gray-900">安全最佳实践</h2>
          </div>
          <div className="space-y-3">
            <div className="flex items-start space-x-3">
              <div className="w-2 h-2 bg-red-600 rounded-full mt-2"></div>
              <p className="text-sm text-gray-700">
                <strong>保护您的API密钥。</strong> 切勿公开分享或提交到版本控制系统中。
              </p>
            </div>
            <div className="flex items-start space-x-3">
              <div className="w-2 h-2 bg-red-600 rounded-full mt-2"></div>
              <p className="text-sm text-gray-700">
                <strong>使用环境变量</strong> 在应用程序中存储您的API密钥。
              </p>
            </div>
            <div className="flex items-start space-x-3">
              <div className="w-2 h-2 bg-red-600 rounded-full mt-2"></div>
              <p className="text-sm text-gray-700">
                <strong>定期重新生成。</strong> 考虑定期轮换您的API密钥以提高安全性。
              </p>
            </div>
            <div className="flex items-start space-x-3">
              <div className="w-2 h-2 bg-red-600 rounded-full mt-2"></div>
              <p className="text-sm text-gray-400">
                <strong>Monitor usage.</strong> Keep track of your API key usage and watch for unexpected activity.
              </p>
            </div>
            <div className="flex items-start space-x-3">
              <div className="w-2 h-2 bg-red-600 rounded-full mt-2"></div>
              <p className="text-sm text-gray-700">
                <strong>Use HTTPS only.</strong> Always make API requests over secure connections.
              </p>
            </div>
          </div>
        </div>
      </div>

      {/* Available Endpoints */}
      <div className="bg-white rounded-lg shadow-sm border p-6">
        <div className="flex items-center space-x-3 mb-4">
          <Code className="h-6 w-6 text-purple-600" />
          <h2 className="text-lg font-semibold text-gray-900">Available API Endpoints</h2>
        </div>
        <div className="overflow-x-auto">
          <table className="min-w-full divide-y divide-gray-200">
            <thead className="bg-gray-50">
              <tr>
                <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                  Method
                </th>
                <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                  Endpoint
                </th>
                <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                  Description
                </th>
              </tr>
            </thead>
            <tbody className="bg-white divide-y divide-gray-200">
              <tr>
                <td className="px-6 py-4 whitespace-nowrap text-sm font-mono text-green-600">GET</td>
                <td className="px-6 py-4 whitespace-nowrap text-sm font-mono text-gray-900">/api/v1/proxies</td>
                <td className="px-6 py-4 text-sm text-gray-700">List all proxies</td>
              </tr>
              <tr>
                <td className="px-6 py-4 whitespace-nowrap text-sm font-mono text-blue-600">POST</td>
                <td className="px-6 py-4 whitespace-nowrap text-sm font-mono text-gray-900">/api/v1/proxies</td>
                <td className="px-6 py-4 text-sm text-gray-700">Create a new proxy</td>
              </tr>
              <tr>
                <td className="px-6 py-4 whitespace-nowrap text-sm font-mono text-yellow-600">PUT</td>
                <td className="px-6 py-4 whitespace-nowrap text-sm font-mono text-gray-900">/api/v1/proxies/:id</td>
                <td className="px-6 py-4 text-sm text-gray-700">Update a proxy</td>
              </tr>
              <tr>
                <td className="px-6 py-4 whitespace-nowrap text-sm font-mono text-red-600">DELETE</td>
                <td className="px-6 py-4 whitespace-nowrap text-sm font-mono text-gray-900">/api/v1/proxies/:id</td>
                <td className="px-6 py-4 text-sm text-gray-700">Delete a proxy</td>
              </tr>
              <tr>
                <td className="px-6 py-4 whitespace-nowrap text-sm font-mono text-green-600">GET</td>
                <td className="px-6 py-4 whitespace-nowrap text-sm font-mono text-gray-900">/api/v1/tasks</td>
                <td className="px-6 py-4 text-sm text-gray-700">List all tasks</td>
              </tr>
              <tr>
                <td className="px-6 py-4 whitespace-nowrap text-sm font-mono text-blue-600">POST</td>
                <td className="px-6 py-4 whitespace-nowrap text-sm font-mono text-gray-900">/api/v1/tasks</td>
                <td className="px-6 py-4 text-sm text-gray-700">Create a new task</td>
              </tr>
              <tr>
                <td className="px-6 py-4 whitespace-nowrap text-sm font-mono text-blue-600">POST</td>
                <td className="px-6 py-4 whitespace-nowrap text-sm font-mono text-gray-900">/api/v1/proxies/batch-import</td>
                <td className="px-6 py-4 text-sm text-gray-700">Batch import proxies</td>
              </tr>
            </tbody>
          </table>
        </div>
      </div>
    </div>
  );
};
