import React, { useState, useEffect } from 'react'
import {
  Server,
  ListTodo,
  Activity,
  Clock,
  TrendingUp,
  AlertTriangle,
  Refresh<PERSON>w,
  TestTube
} from 'lucide-react'
import { proxyAPI, taskAPI } from '../services/api'
import LoadingSpinner from '../components/common/LoadingSpinner'
import StatsCard from '../components/dashboard/StatsCard'
import ProxyStatus<PERSON>hart from '../components/dashboard/ProxyStatusChart'
import TaskStatus<PERSON>hart from '../components/dashboard/TaskStatusChart'
import QualityScoreChart from '../components/dashboard/QualityScoreChart'
import RecentTasks from '../components/dashboard/RecentTasks'
// import TrendChart from '../components/dashboard/TrendChart' // TODO: 等待后端趋势数据API
import { simpleToast } from '../components/common/SimpleToast'
import { REFRESH_INTERVALS } from '../utils/constants'
import toast from 'react-hot-toast'
import type { ProxyStats, TaskStats, Task, Proxy } from '../types'

interface DashboardStats {
  proxy: ProxyStats | null
  task: TaskStats | null
}

const Dashboard: React.FC = () => {
  const [stats, setStats] = useState<DashboardStats>({
    proxy: null,
    task: null,
  })
  const [recentTasks, setRecentTasks] = useState<Task[]>([])
  const [proxies, setProxies] = useState<Proxy[]>([])
  const [loading, setLoading] = useState<boolean>(true)
  const [refreshing, setRefreshing] = useState<boolean>(false)
  // const [trendData, setTrendData] = useState<any[]>([]) // TODO: 等待后端趋势数据API

  // TODO: 等待后端实现历史趋势数据API
  // 当前暂时移除趋势图表功能

  // 获取统计数据
  const fetchStats = async (showLoading = true): Promise<void> => {
    try {
      if (showLoading) setLoading(true)
      else setRefreshing(true)

      const [proxyStatsRes, taskStatsRes, tasksRes, proxiesRes] = await Promise.all([
        proxyAPI.getProxyStats(),
        taskAPI.getTaskStats(),
        taskAPI.getTasks(),
        proxyAPI.getAllProxies() // 使用getAllProxies获取所有代理用于仪表板统计
      ])

      setStats({
        proxy: proxyStatsRes.data.data!,
        task: taskStatsRes.data.data!,
      })

      // 获取最近的任务（最多10个）
      const tasks = tasksRes.data.data || []
      setRecentTasks(tasks.slice(0, 10))

      // 设置代理数据用于质量评分图表
      setProxies(proxiesRes.data.data || [])

      // TODO: 等待后端实现历史趋势数据API后再启用
      // setTrendData(trendDataFromAPI)

    } catch (error) {
      console.error('Failed to fetch dashboard data:', error)
    } finally {
      setLoading(false)
      setRefreshing(false)
    }
  }

  // 初始加载和定时刷新
  useEffect(() => {
    fetchStats()
    
    const interval = setInterval(() => {
      fetchStats(false)
    }, REFRESH_INTERVALS.DASHBOARD)
    
    return () => clearInterval(interval)
  }, [])

  if (loading) {
    return <LoadingSpinner size="lg" text="加载仪表板数据..." />
  }

  const proxyStats = stats.proxy || { total: 0, active: 0, inactive: 0, failed: 0 }
  const taskStats = stats.task || { total: 0, pending: 0, running: 0, completed: 0, failed: 0, cancelled: 0 }

  return (
    <div className="space-y-6">
      {/* 页面标题和控制 */}
      <div className="flex flex-col sm:flex-row sm:items-center sm:justify-between">
        <div>
          <h1 className="text-2xl font-bold text-gray-900">仪表板</h1>
          <p className="mt-1 text-sm text-gray-500">
            系统概览和实时监控
          </p>
        </div>
        <div className="flex items-center space-x-3 mt-4 sm:mt-0">
          <button
            onClick={() => fetchStats(false)}
            disabled={refreshing}
            className="btn-outline"
          >
            <RefreshCw className={`h-4 w-4 mr-2 ${refreshing ? 'animate-spin' : ''}`} />
            刷新数据
          </button>
        </div>
      </div>

      {/* 仪表盘内容 */}
      {/* 统计卡片 */}
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
        <StatsCard
          title="代理总数"
          value={proxyStats.total}
          icon={Server}
          color="blue"
          trend={proxyStats.active > 0 ? 'up' : 'down'}
          trendValue={`${proxyStats.active} 个正常`}
        />
        
        <StatsCard
          title="任务总数"
          value={taskStats.total}
          icon={ListTodo}
          color="green"
          trend={taskStats.running > 0 ? 'up' : 'neutral'}
          trendValue={`${taskStats.running} 个运行中`}
        />
        
        <StatsCard
          title="成功率"
          value={taskStats.total > 0 ? 
            `${Math.round((taskStats.completed) / taskStats.total * 100)}%` : 
            '0%'
          }
          icon={TrendingUp}
          color="purple"
          trend={taskStats.completed > taskStats.failed ? 'up' : 'down'}
          trendValue={`${taskStats.completed} 个完成`}
        />
        
        <StatsCard
          title="失败任务"
          value={taskStats.failed}
          icon={AlertTriangle}
          color="red"
          trend={taskStats.failed > 0 ? 'down' : 'neutral'}
          trendValue={taskStats.failed > 0 ? '需要关注' : '运行正常'}
        />
      </div>

      {/* 图表区域 */}
      <div className="grid grid-cols-1 lg:grid-cols-2 xl:grid-cols-3 gap-6">
        <div className="card">
          <div className="card-header">
            <h3 className="text-lg font-medium text-gray-900">代理状态分布</h3>
          </div>
          <div className="card-body">
            <ProxyStatusChart data={proxyStats} />
          </div>
        </div>

        <div className="card">
          <div className="card-header">
            <h3 className="text-lg font-medium text-gray-900">任务状态分布</h3>
          </div>
          <div className="card-body">
            <TaskStatusChart data={taskStats} />
          </div>
        </div>

        <div className="card">
          <div className="card-header">
            <h3 className="text-lg font-medium text-gray-900">代理质量分布</h3>
          </div>
          <div className="card-body">
            <QualityScoreChart proxies={proxies} />
          </div>
        </div>
      </div>

      {/* 最近任务 */}
      <div className="card">
        <div className="card-header">
          <h3 className="text-lg font-medium text-gray-900">最近任务</h3>
        </div>
        <div className="card-body">
          <RecentTasks tasks={recentTasks} />
        </div>
      </div>

      {/* 系统状态 */}
      <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
        <div className="card">
          <div className="card-body">
            <div className="flex items-center">
              <div className="flex-shrink-0">
                <Activity className="h-8 w-8 text-green-500" />
              </div>
              <div className="ml-4">
                <h4 className="text-lg font-medium text-gray-900">系统状态</h4>
                <p className="text-sm text-green-600">运行正常</p>
              </div>
            </div>
          </div>
        </div>

        <div className="card">
          <div className="card-body">
            <div className="flex items-center">
              <div className="flex-shrink-0">
                <Clock className="h-8 w-8 text-blue-500" />
              </div>
              <div className="ml-4">
                <h4 className="text-lg font-medium text-gray-900">运行时间</h4>
                <p className="text-sm text-gray-600">24小时7分钟</p>
              </div>
            </div>
          </div>
        </div>

        <div className="card">
          <div className="card-body">
            <div className="flex items-center">
              <div className="flex-shrink-0">
                <Server className="h-8 w-8 text-purple-500" />
              </div>
              <div className="ml-4">
                <h4 className="text-lg font-medium text-gray-900">活跃连接</h4>
                <p className="text-sm text-gray-600">{proxyStats.active} 个代理</p>
              </div>
            </div>
          </div>
        </div>
      </div>

      {/* TODO: 趋势图表 - 等待后端实现历史数据API */}
      <div className="card">
        <div className="card-body">
          <div className="text-center py-8 text-gray-500">
            <div className="text-lg font-medium mb-2">趋势分析</div>
            <div className="text-sm">等待后端实现历史数据API...</div>
          </div>
        </div>
      </div>

      {/* Toast 测试区域 - 仅在开发环境显示 */}
      {import.meta.env.DEV && false && (
        <div className="card">
          <div className="card-header">
            <h3 className="text-lg font-medium text-gray-900 flex items-center">
              <TestTube className="h-5 w-5 mr-2" />
              Toast 测试区域 (开发环境)
            </h3>
          </div>
          <div className="card-body">
            <div className="grid grid-cols-2 md:grid-cols-4 gap-4">
              <button
                onClick={() => simpleToast.success('这是一个成功消息！可以点击右侧 X 关闭')}
                className="btn-primary"
              >
                成功 Toast
              </button>

              <button
                onClick={() => simpleToast.error('这是一个错误消息！可以点击右侧 X 关闭')}
                className="btn-danger"
              >
                错误 Toast
              </button>

              <button
                onClick={() => simpleToast.warning('这是一个警告消息！可以点击右侧 X 关闭')}
                className="btn-secondary"
              >
                警告 Toast
              </button>

              <button
                onClick={() => simpleToast.info('这是一个信息消息！可以点击右侧 X 关闭')}
                className="btn-outline"
              >
                信息 Toast
              </button>

              <button
                onClick={() => simpleToast.persistent.info('这是一个持久消息，不会自动消失，只能手动关闭')}
                className="btn-outline"
              >
                持久 Toast
              </button>

              <button
                onClick={() => toast.success('这是原生 toast（无关闭按钮）')}
                className="btn-outline"
              >
                原生 Toast
              </button>

              <button
                onClick={() => toast.dismiss()}
                className="btn-outline"
              >
                关闭所有 Toast
              </button>

              <button
                onClick={() => {
                  simpleToast.success('批量操作成功！')
                  simpleToast.info('正在处理后续任务...')
                  setTimeout(() => simpleToast.warning('请注意检查结果'), 1000)
                }}
                className="btn-outline"
              >
                批量 Toast
              </button>
            </div>
            <p className="mt-4 text-sm text-gray-500">
              新的 Toast 组件支持手动关闭功能，点击右侧的 X 按钮即可关闭。持久 Toast 不会自动消失，必须手动关闭。
            </p>
          </div>
        </div>
      )}
    </div>
  )
}

export default Dashboard
