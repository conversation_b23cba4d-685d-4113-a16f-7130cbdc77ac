import React, { useState, useEffect, useMemo } from 'react'
import {
  Plus,
  RefreshCw,
  Search,
  Trash2,
  Activity,
  Server,
  Upload,
  MapPin,
  Globe,
  BarChart3,
  Grid,
  List,
  ChevronLeft,
  ChevronRight
} from 'lucide-react'
import { proxyAPI } from '../services/api'
import LoadingSpinner from '../components/common/LoadingSpinner'
import StatusBadge from '../components/common/StatusBadge'
import Modal from '../components/common/Modal'
import AddProxyForm from '../components/proxy/AddProxyForm'
import BatchAddProxyForm from '../components/proxy/BatchAddProxyForm'
import QualityScoreDisplay from '../components/proxy/QualityScoreDisplay'
import LocationStatsCard from '../components/LocationStatsCard'
import LocationOverview from '../components/LocationOverview'
import { formatDate, formatResponseTime } from '../utils/helpers'
import { REFRESH_INTERVALS } from '../utils/constants'
import toast from 'react-hot-toast'
import type { 
  Proxy, 
  LocationFilter as LocationFilterType, 
  ProxyLocationStats, 
  ProxyGroupByLocation,
  BackendPaginatedResponse,
  ProxyListParams
} from '../types'

const ProxyManagement: React.FC = () => {
  const [proxies, setProxies] = useState<Proxy[]>([])
  const [loading, setLoading] = useState<boolean>(true)
  const [refreshing, setRefreshing] = useState<boolean>(false)
  const [searchTerm, setSearchTerm] = useState<string>('')
  const [showAddModal, setShowAddModal] = useState<boolean>(false)
  const [showBatchAddModal, setShowBatchAddModal] = useState<boolean>(false)
  const [deletingId, setDeletingId] = useState<string | null>(null)

  // 分页相关状态
  const [pagination, setPagination] = useState({
    page: 1,
    limit: 20,
    total: 0,
    total_pages: 0,
    has_next: false,
    has_prev: false
  })

  // 筛选相关状态
  const [filters, setFilters] = useState<ProxyListParams>({
    page: 1,
    limit: 10
  })

  // 地理位置相关状态
  const [locationStats, setLocationStats] = useState<ProxyLocationStats | null>(null)
  const [_locationGroups, setLocationGroups] = useState<ProxyGroupByLocation[]>([])
  const [locationFilter, _setLocationFilter] = useState<LocationFilterType>({
    countries: [],
    cities: [],
    searchTerm: ''
  })
  const [viewMode, setViewMode] = useState<'list' | 'location'>('list')
  const [groupBy, setGroupBy] = useState<'country' | 'city'>('country')
  const [showLocationStats, setShowLocationStats] = useState<boolean>(false)

  // 批量操作相关状态
  const [selectedProxies, setSelectedProxies] = useState<string[]>([])
  const [isSelectMode, setIsSelectMode] = useState<boolean>(false)
  const [batchTesting, setBatchTesting] = useState<boolean>(false)
  const [testingAll, setTestingAll] = useState<boolean>(false)

  const [healthCheckingIds, setHealthCheckingIds] = useState<Set<string>>(new Set())
  const [assessingQuality, setAssessingQuality] = useState<boolean>(false)
  const [assessingIds, setAssessingIds] = useState<Set<string>>(new Set())

  // 获取代理列表
  const fetchProxies = async (showLoading = true): Promise<void> => {
    try {
      if (showLoading) setLoading(true)
      else setRefreshing(true)

      const response = await proxyAPI.getProxies(filters)
      const data = response.data

      setProxies(data.data || [])
      setPagination({
        page: data.page,
        limit: data.limit,
        total: data.total,
        total_pages: data.total_pages,
        has_next: data.has_next,
        has_prev: data.has_prev
      })
    } catch (error) {
      console.error('Failed to fetch proxies:', error)
      toast.error('获取代理列表失败')
    } finally {
      setLoading(false)
      setRefreshing(false)
    }
  }

  // 更新筛选条件并重新获取数据
  const updateFilters = (newFilters: Partial<ProxyListParams>) => {
    const updatedFilters = { ...filters, ...newFilters, page: 1 } // 重置到第一页
    setFilters(updatedFilters)
  }

  // 分页处理函数
  const handlePageChange = (newPage: number) => {
    if (newPage >= 1 && newPage <= pagination.total_pages) {
      setFilters({ ...filters, page: newPage })
    }
  }

  // 搜索处理函数
  const handleSearch = () => {
    updateFilters({ search: searchTerm || undefined })
  }

  // 重置搜索
  const handleClearSearch = () => {
    setSearchTerm('')
    updateFilters({ search: undefined })
  }

  // 获取地理位置统计
  const fetchLocationStats = async (): Promise<void> => {
    try {
      const response = await proxyAPI.getProxyLocationStats()
      setLocationStats(response.data.data || null)
    } catch (error) {
      console.error('Failed to fetch location stats:', error)
    }
  }

  // 获取地理位置分组
  const fetchLocationGroups = async (): Promise<void> => {
    try {
      const response = await proxyAPI.groupProxiesByLocation(groupBy)
      setLocationGroups(response.data.data || [])
    } catch (error) {
      console.error('Failed to fetch location groups:', error)
    }
  }

  // 删除代理
  const handleDelete = async (id: string): Promise<void> => {
    if (!confirm('确定要删除这个代理吗？')) return

    try {
      setDeletingId(id)
      await proxyAPI.deleteProxy(id)
      toast.success('代理删除成功')
      fetchProxies(false)
    } catch (error) {
      toast.error('删除代理失败')
    } finally {
      setDeletingId(null)
    }
  }

  // 健康检查
  const handleHealthCheck = async (id: string): Promise<void> => {
    setHealthCheckingIds(prev => new Set(prev).add(id))
    try {
      const result = await proxyAPI.healthCheck(id)
      toast.success('健康检查成功')
      
      // 如果API返回了健康检查结果，立即更新本地状态
      if (result?.data?.data) {
        const healthCheck = result.data.data as any
        const now = new Date().toISOString()
        
        setProxies(prev => prev.map(proxy =>
          proxy.id === id ? {
            ...proxy,
            status: healthCheck.status || proxy.status,
            response_time: healthCheck.response_time || proxy.response_time,
            last_check: now,
            failures: healthCheck.status === 'failed' ? (proxy.failures || 0) + 1 : 0
          } : proxy
        ))
        
        console.log(`健康检查状态已更新 - 代理 ${id}:`, {
          status: healthCheck.status,
          response_time: healthCheck.response_time
        })
      }
      
      // 延迟刷新以确保获取最新状态（增加延迟时间）
      setTimeout(() => fetchProxies(false), 1000)
    } catch (error) {
      toast.error('健康检查失败')
    } finally {
      setHealthCheckingIds(prev => {
        const next = new Set(prev)
        next.delete(id)
        return next
      })
    }
  }

  // 批量健康检查
  const handleBatchHealthCheck = async (): Promise<void> => {
    if (selectedProxies.length === 0) {
      toast.error('请先选择要测试的代理')
      return
    }

    try {
      setBatchTesting(true)
      await proxyAPI.batchHealthCheck(selectedProxies)
      toast.success(`已启动 ${selectedProxies.length} 个代理的健康检查`)
      // 延迟刷新以获取最新状态
      setTimeout(() => {
        fetchProxies(false)
        setSelectedProxies([])
        setIsSelectMode(false)
      }, 1000)
    } catch (error) {
      toast.error('批量健康检查失败')
    } finally {
      setBatchTesting(false)
    }
  }

  // 测试所有代理
  const handleTestAll = async (): Promise<void> => {
    if (filteredProxies.length === 0) {
      toast.error('没有可测试的代理')
      return
    }

    try {
      setTestingAll(true)
      await proxyAPI.healthCheckAll()
      toast.success(`已启动所有 ${filteredProxies.length} 个代理的健康检查`)
      // 延迟刷新以获取最新状态
      setTimeout(() => fetchProxies(false), 1000)
    } catch (error) {
      toast.error('测试所有代理失败')
    } finally {
      setTestingAll(false)
    }
  }

  // 评估单个代理质量
  const handleAssessQuality = async (id: string): Promise<void> => {
    setAssessingIds(prev => new Set(prev).add(id))
    try {
      const result = await proxyAPI.assessProxyQuality(id)
      toast.success('质量评估成功')

      // 如果API返回了质量评分数据，立即更新本地状态
      if (result?.data?.data) {
        const qualityScore = result.data.data

        // 验证返回的数据结构
        if (typeof qualityScore.overall === 'number' &&
            typeof qualityScore.speed === 'number' &&
            typeof qualityScore.stability === 'number' &&
            typeof qualityScore.reliability === 'number') {

          setProxies(prev => prev.map(proxy =>
            proxy.id === id ? {
              ...proxy,
              quality_score: qualityScore.overall,
              speed_score: qualityScore.speed,
              stability_score: qualityScore.stability,
              reliability_score: qualityScore.reliability,
              anonymity_level: qualityScore.anonymity_level || proxy.anonymity_level,
              last_quality_check: new Date().toISOString()
            } : proxy
          ))

          console.log(`质量评分已更新 - 代理 ${id}:`, {
            overall: qualityScore.overall,
            speed: qualityScore.speed,
            stability: qualityScore.stability,
            reliability: qualityScore.reliability,
            anonymity: qualityScore.anonymity_level
          })
        } else {
          console.warn('质量评分数据格式不正确:', qualityScore)
        }
      } else {
        console.warn('质量评估API未返回数据，将通过延迟刷新获取最新状态')
      }

      // 延迟刷新以确保获取最新状态（质量评估需要更长时间）
      setTimeout(() => fetchProxies(false), 3000)
    } catch (error) {
      toast.error('质量评估失败')
    } finally {
      setAssessingIds(prev => {
        const next = new Set(prev)
        next.delete(id)
        return next
      })
    }
  }

  // 批量质量评估
  const handleBatchAssessQuality = async (): Promise<void> => {
    if (selectedProxies.length === 0) {
      toast.error('请先选择要评估的代理')
      return
    }

    try {
      setAssessingQuality(true)
      await proxyAPI.batchAssessQuality(selectedProxies)
      toast.success(`已启动 ${selectedProxies.length} 个代理的质量评估`)

      // 记录开始时间用于跟踪
      const startTime = Date.now()
      console.log(`批量质量评估开始 - ${selectedProxies.length} 个代理`, {
        proxyIds: selectedProxies,
        startTime: new Date(startTime).toISOString()
      })

      // 多次延迟刷新以确保获取最新状态（质量评估是异步过程）
      const refreshTimes = [2000, 5000, 10000, 15000] // 2秒、5秒、10秒、15秒后刷新
      refreshTimes.forEach((delay, index) => {
        setTimeout(() => {
          console.log(`批量质量评估 - 第${index + 1}次刷新 (${delay}ms后)`)
          fetchProxies(false)
        }, delay)
      })

      // 清理选择状态
      setTimeout(() => {
        setSelectedProxies([])
        setIsSelectMode(false)
      }, 1000)
    } catch (error) {
      toast.error('批量质量评估失败')
    } finally {
      setAssessingQuality(false)
    }
  }

  // 切换代理选择
  const toggleProxySelection = (proxyId: string): void => {
    setSelectedProxies(prev =>
      prev.includes(proxyId)
        ? prev.filter(id => id !== proxyId)
        : [...prev, proxyId]
    )
  }

  // 全选/取消全选
  const toggleSelectAll = (): void => {
    if (selectedProxies.length === filteredProxies.length) {
      setSelectedProxies([])
    } else {
      setSelectedProxies(filteredProxies.map(p => p.id))
    }
  }

  // 过滤代理（现在主要用于地理位置筛选，搜索由后端处理）
  const filteredProxies = useMemo(() => {
    return proxies.filter(proxy => {
      // 地理位置筛选
      const matchesLocationFilter = !locationFilter.searchTerm || (
        proxy.host.toLowerCase().includes(locationFilter.searchTerm.toLowerCase()) ||
        (proxy.country_code && proxy.country_code.toLowerCase().includes(locationFilter.searchTerm.toLowerCase())) ||
        (proxy.city_name && proxy.city_name.toLowerCase().includes(locationFilter.searchTerm.toLowerCase()))
      )

      // 国家筛选
      const matchesCountryFilter = locationFilter.countries.length === 0 ||
        (proxy.country_code && locationFilter.countries.includes(proxy.country_code))

      // 城市筛选
      const matchesCityFilter = locationFilter.cities.length === 0 ||
        (proxy.country_code && proxy.city_name &&
         locationFilter.cities.includes(`${proxy.country_code}_${proxy.city_name}`))

      return matchesLocationFilter && matchesCountryFilter && matchesCityFilter
    })
  }, [proxies, locationFilter])

  // 当筛选条件变化时重新获取数据
  useEffect(() => {
    fetchProxies()
  }, [filters])

  // 初始加载地理位置统计
  useEffect(() => {
    fetchLocationStats()

    const interval = setInterval(() => {
      fetchLocationStats()
    }, REFRESH_INTERVALS.PROXY_LIST)

    return () => clearInterval(interval)
  }, [])

  // 当分组方式改变时重新获取分组数据
  useEffect(() => {
    if (viewMode === 'location') {
      fetchLocationGroups()
    }
  }, [groupBy, viewMode])

  if (loading) {
    return <LoadingSpinner size="lg" text="加载代理列表..." />
  }

  return (
    <div className="space-y-6">
      {/* 页面标题和操作 */}
      <div className="flex flex-col sm:flex-row sm:items-center sm:justify-between">
        <div>
          <h1 className="text-2xl font-bold text-gray-900">代理管理</h1>
          <p className="mt-1 text-sm text-gray-500">
            管理和监控代理服务器
          </p>
        </div>
        <div className="mt-4 sm:mt-0 flex flex-wrap gap-3">

          {/* 地理位置统计按钮 */}
          <button
            onClick={() => setShowLocationStats(!showLocationStats)}
            className={`btn-outline ${showLocationStats ? 'bg-blue-50 border-blue-200 text-blue-700' : ''}`}
          >
            <BarChart3 className="h-4 w-4 mr-2" />
            位置统计
          </button>

          {/* 批量操作按钮 */}
          <button
            onClick={() => setIsSelectMode(!isSelectMode)}
            className={`btn-outline ${isSelectMode ? 'bg-purple-50 border-purple-200 text-purple-700' : ''}`}
          >
            <Grid className="h-4 w-4 mr-2" />
            {isSelectMode ? '取消选择' : '批量选择'}
          </button>

          {/* 测试所有代理按钮 */}
          <button
            onClick={handleTestAll}
            disabled={testingAll || filteredProxies.length === 0}
            className="btn-outline"
          >
            {testingAll ? <RefreshCw className="h-4 w-4 mr-2 animate-spin" />:<Activity className={`h-4 w-4 mr-2`} />}
            {testingAll ? '测试中...' : '测试全部'}
          </button>

          <button
            onClick={() => {
              fetchProxies(false)
              fetchLocationStats()
              if (viewMode === 'location') fetchLocationGroups()
            }}
            disabled={refreshing}
            className="btn-outline"
          >
            <RefreshCw className={`h-4 w-4 mr-2 ${refreshing ? 'animate-spin' : ''}`} />
            刷新
          </button>
          <button
            onClick={() => setShowAddModal(true)}
            className="btn-primary"
          >
            <Plus className="h-4 w-4 mr-2" />
            添加代理
          </button>
          <button
            onClick={() => setShowBatchAddModal(true)}
            className="btn-outline"
          >
            <Upload className="h-4 w-4 mr-2" />
            批量添加
          </button>
        </div>
      </div>

      {/* 搜索和统计 */}
      <div className="flex flex-col sm:flex-row sm:items-center sm:justify-between space-y-4 sm:space-y-0">
        <div className="flex items-center space-x-2">
          <div className="relative">
            <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-gray-400" />
            <input
              type="text"
              placeholder="搜索代理..."
              className="pl-10 input w-full sm:w-64"
              value={searchTerm}
              onChange={(e) => setSearchTerm(e.target.value)}
              onKeyDown={(e) => e.key === 'Enter' && handleSearch()}
            />
          </div>
          <button
            onClick={handleSearch}
            className="btn btn-secondary"
          >
            搜索
          </button>
          {searchTerm && (
            <button
              onClick={handleClearSearch}
              className="btn btn-outline text-gray-500"
            >
              清除
            </button>
          )}
        </div>

        <div className="flex items-center justify-between">
          <div className="flex items-center space-x-6 text-sm text-gray-600">
            <div className="flex items-center">
              <div className="w-3 h-3 bg-green-500 rounded-full mr-2"></div>
              正常: {proxies.filter(p => p.status === 'active').length}
            </div>
            <div className="flex items-center">
              <div className="w-3 h-3 bg-yellow-500 rounded-full mr-2"></div>
              未激活: {proxies.filter(p => p.status === 'inactive').length}
            </div>
            <div className="flex items-center">
              <div className="w-3 h-3 bg-red-500 rounded-full mr-2"></div>
              失败: {proxies.filter(p => p.status === 'failed').length}
            </div>
          </div>

          {/* 批量操作控制 */}
          {isSelectMode && (
            <div className="flex items-center gap-3">
              <span className="text-sm text-gray-600">
                已选择 {selectedProxies.length} 个代理
              </span>
              <button
                onClick={toggleSelectAll}
                className="text-sm text-blue-600 hover:text-blue-800"
              >
                {selectedProxies.length === filteredProxies.length ? '取消全选' : '全选'}
              </button>
              <button
                onClick={handleBatchHealthCheck}
                disabled={selectedProxies.length === 0 || batchTesting}
                className="btn-sm btn-primary"
              >
                <Activity className={`h-3 w-3 mr-1 ${batchTesting ? 'animate-spin' : ''}`} />
                {batchTesting ? '测试中...' : '测试选中'}
              </button>
              <button
                onClick={handleBatchAssessQuality}
                disabled={selectedProxies.length === 0 || assessingQuality}
                className="btn-sm btn-outline"
              >
                <BarChart3 className={`h-3 w-3 mr-1 ${assessingQuality ? 'animate-spin' : ''}`} />
                {assessingQuality ? '评估中...' : '质量评估'}
              </button>
            </div>
          )}
        </div>
      </div>

      {/* 地理位置筛选 */}
      {/* <LocationFilter
        filter={locationFilter}
        onFilterChange={setLocationFilter}
        locationStats={locationStats || undefined}
        className="card p-4"
      /> */}

      {/* 地理位置统计展示 */}
      {showLocationStats && locationStats && (
        <div className="space-y-6">
          {/* 地理位置概览 */}
          <LocationOverview locationStats={locationStats} />

          <div className="flex items-center justify-between">
            <h3 className="text-lg font-medium text-gray-900">详细统计</h3>
            <div className="flex gap-2">
              <button
                onClick={() => setGroupBy('country')}
                className={`px-3 py-1 text-sm rounded ${
                  groupBy === 'country'
                    ? 'bg-blue-100 text-blue-700'
                    : 'bg-gray-100 text-gray-600 hover:bg-gray-200'
                }`}
              >
                按国家
              </button>
              <button
                onClick={() => setGroupBy('city')}
                className={`px-3 py-1 text-sm rounded ${
                  groupBy === 'city'
                    ? 'bg-blue-100 text-blue-700'
                    : 'bg-gray-100 text-gray-600 hover:bg-gray-200'
                }`}
              >
                按城市
              </button>
            </div>
          </div>

          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4 gap-4">
            {/* 未知位置统计 */}
            {locationStats.unknown.total > 0 && (
              <LocationStatsCard
                stats={locationStats.unknown}
                type="unknown"
              />
            )}

            {/* 国家或城市统计 */}
            {(groupBy === 'country' ? locationStats.countries : locationStats.cities)
              .slice(0, 12) // 限制显示数量
              .map((stats) => (
                <LocationStatsCard
                  key={groupBy === 'country' ? stats.country_code : `${stats.country_code}_${stats.city_name}`}
                  stats={stats}
                  type={groupBy}
                />
              ))}
          </div>
        </div>
      )}

      {/* 代理列表 */}
      <div className="card">
        <div className="card-header">
          <h3 className="text-lg font-medium text-gray-900">
            代理列表 ({filteredProxies.length})
          </h3>
        </div>
        <div className="card-body p-0">
          {filteredProxies.length === 0 ? (
            <div className="text-center py-12">
              <Server className="mx-auto h-12 w-12 text-gray-400" />
              <h3 className="mt-2 text-sm font-medium text-gray-900">
                {searchTerm ? '没有找到匹配的代理' : '暂无代理'}
              </h3>
              <p className="mt-1 text-sm text-gray-500">
                {searchTerm ? '尝试调整搜索条件' : '点击上方按钮添加第一个代理'}
              </p>
            </div>
          ) : (
            <div className="overflow-x-auto">
              <table className="min-w-full divide-y divide-gray-200">
                <thead className="bg-gray-50">
                  <tr>
                    {isSelectMode && (
                      <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                        <input
                          type="checkbox"
                          checked={selectedProxies.length === filteredProxies.length && filteredProxies.length > 0}
                          onChange={toggleSelectAll}
                          className="rounded border-gray-300 text-blue-600 focus:ring-blue-500"
                        />
                      </th>
                    )}
                    <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                      代理信息
                    </th>
                    <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                      地理位置
                    </th>
                    <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                      状态
                    </th>
                    <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                      性能
                    </th>
                    <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                      质量评分
                    </th>
                    <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                      使用情况
                    </th>
                    <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                      最后检查
                    </th>
                    <th className="px-6 py-3 text-right text-xs font-medium text-gray-500 uppercase tracking-wider">
                      操作
                    </th>
                  </tr>
                </thead>
                <tbody className="bg-white divide-y divide-gray-200">
                  {filteredProxies.map((proxy) => (
                    <tr key={proxy.id} className="hover:bg-gray-50">
                      {isSelectMode && (
                        <td className="px-6 py-4 whitespace-nowrap">
                          <input
                            type="checkbox"
                            checked={selectedProxies.includes(proxy.id)}
                            onChange={() => toggleProxySelection(proxy.id)}
                            className="rounded border-gray-300 text-blue-600 focus:ring-blue-500"
                          />
                        </td>
                      )}
                      <td className="px-6 py-4 whitespace-nowrap">
                        <div>
                          <div className="text-sm font-medium text-gray-900">
                            {proxy.host}:{proxy.port}
                          </div>
                          <div className="text-sm text-gray-500">
                            {proxy.type.toUpperCase()}
                            {proxy.weight && ` • 权重: ${proxy.weight}`}
                          </div>
                        </div>
                      </td>
                      <td className="px-6 py-4 whitespace-nowrap">
                        <div className="text-sm text-gray-900">
                          {proxy.country_code || proxy.city_name ? (
                            <div className="flex items-center gap-1">
                              <MapPin className="h-3 w-3 text-gray-400" />
                              <span>
                                {proxy.city_name && proxy.country_code
                                  ? `${proxy.city_name}, ${proxy.country_code}`
                                  : proxy.country_code || proxy.city_name}
                              </span>
                            </div>
                          ) : (
                            <span className="text-gray-400 text-xs">未知位置</span>
                          )}
                          {proxy.asn_name && (
                            <div className="text-xs text-gray-500 mt-1">
                              {proxy.asn_name}
                            </div>
                          )}
                        </div>
                      </td>
                      <td className="px-6 py-4 whitespace-nowrap">
                        <StatusBadge status={proxy.status} type="proxy" />
                      </td>
                      <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-900">
                        <div>
                          <div>响应时间: {formatResponseTime(proxy.response_time)}</div>
                          {proxy.failures !== undefined && (
                            <div className="text-red-600">失败: {proxy.failures} 次</div>
                          )}
                        </div>
                      </td>
                      <td className="px-6 py-4 whitespace-nowrap">
                        <QualityScoreDisplay proxy={proxy} />
                      </td>
                      <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-900">
                        使用次数: {proxy.use_count || 0}
                      </td>
                      <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
                        {formatDate(proxy.last_check)}
                      </td>
                      <td className="px-6 py-4 whitespace-nowrap text-right text-sm font-medium">
                        <div className="flex items-center justify-end space-x-2">
                          <button
                            onClick={() => handleHealthCheck(proxy.id)}
                            className="text-blue-600 hover:text-blue-900 disabled:opacity-50"
                            title="健康检查"
                            disabled={healthCheckingIds.has(proxy.id)}
                          >
                            {healthCheckingIds.has(proxy.id) ? (
                              <RefreshCw className="h-4 w-4 animate-spin" />
                            ) : (
                              <Activity className="h-4 w-4" />
                            )}
                          </button>
                          <button
                            onClick={() => handleAssessQuality(proxy.id)}
                            className="text-purple-600 hover:text-purple-900 disabled:opacity-50"
                            title="质量评估"
                            disabled={assessingIds.has(proxy.id)}
                          >
                            {assessingIds.has(proxy.id) ? (
                              <RefreshCw className="h-4 w-4 animate-spin" />
                            ) : (
                              <BarChart3 className="h-4 w-4" />
                            )}
                          </button>
                          <button
                            onClick={() => handleDelete(proxy.id)}
                            disabled={deletingId === proxy.id}
                            className="text-red-600 hover:text-red-900 disabled:opacity-50"
                            title="删除代理"
                          >
                            {deletingId === proxy.id ? (
                              <RefreshCw className="h-4 w-4 animate-spin" />
                            ) : (
                              <Trash2 className="h-4 w-4" />
                            )}
                          </button>
                        </div>
                      </td>
                    </tr>
                  ))}
                </tbody>
              </table>
            </div>
          )}

          {/* 分页控件 */}
          {pagination.total > 0 && (
            <div className="bg-white px-4 py-3 flex items-center justify-between border-t border-gray-200 sm:px-6">
              <div className="flex-1 flex justify-between sm:hidden">
                <button
                  onClick={() => handlePageChange(pagination.page - 1)}
                  disabled={!pagination.has_prev}
                  className="relative inline-flex items-center px-4 py-2 border border-gray-300 text-sm font-medium rounded-md text-gray-700 bg-white hover:bg-gray-50 disabled:opacity-50 disabled:cursor-not-allowed"
                >
                  上一页
                </button>
                <button
                  onClick={() => handlePageChange(pagination.page + 1)}
                  disabled={!pagination.has_next}
                  className="ml-3 relative inline-flex items-center px-4 py-2 border border-gray-300 text-sm font-medium rounded-md text-gray-700 bg-white hover:bg-gray-50 disabled:opacity-50 disabled:cursor-not-allowed"
                >
                  下一页
                </button>
              </div>
              <div className="hidden sm:flex-1 sm:flex sm:items-center sm:justify-between">
                <div>
                  <p className="text-sm text-gray-700">
                    显示第 <span className="font-medium">{(pagination.page - 1) * pagination.limit + 1}</span> 到{' '}
                    <span className="font-medium">
                      {Math.min(pagination.page * pagination.limit, pagination.total)}
                    </span>{' '}
                    项，共 <span className="font-medium">{pagination.total}</span> 项
                  </p>
                </div>
                <div>
                  <nav className="relative z-0 inline-flex rounded-md shadow-sm -space-x-px" aria-label="Pagination">
                    <button
                      onClick={() => handlePageChange(pagination.page - 1)}
                      disabled={!pagination.has_prev}
                      className="relative inline-flex items-center px-2 py-2 rounded-l-md border border-gray-300 bg-white text-sm font-medium text-gray-500 hover:bg-gray-50 disabled:opacity-50 disabled:cursor-not-allowed"
                    >
                      <ChevronLeft className="h-5 w-5" />
                    </button>
                    
                    {/* 页码 */}
                    {Array.from({ length: Math.min(5, pagination.total_pages) }, (_, i) => {
                      let pageNum;
                      if (pagination.total_pages <= 5) {
                        pageNum = i + 1;
                      } else if (pagination.page <= 3) {
                        pageNum = i + 1;
                      } else if (pagination.page >= pagination.total_pages - 2) {
                        pageNum = pagination.total_pages - 4 + i;
                      } else {
                        pageNum = pagination.page - 2 + i;
                      }
                      
                      return (
                        <button
                          key={pageNum}
                          onClick={() => handlePageChange(pageNum)}
                          className={`relative inline-flex items-center px-4 py-2 border text-sm font-medium ${
                            pageNum === pagination.page
                              ? 'z-10 bg-blue-50 border-blue-500 text-blue-600'
                              : 'bg-white border-gray-300 text-gray-500 hover:bg-gray-50'
                          }`}
                        >
                          {pageNum}
                        </button>
                      );
                    })}
                    
                    <button
                      onClick={() => handlePageChange(pagination.page + 1)}
                      disabled={!pagination.has_next}
                      className="relative inline-flex items-center px-2 py-2 rounded-r-md border border-gray-300 bg-white text-sm font-medium text-gray-500 hover:bg-gray-50 disabled:opacity-50 disabled:cursor-not-allowed"
                    >
                      <ChevronRight className="h-5 w-5" />
                    </button>
                  </nav>
                </div>
              </div>
            </div>
          )}
        </div>
      </div>

      {/* 添加代理模态框 */}
      <Modal
        isOpen={showAddModal}
        onClose={() => setShowAddModal(false)}
        title="添加代理"
        size="lg"
      >
        <AddProxyForm
          onSuccess={() => {
            setShowAddModal(false)
            fetchProxies(false)
          }}
          onCancel={() => setShowAddModal(false)}
        />
      </Modal>

      {/* 批量添加代理模态框 */}
      <Modal
        isOpen={showBatchAddModal}
        onClose={() => setShowBatchAddModal(false)}
        title="批量添加代理"
        size="xl"
      >
        <BatchAddProxyForm
          onSuccess={() => {
            setShowBatchAddModal(false)
            fetchProxies(false)
          }}
          onCancel={() => setShowBatchAddModal(false)}
        />
      </Modal>
    </div>
  )
}

export default ProxyManagement
