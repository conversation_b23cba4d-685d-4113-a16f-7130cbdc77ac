请根据当前项目情况，阅读项目源码并完成以下目标。
在 @freeProxy/sources.json 文件有代理源信息，你可以阅读该文件。然后帮我实现以下目标：
1. 代理源包含了各种类型的代理，你应该对每个代理源发起请求，获取实际的代理，然后根据实际情况存储到某个文件中。
2. 有相应的检测逻辑，检测获取到的代理是否可用，延迟信息之类的。
3. 将检测到可用的代理添加到本项目的后端服务中。
4. 这个逻辑最好做成一个独立的模块，独立的文件夹之类的。
5. 这个服务应该不影响程序主流程运行，或许可以用一个goroutine。
6. 增加你认为应该增加的逻辑。

------------------------------------------------------------------------------------
文件 @b:\conan-work\proxyManager/internal\collector\verifier.go 有个验证代理是否有效的逻辑：verifyProxies 但是验证的代理数量可能超过10万条，如何才能做到更加高效的验证，并且能有合适的进度提示。最好还有个本地文件能简单记录上次验证的时间，如果没有超过24小时则不用重复验证。


------------------------------------------------------------------------------------
我觉得目前的方案可能稍微有点复杂了，我理解这个需求代码量不应该有这么多。
应该在不影响原有功能的前提增加新功能。请帮我精简代码，实现刚刚的需求。最后不需要测试文件，只需要通过go run cmd/main.go 来验证。


------------------------------------------------------------------------------------




------------------------------------------------------------------------------------



------------------------------------------------------------------------------------




------------------------------------------------------------------------------------



------------------------------------------------------------------------------------




------------------------------------------------------------------------------------



------------------------------------------------------------------------------------




------------------------------------------------------------------------------------


